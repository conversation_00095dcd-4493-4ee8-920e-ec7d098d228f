.highlyavailable{
    // hightLight
    &-hightLight-text{
        color:#46FFDE;
        font-weight: 600;
    }
    &-hightLight-text-27{
        color:#46FFDE;
        font-size:1.5rem;
        font-weight: 600;
    }

    // flex
    &-flex-row{
        display: flex;
        align-items: center;
    }
    &-flex-combination{
        align-items: center;
        justify-content: center;
    }

    // width
    &-w-full{
        width:100%;
    }

    // overflow;
    &-overflow{
        overflow-y: auto;
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            background: #ccc;
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            background: transparent;
        }
        &::-webkit-scrollbar-corner{
            background:rgba(0,0,0,0);
        }
    }
    // 文本溢出
    &-truncate-text {
        overflow: hidden; /* 确保溢出文本被截断 */
        white-space: nowrap; /* 保证文本在一行内显示，不换行 */
        text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
    }
    &-primary-btn{
        &.el-button--primary{
            background:@primary-color;
            color:@white;
        }
        &.el-button{
            border:0;
            border-radius: 4px;
            &:hover{
                opacity: 0.8;
            }
        }
    }
    &-plain-btn{
        &:hover{
            opacity: 0.8;
        }
    }
    &-dialog{
        .el-dialog{
            display:flex;
            flex-direction:column;
            margin:0 !important;
            position: absolute;
            top:50%;
            left:50%;
            transform: translate(-50%, -50%);
          }
    }
    &-returnIcon {
        width: 14px;
        height: 14px;
        display: inline-block;
        background-repeat: no-repeat;
        margin: 0 5px;
        background-image: url("../img/icon/return.png");
        vertical-align: text-bottom;
        margin-top: -3px;
    }
}