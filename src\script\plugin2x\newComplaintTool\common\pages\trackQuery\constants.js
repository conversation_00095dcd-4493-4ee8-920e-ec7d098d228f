const fields = [
    {
        prop: 'dataStime',
        label: '数据开始时间:',
        element: 'el-date-picker',
        bind: {
            placeholder: '请选择',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss'
        },
        span: 5
    },
    {
        prop: 'dataEtime',
        label: '数据结束时间:',
        element: 'el-date-picker',
        bind: {
            placeholder: '请选择',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss'
        },
        span: 5
    },
   /*  {
        prop: 'stayDuration',
        label: '驻留时长:',
        element: 'el-input-number',
        bind: {
            placeholder: '请输入',
            clearable: true,
            'controls-position': 'right',
            min: 0
        },
        span: 4
    }, */
    {
        span: 10
    }
];

const columns = [
    {
        prop: 'sourceTime',
        label: '信令时间'
    },
    {
        prop: 'lacCell',
        label: '基站标识'
    },
    {
        prop: 'cgiLat',
        label: '基站经度'
    },
    {
        prop: 'cgiLng',
        label: '基站纬度'
    },
    {
        prop: 'stay',
        label: '是否驻留点'
    }
];
export { fields, columns };
