export default (mapType) => {
    return {
        initialZoom: 15,
        initPointLat: 30.73722,
        initPointLng: 121.32674,
        city: {
            lat: 30.73722,
            lng: 121.32674,
            cityId: 4403,
            cityName: '深圳',
        },
        options: {
            antialias: true,
            mapLayer: {
                visible: true,
                mapType,
            },
            amapMapLayer: true,
            cameraControl: {
                type: '2D',
                minZoom: 4,
                maxZoom: 17,
                wheelLevelChange: true,
            },
            tool: {
                searchVisible: false,
                baiduSearch: false,
                buttonVisible: false,
                toolBoxVisible: false, //工具箱 不显示
                layerVisible: false,
                baiduReverseGeocoding: false,
                circleChoice: false,
                boxSelectLayer: true,
            },
            lineLayer: true, //线条
            panoramaLayer: {
                visible: false,
            },
            circleChoice: {//圈选
                circleColor: 0x4f2cde,
                circleOpacity: 0.5,
                circleFrame: true,
                circleFrameColor: 0x734CE3,
                circleShowRadius: true, // 该属性会导致辅助圆出现半径巨大的情况
                cirCleShowClose: true,
            },
            boxSelectLayer: true,
            lineEditLayer: true,
            LacCiNumberTimer: null,
            areaEditLayer: true,
        },
    };
};
