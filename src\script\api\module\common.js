import norMalConfig from '../normal-config';
const _url = '/spaceResource';
const _url2 = '/default-server';
// 通用模块
export default {
    commentApi: {
        getDistrict(params) {
            return norMalConfig(`${_url}/aoi/getDistrict`, params);
        },
        getLayers(params) {
            return norMalConfig(`${_url}/dimensionTable/getLayer`, params);
        },
        getAoi(params) {
            return norMalConfig(`${_url2}/task-service/ckSwitchOverImportant/searchRegion`, params);
        },
        getPriDataSources(params) {
            return norMalConfig(`${_url2}/task-service/ckSwitchOverSet/query`, params);
        }
    }
};
