import totalIcon from '@/img/icon/totalOrder.png';
import usedIcon from '@/img/icon/userOrder.png';
// 状态枚举
const fields = [
    {
        prop: 'timeType',
        label: '时间粒度:',
        labelWidth: '76px',
        element: 'el-select',
        bind: {
            placeholder: '请选择',
            clearable: true
        },
        slot: {
            element: 'el-option',
            enums: [
                { label: '天', value: 1 },
                { label: '周', value: 2 },
                { label: '月', value: 3 },
                { label: '季度', value: 4 },
                { label: '半年', value: 5 },
                { label: '年', value: 6 }
            ]
        },
        span: 3
    },
    {
        prop: 'listType',
        label: '投诉类型:',
        labelWidth: '76px',
        element: 'el-select',
        bind: {
            placeholder: '请选择',
            clearable: true
        },
        slot: {
            element: 'el-option',
            enums: [
                { label: '用户轨迹稽核', value: 1 },
                { label: '实时事件稽核', value: 2 }
            ]
        },
        span: 4
    },
    {
        prop: 'createUserId',
        label: '租户ID:',
        element: 'el-input',
        bind: {
            placeholder: '请输入',
            clearable: true
        },
        span: 4
    },
    {
        prop: 'startTime',
        label: '开始时间:',
        labelWidth: '76px',
        element: 'el-date-picker',
        bind: {
            placeholder: '请选择',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss'
        },
        span: 4
    },
    {
        prop: 'endTime',
        label: '结束时间:',
        labelWidth: '76px',
        element: 'el-date-picker',
        bind: {
            placeholder: '请选择',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss'
        },
        span: 4
    },
    {
        prop: 'status',
        label: '状态:',
        element: 'el-select',
        bind: {
            placeholder: '请选择',
            clearable: true
        },
        slot: {
            element: 'el-option',
            enums: [
                { label: '申请中', value: 0 },
                { label: '同意', value: 1 },
                { label: '驳回', value: 2 },
                { label: '禁止', value: 3 },
                { label: '数据准备中', value: 4 },
                { label: '数据准备完成', value: 5 },
                { label: '数据准备失败', value: 6 }
            ]
        },
        span: 3
    },
    {
        span: 2
    }
];

// 表格列配置
const tableColumns = [
    {
        prop: 'time',
        label: '日期'
    },
    {
        prop: 'userTrajAudAllOrder',
        label: '用户轨迹稽核-总工单'
    },
    {
        prop: 'userTrajAudUsedOrder',
        label: '用户轨迹稽核-使用工单'
    },
    {
        prop: 'realTimeEventAudAllOrder',
        label: '实时事件稽核-总工单'
    },
    {
        prop: 'realTimeEventAudUsedOrder',
        label: '实时事件稽核-使用工单'
    }
];
//
const creatOverviewList = (applyTotalCnt = 0, usedTotalCnt = 0) => {
    return [
        {
            count: applyTotalCnt,
            name: '申请工单总数',
            color: '#1a75ff',
            unit: '个',
            props: 'applyTotalCnt',
            icon: totalIcon,
            iconStyle: {
                height: '42px',
                width: '42px'
            }
        },
        {
            count: usedTotalCnt,
            name: '使用工单总数',
            color: '#0DC05C',
            unit: '个',
            props: 'usedTotalCnt',
            icon: usedIcon,
            iconStyle: {
                height: '42px',
                width: '42px'
            },
            cardStyle: {
                border: '1px solid #0DC05C',
                backgroundColor: '#EAFBF6'
            }
        }
    ];
};
//
const getChartData = ({
    xAxis = [],
    trackCnt = [],
    trackUseCnt = [],
    timeTotleCnt = [],
    timeUseCnt = []
}) => {
    return {
        isNull: !xAxis.length,
        type: 'pureCommon',
        xAxis,
        legend: {
            show: true
        },
        tooltip: {
            trigger: 'axis',
            formatter: function (params) {
                // 检查参数是否有效
                if (!params || !params.length) {
                    return '';
                }
                let str = `<div style="font-size:14px;font-weight:bold;padding:8px 0;color:#333;">${params[0].name}</div>`;
                params.forEach((item) => {
                    str += `
						<div style="background:rgba(255,255,255,0.9);box-shadow:6px 0px 20px 0px rgba(34,87,188,0.1);border-radius:4px;padding:6px 10px;margin-top:5px;">
							<div style="display:flex;align-items:center;">
								<span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${item.color};margin-right:5px;"></span>
								<span style="color:#333;">${item.seriesName} ${item.value}个</span>
							</div>
						</div>`;
                });
                return str;
            },
            extraCssText:
                'background:linear-gradient(322deg, #FDFEFF 0%, #F4F7FC 100%); border-radius:6px; border:1px solid; border-image:linear-gradient(337deg, rgba(255,255,255,1), rgba(255,255,255,0)) 1 1; backdrop-filter:blur(3.7px); padding:8px 12px; box-shadow:0 4px 12px rgba(0,0,0,0.05);',
            padding: 0,
            textStyle: {
                color: '#333'
            }
        },
        grid: {
            left: '3%',
            right: '3%',
            bottom: '0',
            top: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxis,
            axisLine: {
                lineStyle: {
                    color: '#b9babb'
                }
            },
            axisLabel: {
                color: '#86909C',
                margin: 12
            }
        },
        yAxis: {
            type: 'value',
            min: 0,
            axisLabel: {
                color: '#86909C'
            },
            splitLine: {
                lineStyle: {
                    color: '#E0E0E0',
                    type: 'dashed'
                }
            }
        },
        series: [
            {
                name: '用户轨迹稽核-总工单',
                type: 'line',
                data: trackCnt || [],
                smooth: false,
                showSymbol: true,
                itemStyle: {
                    color: '#1565FF'
                },
                lineStyle: {
                    width: 2,
                    color: '#1565FF'
                }
            },
            {
                name: '用户轨迹稽核-使用工单',
                type: 'line',
                data: trackUseCnt || [],
                smooth: false,
                showSymbol: true,
                itemStyle: {
                    color: '#0DC05C'
                },
                lineStyle: {
                    width: 2,
                    color: '#0DC05C'
                }
            },
            {
                name: '实时事件稽核-总工单',
                type: 'line',
                data: timeTotleCnt || [],
                smooth: false,
                showSymbol: true,
                itemStyle: {
                    color: '#FF7802'
                },
                lineStyle: {
                    width: 2,
                    color: '#FF7802'
                }
            },
            {
                name: '实时事件稽核-使用工单',
                type: 'line',
                data: timeUseCnt || [],
                smooth: false,
                showSymbol: true,
                itemStyle: {
                    color: '#F7BF1D'
                },
                lineStyle: {
                    width: 2,
                    color: '#F7BF1D'
                }
            }
        ]
    };
};
export { tableColumns, fields, getChartData, creatOverviewList };
