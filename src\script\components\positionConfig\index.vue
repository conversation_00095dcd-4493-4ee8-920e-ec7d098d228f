<!-- 地理条件配置组件 -->
<template>
    <div class="position-condition">
        <div class="position-condition__body">
            <search-bar
                class="areaSetting"
                :name="'区域设置'"
                :type="'select'"
                v-model="form.regionSet"
                :options="areaSettingOps[form.regionType]"
                :placeholder="'区域设置'"
                :clearable="true"
                @selectChange="handleRegionSet"
            ></search-bar>
            <el-form class="form-box" ref="form" :model="form" label-width="0px" size="small">
                <!-- 动态 -->
                <el-form-item label="" style="margin-bottom: 0px">
                    <Keep-alive>
                        <component
                            :key="selectLabel"
                            :selectLabel="selectLabel"
                            :is="curComp.name"
                            :regionType="curComp.type"
                            :panelLevels="curComp.panelLevels"
                            :regionList="curComp.regionList"
                            :sourceRegionList="curComp.sourceRegionList"
                            :targetRegionList="curComp.targetRegionList"
                        />
                    </Keep-alive>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import searchBar from './searchBar.vue';
import subAreaSelect from './subAreaSelect.vue';
import multiAreaSelect from './multiAreaSelect.vue';
import { compData, regionSetOps } from './static';
export default {
    name: 'position-condition',
    components: {
        subAreaSelect,
        multiAreaSelect,
        searchBar,
    },
    provide() {
        return {
            getTags: this.getTags,
            setRegionData: this.setRegionData,
        };
    },
    props: {},
    data() {
        return {
            compData: JSON.parse(JSON.stringify(compData)),
            form: {
                regionType: 1,
                regionSet: 1,
            },
            areaSettingOps: {
                1: regionSetOps,
                2: regionSetOps.filter((item) => ['省份', '地市', '区县'].includes(item.label)),
            },
            mapRegionSet: ['省份', '地市', '区县', '区域'],
            mapRegionType: ['区域'],
        };
    },
    computed: {
        regionTypeLabel() {
            return this.mapRegionType[this.form.regionType - 1];
        },
        regionSetLabel() {
            return this.mapRegionSet[this.form.regionSet - 1];
        },
        selectLabel() {
            if (!this.form.regionType || !this.form.regionSet) {
                return '区域-省份';
            }
            return `${this.regionTypeLabel}-${this.regionSetLabel}`;
        },
        curComp() {
            return this.compData[this.selectLabel];
        },
    },
    watch: {},
    created() {},
    methods: {
        getTags(tags) {
            this.curComp.conclusion = tags.join('，');
        },
        setRegionData(type, list) {
            this.compData[type].regionList = list;
        },
        handleRegionSet() {
            this.setRegionData(this.selectLabel, []);
        },
        getRegionData() {
            const regionIds = this.curComp.regionList.map((item) => item.regionId);
            const regionSet = this.form.regionSet;
            return {
                regionList: this.curComp.regionList,
                regionIds,
                type: {
                    1: 2,
                    2: 3,
                    3: 4,
                    4: 1,
                }[regionSet],
            };
        },
    },
};
</script>

<style lang="less" scoped>
.position-condition {
    height: 100%;
    width: 100%;
    .form-box {
        height: 100%;
        flex: 1;
        overflow: hidden;
    }
    /deep/ .el-select--small {
        width: 150px;
    }
    /deep/ .type-label {
        min-width: 80px;
    }
    &__body {
        height: 100%;
        width: 100%;
        overflow: hidden;
        .areaSetting {
            margin-bottom: 14px;
            width: 200px;
        }
        .el-form {
            .el-form-item {
                height: 100%;
                &__content {
                    height: 100%;
                }
            }
            /deep/ .el-form-item__label {
                font-weight: normal;
            }
        }
    }
}
</style>
