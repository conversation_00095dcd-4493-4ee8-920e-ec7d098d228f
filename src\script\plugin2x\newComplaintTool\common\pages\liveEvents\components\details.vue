<template>
    <transition name="fade">
        <div class="details" :style="{ width: isExpand ? '488px' : 0 }">
            <div class="list">
                <div class="tab">
                    <el-tabs class="tab-list" v-model="activeTab" @tab-click="handleClick">
                        <el-tab-pane label="全量轨迹" name="totalTrack"></el-tab-pane>
                        <el-tab-pane label="未进入详单" name="notInList"></el-tab-pane>
                        <el-tab-pane v-if="permissionTabs.includes('enterLeaveList')" label="进入离开详单" name="enterLeaveList"></el-tab-pane>
                        <el-tab-pane v-if="permissionTabs.includes('residentList')" label="驻留详单" name="residentList"></el-tab-pane>
                    </el-tabs>
                </div>
                <div class="table-list">
                    <dataTable
                        :columns="columns"
                        :data="tableData"
                        :pagination="pagination"
                        :total="total"
                        :updateTable="getBaseStations"
                        layout="total, prev, pager, next"
                        isHideUpLine
                        size="mini"
                        :isHidePagination="!isTotalTrack"
                    >
                    </dataTable>
                </div>
            </div>
            <div v-if="isShowOverview" class="statistics">
                <div class="head">统计数据</div>
                <overview
                    class="overview"
                    :resList="overviewList"
                    :span="24 / overviewList.length"
                    :styleType="2"
                    :gutter="12"
                />
            </div>
            <img class="toggle-icon" :src="toggleIcon" alt="" @click="isExpand = !isExpand" />
        </div>
    </transition>
</template>

<script>
import dataTable from '_com/tables/dataTableLast.vue';
import overview from '@/script/components/overView/overview.vue';
import arrowLeft from '@/img/icon/left.png';
import arrowRight from '@/img/icon/right.png';
import { columns} from '../constants.js';
export default {
    name: 'details',
    components: {
        dataTable,
        overview
    },
    props: {
        curRow: {
            type: Object,
            default: () => ({})
        },
        getBaseStations: {
            type: Function,
            default: () => {}
        },
        tableData: {
            type: Array,
            default: () => []
        },
        overviewList: {
            type: Array,
            default: () => []
        },
        total: {
            type: Number,
            default: 0
        },
        permissionTabs: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            activeTab: 'totalTrack',
            pagination: {
                curPage: 1,
                pageSize: 10,
                pagerCount: 5
            },
            isExpand: true
        };
    },
    computed: {
        columns() {
            return columns.filter(
                (item) => !item.showTabs || item.showTabs.includes(this.activeTab)
            );
        },
        isTotalTrack() {
            return this.activeTab === 'totalTrack';
        },
        isShowOverview() {
            return ['enterLeaveList', 'residentList'].includes(this.activeTab);
        },
        toggleIcon() {
            return this.isExpand ? arrowRight : arrowLeft;
        }
    },
    methods: {
        handleClick() {
            this.$emit('changeTab', this.activeTab);
        },
    }
};
</script>

<style lang="less" scoped>
.details {
    display: flex;
    flex-direction: column;
    position: absolute;
    right: 16px;
    top: 16px;
    width: 488px;
    height: calc(100% - 32px);
    border-radius: 4px;
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    background-color: #f6f8fa;
    z-index: 1000;
    .list {
        flex: 1;
        height: 0;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        .tab {
            .tab-list {
                height: 40px;
                /deep/ .el-tabs__nav {
                    padding-left: 16px;
                    .el-tabs__active-bar {
                        margin-left: 16px;
                    }
                }
            }
        }
        .table-list {
            flex: 1;
            height: 0;
            padding: 12px 16px;
        }
    }
    .statistics {
        margin-top: 6px;
        padding: 8px 16px 14px;
        background-color: #fff;
        .head {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            height: 32px;
            font-size: 16px;
            font-weight: bold;
            &::before {
                content: '';
                display: inline-block;
                margin-right: 6px;
                width: 3px;
                height: 13px;
                background: #0091ff;
            }
        }
        .overview {
            .el-col {
                margin-bottom: 0px;
                /deep/ .overview__main {
                    padding: 11px;
                    border: none;
                    background-color: #f6f8fa;
                    .name {
                        font-weight: 400;
                        font-size: 14px;
                        color: rgba(0, 0, 0, 0.85);
                    }
                    .count {
                        font-size: 18px;
                        .unit {
                            font-weight: 400;
                            font-size: 12px;
                            color: rgba(0, 0, 0, 0.65) !important;
                        }
                    }
                }
            }
        }
    }
    .toggle-icon {
        position: absolute;
        left: -16px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
    }
}

.fade-enter-active,
.fade-leave-active {
    transition: all 0.5s;
}
.fade-enter,
.fade-leave-to {
    opacity: 0;
    width: 0 !important;
}
</style>
