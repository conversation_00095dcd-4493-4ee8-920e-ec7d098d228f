import { setFitView } from '@/script/utils/method';
export default (g) => {
  let graphs = [];
  // 创建图层
  let layer = new g.layer();
  layer.name = 'plane';
  g.gis.scene.add(layer);
  g.meshList.plane.opacity = 0.4;
  layer.visible = true;

  class Plane {
    static getGraphs() {
      return graphs;
    }
    static draw(data = []) {
      if (!data || !data.length) return;
      const res = [];
      for (const plane of data) {
        const { points, color } = plane;
        const coors = points.map((point) => [point.lng, point.lat]);
        res.push({
          ls: g.math.lsRegular(coors) || [],
          ht: 0 * g.HM,
          layers: [{ maxH: 0.01, color }],
        });
      }
      Object.assign(res, {
        needFrame: true,
        frameWidth: 2,
        frameColor: 0xffffff
      });

      const mesh = g.meshList.plane.create(res);
      layer.add(mesh);

      setFitView(data[0].points, g, 1);

      g.gis.needUpdate = true;
    }
    static remove() {
      layer.removeAll();
    }
    static toMove(points) {
      setFitView(points, g, 1);
    }
  }

  return Plane;
};
