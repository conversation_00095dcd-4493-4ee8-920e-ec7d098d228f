const fields = [
    {
        prop: 'time',
        label: '创建时间：',
        labelWidth: '40',
        element: 'el-date-picker',
        bind: {
            clearable: true,
            type: 'datetimerange',
            'range-separator': '-',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            format: 'yyyy-MM-dd HH:00:00',
            'value-format': 'yyyyMMddHH',
        },
    },
    {}
];
const tableColumns = [
    {
        prop: 'time',
        label: '时间',
    },
    {
        prop: 's2Id',
        label: '栅格S2ID',
    },
    {
        prop: 'number',
        label: '人数',
    },
    {
        prop: 's2IdBase',
        label: '栅格S2ID基准值',
    },
    {
        prop: 'numberRate',
        label: '人数波动率(%)',
    },
];


export {
    fields,
    tableColumns,
};