<template>
    <div class="type-box m-r-20" :class="[`bar-${size}`]">
        <label v-if="slotLabel" class="label-border-right"> <slot :name="slotLabel"> </slot></label>
        <label v-else class="type-label m-r-6" :class="{ 'is-need': isNeed }"> {{ name }}</label>

        <template v-if="type === 'select'">
            <el-select
                :class="`common-size-${size}`"
                size="small"
                v-model="modelValue"
                :placeholder="placeholder"
                @change="selectChange"
                :clearable="clearable"
                :multiple="multiple"
                :collapse-tags="collapseTags"
                :disabled="disabled"
            >
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled || false"
                >
                </el-option>
            </el-select>
        </template>
        <template v-else-if="type === 'input'">
            <el-input
                size="small"
                v-model="modelValue"
                :disabled="disabled"
                :placeholder="placeholder"
                :clearable="clearable"
                v-trim
            ></el-input>
        </template>
        <template v-else-if="type === 'cascader'">
            <el-cascader
                size="small"
                :filterable="filterable"
                v-model="modelValue"
                :options="options"
                :clearable="clearable"
                @change="cascaderChange"
                :disabled="disabled"
            ></el-cascader>
        </template>
        <template v-else-if="type === 'date'">
            <el-date-picker
                size="small"
                v-model="modelValue"
                type="date"
                placeholder="选择日期"
                :format="format"
                :value-format="format"
            >
            </el-date-picker>
        </template>
        <template v-else-if="type === 'slot'">
            <slot></slot>
        </template>
        <component v-else :is="type" v-model="modelValue" v-bind="attrs" />
    </div>
</template>

<script>
export default {
    name: 'searchBar',
    props: {
        clearable: {
            type: Boolean,
            default: false,
        },
        name: {
            type: String,
            default: '',
        },
        type: {
            type: String,
            default: '',
        },
        slotLabel: {
            type: String,
            default: '',
        },
        value: {
            type: [String, Number, Array, Object],
        },
        options: {
            type: Array,
            default: () => [],
        },
        size: {
            type: String,
            default: 'small',
        },
        placeholder: {
            type: String,
            default: '请输入内容',
        },
        multiple: {
            type: Boolean,
            default: false,
        },
        collapseTags: {
            type: Boolean,
            default: true,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        isNeed: {
            type: Boolean,
            default: false,
        },
        filterable: {
            type: Boolean,
            default: false,
        },
        format: {
            type: String,
            default: 'yyyy-MM-dd',
        },
        attrs: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            modelValue: this.value,
        };
    },
    watch: {
        modelValue: {
            handler(newV) {
                this.$emit('input', newV);
            },
            deep: true,
        },
        value(val) {
            this.modelValue = val;
        },
    },
    methods: {
        selectChange(data) {
            this.$emit('selectChange', data, this.name);
        },
        cascaderChange(data) {
            this.$emit('cascaderChange', data);
        },
    },
};
</script>

<style lang="less" scoped>
.type-box {
    position: relative;
    display: flex;
    align-items: center;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    background: #fff;
    height: 35px;
}
.type-label {
    font-weight: normal;
    font-size: 14px;
    display: block;
    line-height: 14px;
    height: 14px;
    padding: 0 7px 0 10px;
    margin: 9px 0;
    border-right: 1px solid #ccc;
    color: #333;
}
.is-need:before {
    content: '*';
    color: red;
    // margin-top: 12px;
}
.common-size-mini {
    width: 150px;
}
.common-size-small {
    width: 200px;
}
.common-size-max {
    width: 280px;
}
/deep/ .el-input .el-input__inner {
    border: none;
    outline: none;
}
/deep/ .el-input {
    flex: 1;
}

/deep/ .el-date-editor .el-icon-date {
    display: none;
}

/deep/ .el-date-editor .el-input__inner {
    padding-left: 18px;
}
/deep/.el-range-editor.el-input__inner {
    border: none;
}
</style>
<style lang="less">
.el-cascader-menu__wrap {
    height: 240px;
}
</style>
