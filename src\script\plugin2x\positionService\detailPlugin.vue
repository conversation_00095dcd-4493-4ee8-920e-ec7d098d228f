<template>
	<div class="history-wrapper">
		<div class="top">
			<button
				class="btn-plain mt-btn__small"
				slot="btn"
				@click="handleReturn"
			>
				<em class="highlyavailable-returnIcon"></em>
				<span>返回</span>
			</button>
			<span class="text">详情</span>
		</div>
		<div class="content">
			<div class="history-wrapper-content">
				<detailData />
			</div>
		</div>
	</div>
</template>

<script>
import detailData from './components/detail/detailData.vue';
export default {
	name: 'history',
	components: {
		detailData,
	},
	data() {
		return {};
	},
	computed: {
	},
	methods: {
		handleReturn() {
			this.$router.back();
		},
	},
};
</script>

<style lang="less" scoped>
.history-wrapper {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.top {
		width: 100%;
		height: 60px;
		background: #fff;
		display: flex;
		align-items: center;
		padding: 14px 32px 14px 32px;
		box-shadow: inset 0px -1px 0px 0px #dcdcdc;
	}
	.content {
		width: 100%;
		height: calc(100% - 60px);
		background: #f4f6f8;
		padding: 16px 32px;
	}
	.btn-plain {
		background: #fff;
		border-radius: 4px;
		border: 1px solid #dcdcdc;
		display: flex;
		align-items: center;
		&:hover {
			opacity: 0.8;
		}
	}
	.text {
		font-size: 20px;
		color: #383838;
		line-height: 40px;
		font-weight: 600;
		padding-left: 10px;
	}
	&-content {
		width: 100%;
		height: 100%;
		background: #ffffff;
		box-shadow: 0px 0px 0.89rem 0px rgba(65, 67, 68, 0.15);
		border-radius: 0px 0.44rem 0.44rem 0.44rem;
	}
}
</style>
