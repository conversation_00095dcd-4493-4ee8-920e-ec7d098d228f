/*
 * @Date: 2025-02-11 11:34:01
 * @LastEditors: liurong <EMAIL>
 * @LastEditTime: 2025-06-17 18:52:31
 * @FilePath: \mtex-static-highlyavailable\src\script\enter\routerItems.js
 */
import { loadView } from './plugin';
export default [
    {
        path: 'highlyavailable/index',
        component: loadView('highlyavailable/index', false),
        children: [
            {
                path: 'home',
                name: 'home',
                component: loadView('highlyavailable/home', false)
            }, //首页
            {
                path: 'highDetail',
                name: 'highDetail',
                component: loadView('highlyavailable/detail', false, false)
            }, //详情
            {
                path: 'mapDetail',
                name: 'mapDetail',
                component: loadView('highlyavailable/mapDetail', false)
            }, //详情
            {
                path: 'history',
                name: 'history',
                component: loadView('highlyavailable/history', false)
            } //历史
        ]
    }, //高可用监控
    {
        path: 'positionService/index',
        component: loadView('positionService/index', false),
        children: [
            {
                path: 'home',
                name: 'home',
                component: loadView('positionService/home', false)
            }, //首页
            {
                path: 'detail',
                name: 'detail',
                component: loadView('positionService/detail', false)
            } //详情
        ]
    }, //位置服务应用
    {
        path: 'realTimeCompTool/index',
        component: loadView('realTimeCompTool/index', false),
        children: [
            {
                path: 'home',
                name: 'home',
                component: loadView('realTimeCompTool/home', false)
            }
        ]
    }, //realTimeComplaintTool
    {
        path: 'newComplaintTool/index',
        component: loadView('newComplaintTool/index', false),
        children: [
            {
                path: 'complaintBlacklist',
                name: 'complaintBlacklist',
                component: loadView('newComplaintTool/complaintBlacklist/index', false)
            },
            {
                path: 'dataQueryApply',
                name: 'dataQueryApply',
                component: loadView('newComplaintTool/dataQueryApply/index', false)
            },
            {
                path: 'dataQueryApproval',
                name: 'dataQueryApproval',
                component: loadView('newComplaintTool/dataQueryApproval/index', false)
            },
            {
                path: 'dataQueryStatistics',
                name: 'dataQueryStatistics',
                component: loadView('newComplaintTool/dataQueryStatistics/index', false)
            },
            {
                path: 'liveEvents',
                name: 'liveEvents',
                component: loadView('newComplaintTool/common/pages/liveEvents/index', false)
            },
            {
                path: 'trackQuery',
                name: 'trackQuery',
                component: loadView('newComplaintTool/common/pages/trackQuery/index', false)
            }
        ]
    } //实时投诉工具
];
