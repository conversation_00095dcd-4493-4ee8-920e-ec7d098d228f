<template>
    <div class="monitor">
        <searchBar :fields="fields" :form="form">
            <el-button class="highlyavailable-primary-btn" type="primary" size="small" @click="search()">查询</el-button>
        </searchBar>
        <dataTable class="dataTable" :columns="columns" :data="tableData" :pagination="pagination" :total="total" stripe
            :updateTable="getTableData">
            <template #primaryDatasource="{ row }">
                <span>{{ ckDataList[row.primaryDatasource] }}</span>
            </template>
            <template #isValid="{ row }">
                <span class="status-text" :class="`${['red', 'green'][row.isValid]}`">{{ statusList[row.isValid]
                }}</span>
            </template>
            <template #startTime="{ row }">
                <span v-if="row.status !== 3">{{ row.startTime }}</span>
                <span v-else></span>
            </template>
            <template #endTime="{ row }">
                <span v-if="row.status !== 3">{{ row.endTime }}</span>
                <span v-else></span>
            </template>
            <template #operation="{ row }">
                <div class="table-btn">
                    <div class="right-line right-margin">
                        <el-button class="table-btn-item" type="text" size="mini" @click="handleChange(row)"
                            :disabled="row.isValid === 0">
                            切换数据源
                        </el-button>
                    </div>

                    <div v-if="row.isValid === 1" class="right-margin">
                        <el-button class="table-btn-item redText" type="text" size="mini" @click="handleDel(row)">
                            注销
                        </el-button>
                    </div>
                    <div v-if="row.isValid === 0" class="right-margin">
                        <el-button class="table-btn-item redText" type="text" size="mini" @click="handleReuse(row)">
                            恢复
                        </el-button>
                    </div>
                </div>
            </template>
        </dataTable>
        <!-- 省份新增 -->
        <addProvince v-if="visibleRegion" :visible.sync="visibleRegion" @updateTable="search" :type="dialogType"
            :rowData="rowData" />
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import { getAreasToCity } from '@/script/utils/method.js';
import {
    fields,
    tableColumns,
    statusList,
    grayscaleTypeList,
    ckDataList
} from '@/script/constant/ckChange.js';
import dataTable from '_com/tables/dataTableLast.vue';
import addProvince from './addProvince.vue';
export default {
    name: 'monitor',
    components: {
        searchBar,
        dataTable,
        addProvince,
    },
    data() {
        return {
            form: {
                createTime: [],
                isValid: '',
                standbyDatasource: '',
                primaryDatasource: "",
                provinceCode: '',
                chName: '',
            },
            formData: {
                type: 'add'
            },
            columns: tableColumns,
            ckDataList,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15,
            },
            total: 0,
            statusList,
            grayscaleTypeList,
            visibleRegion: false,
            dialogType: 'add',
            rowData: {},
        };
    },
    computed: {
        fields() {
            const { highDistricts, highLayers } = this.$store.getters;
            return fields(
                {
                    chName: getAreasToCity(highDistricts, true),
                }
            );
        },
    },
    mounted() {
        this.search();
    },
    methods: {
        handleChange(row) {
            this.rowData = row;
            this.dialogType = 'change';
            this.visibleRegion = true;
        },
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            const { chName, provinceCode, primaryDatasource, standbyDatasource, isValid, createTime } = this.form;
            const [startTime, endTime] = createTime || [];
            this.highGetPost(
                'monitorApi',
                'ckChangeInfo',
                {
                    pageNum: curPage,
                    pageSize,
                    chName,
                    provinceCodeList: [provinceCode],
                    primaryDatasource,
                    standbyDatasource,
                    isValid,
                    createTimeFrom: startTime,
                    createTimeTo: endTime,
                },
                'CK主备库切换管理查询'
            ).then(({ data }) => {
                this.tableData = data.list;
                this.total = data.pageTotal;
            });
        },
        handleReuse(row) {
            let params = {
                provinceCodeList: [row.provinceCode],
                primaryDatasource: row.primaryDatasource,
                standbyDatasource: row.standbyDatasource,
                isValid: 1,
            };
            return this.highGetPost('monitorApi', 'ckChangeUpdate', params, 'CK主备库切换恢复').then(
                (res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.$message.success(res.returnMsg);
                        this.search();
                    } else {
                        this.$message.error(res.returnMsg);
                    }
                    return res;
                }
            );
        },
        addCk() {
            this.visibleRegion = true;
            this.dialogType = 'add';
            this.rowData = {};
        },
        handleDel(row) {
            let params = {
                provinceCodeList: [row.provinceCode],
                primaryDatasource: row.primaryDatasource,
                standbyDatasource: row.standbyDatasource,
                isValid: 0,
            };
            return this.highGetPost('monitorApi', 'ckChangeUpdate', params, 'CK主备库切换注销').then(
                (res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.$message.success(res.returnMsg);
                        this.search();
                    } else {
                        this.$message.error(res.returnMsg);
                    }
                    return res;
                }
            );
        },
    },
};
</script>

<style lang="less" scoped>
.monitor {
    width: 100%;
    height: 100%;
    padding: 1.33rem;
    display: flex;
    flex-direction: column;
}

.dataTable {
    width: 100%;
    flex: 1;
    height: 0;

    .status-text {
        color: #00a870;
        background: #e8f8f2;
        border-radius: 3px;
        border: 1px solid #00a870;
        font-size: 12px;
        padding: 3px;
    }

    .red {
        color: #e34d59;
        background: #fdecee;
        border: 1px solid #e34d59;
    }

    .green {
        color: #00a870;
        border: 1px solid #00a870;
    }
}

a {
    color: #1664ff;
    text-decoration: underline;
    cursor: pointer;
}

.table-btn {
    display: flex;

    .right-line {
        /deep/.el-button ::after {
            content: '';
            display: block;
            width: 1px;
            height: 12px;
            background-color: #ccc;
            position: absolute;
            top: 8px;
            right: 0;
            margin-right: -7px;
        }
    }

    .right-margin {
        margin-right: 12px;
    }

    &-item {
        position: relative;
        font-size: 14px;

        &.redText {
            color: #ff4d4f;
        }

        &::after {
            position: absolute;
            right: -13px;
            top: 8px;
            width: 1px;
            background: #ebeef5;
            height: 11px;
            content: '';
        }

        &:last-child {
            &::after {
                position: absolute;
                right: -13px;
                top: 8px;
                width: 1px;
                background: #ebeef5;
                height: 0px;
                content: '';
            }
        }
    }
}

.startTime {
    display: flex;
    align-items: center;

    .el-date-editor {
        width: 100%;
    }

    .el-checkbox {
        margin-bottom: 0;

        &__label {
            padding-left: 6px;
        }
    }

    /deep/.el-date-editor.el-input,
    .el-date-editor.el-input__inner {
        width: 208px;
    }

    .slash {
        margin: 0 8px;
    }
}

.warning {
    display: flex;
    align-items: center;
    padding-bottom: 10px;

    img {
        width: 16px;
        height: 16px;
    }

    &-text {
        font-size: 14px;
        color: #ff4d4f;
        line-height: 22px;
        padding-left: 10px;
    }
}
</style>
