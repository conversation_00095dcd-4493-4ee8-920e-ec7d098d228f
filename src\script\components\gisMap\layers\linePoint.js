import { setFitView } from '@/script/utils/method.js';
export default (g, options = { name: '线和点' }) => {
    // 创建图层
    let layer = new g.layer();
    layer.name = options.name;
    g.gis.scene.add(layer);
    layer.visible = true;
    let material = options.material;
    class linePoint {
        static removeAll() {
            layer.removeAll();
        }
        static createLinePoint(lineData = [], pointData = [], rectangleRange, hasFitView = 1) {
            linePoint.removeAll();
            // 绘制线
            lineData.autoScale = true;
            let lineMesh = g.meshList.line.create(lineData);
            pointData.autoScale = true;
            // 绘制点
            let imgMesh = g.meshList.img.create(pointData, material);
            if (rectangleRange) {
                const { lTopLng, lTopLat, rBtmLng, rBtmLat } = rectangleRange;
                // 绘制面
                const rectData = [
                    [lTopLng - 0.00006, lTopLat - 0.00006],
                    [lTopLng - 0.00006, rBtmLat + 0.00006],
                    [rBtmLng + 0.00006, rBtmLat + 0.00006],
                    [rBtmLng + 0.00006, lTopLat - 0.00006]
                ];
                let regularData = g.math.lsRegular(rectData);
                let data = [
                    { ls: regularData, ht: 0 * g.HM, layers: [{ maxH: 0, color: 0x409eff }] }
                ];
                g.meshList.plane.opacity = 0.4;
                let plane = g.meshList.plane.create(data);
                layer.add(plane);
            }

            layer.add(lineMesh);
            layer.add(imgMesh);

            g.gis.needUpdate = true;
            if (hasFitView) {
                pointData && setFitView(pointData, g, hasFitView);
            }
        }
    }
    return linePoint;
};
