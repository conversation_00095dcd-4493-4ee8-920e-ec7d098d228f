import enterIcon from '@/img/icon/enter.png';
import leaveIcon from '@/img/icon/leave.png';
import residentIcon from '@/img/icon/resident.png';

const fields = (areaOpts = []) => [
    {
        label: '区域选择',
        prop: 'regionId',
        element: 'el-select',
        bind: {
            placeholder: '请选择区域'
        },
        slot: {
            element: 'el-option',
            enums: areaOpts
        },
        span: 4
    },
    {
        prop: 'dataStime',
        label: '数据开始时间:',
        element: 'el-date-picker',
        bind: {
            placeholder: '请选择',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss'
        },
        span: 5
    },
    {
        prop: 'dataEtime',
        label: '数据结束时间:',
        element: 'el-date-picker',
        bind: {
            placeholder: '请选择',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss'
        },
        span: 5
    },
    {
        prop: 'stayDuration',
        label: '驻留时长:',
        element: 'el-input-number',
        bind: {
            placeholder: '请输入',
            clearable: true,
            'controls-position': 'right',
            min: 1
        },
        span: 4
    },
    {
        span: 6
    }
];

const trackFields = (areaOpts = []) => [
    {
        label: '区域选择',
        prop: 'regionId',
        element: 'el-select',
        bind: {
            placeholder: '请选择区域'
        },
        slot: {
            element: 'el-option',
            enums: areaOpts
        },
        span: 4
    },
    {
        prop: 'dataStime',
        label: '数据开始时间:',
        element: 'el-date-picker',
        bind: {
            placeholder: '请选择',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss'
        },
        span: 5
    },
    {
        prop: 'dataEtime',
        label: '数据结束时间:',
        element: 'el-date-picker',
        bind: {
            placeholder: '请选择',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss'
        },
        span: 5
    },
    {
        span: 6
    }
];

const columns = [
    {
        prop: 'sourceTime',
        label: '信令时间',
        showTabs: ['totalTrack', 'notInList', 'enterLeaveList']
    },
    {
        prop: 'stayId',
        label: '驻留点ID',
        showTabs: ['residentList']
    },
    {
        prop: 'lacCell',
        label: '基站标识'
    },
    {
        prop: 'cgiLat',
        label: '基站经度'
    },
    {
        prop: 'cgiLng',
        label: '基站纬度'
    },
    {
        prop: 'status',
        label: '动作类型',
        showTabs: ['enterLeaveList']
    },
    {
        prop: 'stayTime',
        label: '驻留时长',
        showTabs: ['residentList']
    }
];

const enterLeaveOverviewList = (enterCnt, outCnt) => [
    {
        count: enterCnt,
        name: '进入次数',
        color: '#1565FF',
        unit: '次',
        props: 'enterCnt',
        icon: enterIcon,
        iconStyle: {
            height: '36px',
            width: '36px'
        }
    },
    {
        count: outCnt,
        name: '离开次数',
        color: '#FF7802',
        unit: '次',
        props: 'outCnt',
        icon: leaveIcon,
        iconStyle: {
            height: '36px',
            width: '36px'
        }
    }
];
const residentOverviewList = (stayCnt) => [
    {
        count: stayCnt,
        name: '驻留时长',
        color: '#1565FF',
        unit: '分钟',
        props: 'stayTime',
        icon: residentIcon,
        iconStyle: {
            height: '36px',
            width: '36px'
        }
    }
];
export { fields, trackFields, columns, enterLeaveOverviewList, residentOverviewList };
