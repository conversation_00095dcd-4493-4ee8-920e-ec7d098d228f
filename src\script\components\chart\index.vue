<template>
    <div class="custom-chart">
        <div slot="title" class="title">
            <span class="text">{{ title }}</span>
            <span v-if="!isConsistent && tip" class="tip">
                <i v-if="tip" class="el-icon-info"></i>
                {{ tip }}
            </span>
            <div class="custom-slot">
                <slot name="tool"></slot>
            </div>
            <span class="detail" @click="handleDetail"
                >详情<i class="el-icon-arrow-right"></i
            ></span>
        </div>
        <div class="content">
            <div v-show="!isHasData" class="empty">暂无数据</div>
            <div v-show="isHasData" ref="customChartRef" class="chart" :style="{ height }"></div>
        </div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import { option } from './chart.js';
import _ from 'lodash';
export default {
    name: 'custom-chart',
    props: {
        type: {
            type: String,
            default: 'default',
        },
        title: {
            type: String,
            default: '',
        },
        tip: {
            type: String,
        },
        chartData: {
            type: Object,
            default: () => ({}),
        },
        height: {
            type: String,
            default: '250px',
        },
        preload: {
            type: Boolean,
            default: false,
        },
        isConsistent: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            isLoading: false,
        };
    },
    computed: {
        isHasData() {
            return Boolean(Object.keys(this.chartData || {}).length);
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart(this.chartData);
            this.$watch('chartData', (chartData) => {
                this.initChart(chartData);
            });
            window.addEventListener('resize', this.resize);
        });
    },
    beforeDestroy() {
        if (this.chart) {
            this.chart.dispose();
        }
    },
    activated() {
        this.resize();
    },
    destroyed() {
        window.removeEventListener('resize', this.resize);
    },
    methods: {
        initChart(chartData) {
            if (!this.isHasData) return;
            const chart = echarts.init(this.$refs.customChartRef);
            chart.clear();
            chart.setOption(option[this.type](chartData));
            if (this.preload && this.chart) {
                this.chart.off('datazoom');
            }
            this.chart = chart;
            this.isLoading = false;
            // 监听 datazoom 事件
            if (!this.preload) return;
            let timer = setTimeout(() => {
                this.chart.on(
                    'datazoom',
                    _.throttle(
                        (params) => {
                            console.log('params~108', params);
                            if (this.isLoading) return;
                            const { start, end } = params;
                            if (end >= 97) {
                                this.isLoading = true;
                                this.$emit('data-zoom-right', this.chart, () => {
                                    this.isLoading = false;
                                });
                            } else if (start <= 3) {
                                this.isLoading = true;
                                this.$emit('data-zoom-left', this.chart, () => {
                                    this.isLoading = false;
                                });
                            } else {
                                this.isLoading = false;
                            }
                        },
                        500,
                        { trailing: true }
                    )
                ); // 节流500ms
                clearTimeout(timer);
            }, 200);
        },
        resize() {
            if (this.chart) {
                this.chart.resize();
            }
        },
        handleDetail() {
            this.$emit('jump');
        },
    },
};
</script>

<style lang="less" scoped>
.custom-chart {
    background: #ffffff;
    border-radius: 8px;
    .title {
        padding: 0 12px;
        display: flex;
        align-items: center;
        height: 36px;
        .text {
            font-size: 14px;
            color: #2c2c2c;
        }
        .tip {
            margin-left: 8px;
            font-weight: 400;
            font-size: 12px;
            color: #ff4d4f;
            line-height: 12px;
            text-align: left;
            font-style: normal;
        }
        .custom-slot {
            margin-left: auto;
            margin-right: 9px;
        }
        .detail {
            // margin-left: auto;
            font-weight: 400;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            &:hover {
                color: #1a75ff;
                cursor: pointer;
            }
            .el-icon-arrow-right {
                margin-left: 1px;
            }
        }
    }
    .content {
        .chart {
            width: 100%;
        }
        .empty {
            display: flex;
            height: 180px;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.5);
            font-style: normal;
            font-family: PingFangSC, PingFang SC;
        }
    }
}
</style>
