<template>
    <div class="permissionApplication">
        <commonComp
            ref="commonComp"
            :fieldsList="fields"
            :formList="form"
            :tableColumns="tableColumns"
            :dialogFieldsList="dialogFields"
            :dialogFormList="dialogForm"
            @getTableData="getTableData"
            @handleDelete="handleDelete"
            @addOrEdit="addOrEdit"
            @restore="restore"
        ></commonComp>
    </div>
</template>

<script>
import commonComp from './common.vue';
import { fields, tableColumns, dialogFields } from '../constatnt/permissionApplication.js';
export default {
    name: 'permissionApplication',
    components: {
        commonComp
    },
    data() {
        return {
            fields: fields(),
            form: {
                queryMsisdn: '',
                regionId: '',
                status: 0
            },
            tableColumns,
            dialogFields: dialogFields(),
            dialogForm: {
                queryMsisdn: '',
                authStime: '',
                dataEtime: '',
                regionId: '',
                applicationReason: ''
            }
        };
    },
    created() {
        this.getWhitePhone();
    },
    methods: {
        getWhitePhone() {
            this.highGetPost('realTimeToolSeviceApi', 'getWhitePhone', {}, '获取白名单号码').then(
                ({ data }) => {
                    this.fields = fields({
                        msisdn: data
                    });
                    this.dialogFields = dialogFields({
                        msisdn: data
                    });
                }
            );
        },
        getTableData(pagination = {}, form, cb) {
            const { curPage = 1, pageSize = 15 } = pagination;
            this.highGetPost(
                'realTimeToolSeviceApi',
                'getPermissionList',
                {
                    queryType: 1,
                    ...form,
                    pageNum: curPage,
                    pageSize
                },
                '获取用户数据查询权限列表'
            ).then(({ data }) => {
                cb && cb(data);
            });
        },
        handleAdd() {
            this.$refs.commonComp.handleAdd();
        },
        handleDelete(row, cb) {
            this.highGetPost(
                'realTimeToolSeviceApi',
                'delPermission',
                {
                    id: row.id
                },
                '删除用户数据查询权限信息'
            ).then((res) => {
                cb && cb(res);
            });
        },
        addOrEdit(form, type, row, cb) {
            if (type === 'add') {
                this.highGetPost(
                    'realTimeToolSeviceApi',
                    'addPermission',
                    {
                        ...form
                    },
                    '新增用户数据查询权限'
                ).then((res) => {
                    cb && cb(res);
                });
            } else {
                this.highGetPost(
                    'realTimeToolSeviceApi',
                    'updatePermission',
                    {
                        id: row.id,
                        ...form
                    },
                    '修改权限信息'
                ).then((res) => {
                    cb && cb(res);
                });
            }
        },
        restore(row, { id }, cb) {
            this.highGetPost(
                'realTimeToolSeviceApi',
                'authUpdate',
                {
                    id,
                    ...row,
                    isValid: 1
                },
                '恢复投诉应用权限'
            ).then((res) => {
                cb && cb(res);
            });
        }
    }
};
</script>

<style lang="less" scoped>
.permissionApplication {
    width: 100%;
    height: 100%;
}
</style>
