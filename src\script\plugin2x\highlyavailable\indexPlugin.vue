<template>
    <div id="analysis-platform">
        <div class="main-content">
            <router-view class="page-route"></router-view>
        </div>
    </div>
</template>
<script>
import _ from 'lodash';
import { formatAreas } from '@/script/utils/method.js';
export default {
    components: {},
    data() {
        return {};
    },
    mounted() {
        this.onload();
    },
    methods: {
        jumpRouter(value) {
            this.openSubPath(value, '', {});
        },
        onload() {
            const origin = window.location.href;
            const lastItem = origin.substring(origin.lastIndexOf('/') + 1);
            const isSub = lastItem.split('-');
            if (isSub.length !== 1) {
                this.jumpRouter('home');
            }
            this.initBaseData();
        },
        getDistrict() {
            return this.highGetPost('commentApi', 'getDistrict', {}, '地市维表查询');
        },
        getLayers() {
            return this.highGetPost('commentApi', 'getLayers', {}, '图层类型查询');
        },
        initBaseData() {
            Promise.all([this.getDistrict(), this.getLayers()]).then((res) => {
                const [srcDistricts, srcLayers] = res;
                const originDistricts = srcDistricts.data.districtList;
                // 获取地市
                const districts = formatAreas(originDistricts);
                this.$store.commit('setHighDistricts', districts);
                this.$store.commit('setHighOriginDistricts', Object.freeze(originDistricts));
                const { tree, cityData, mapIds, mapIdToNames } = this.formatDistrict(
                    originDistricts,
                    true
                );
                this.$store.commit('setHighRegionTree', Object.freeze(tree));
                this.$store.commit('setHighRegionIds', Object.freeze(mapIds));
                this.$store.commit('setHighRegionNames', Object.freeze(mapIdToNames));
                this.$store.commit('setHighRegionData', Object.freeze(originDistricts));
                this.$store.commit('setHighCityData', Object.freeze(cityData));
                // 获取图层
                const layers = srcLayers.data.layerList.map((item) => {
                    return {
                        ...item,
                        label: item.layerName,
                        value: item.layerId,
                    };
                });
                this.$store.commit('setHighLayers', layers);
                this.$store.commit('setHighSceneTypes', [{ label: '全部', value: 0 }, ...layers]);
            });
            this.getPrimaryDatasources();
        },
        getPrimaryDatasources() {
            this.highGetPost(
                'commentApi',
                'getPriDataSources',
                {
                    pageNum: 1,
                    pageSize: 100,
                },
                '主库数据查询'
            ).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    const list = res.data.list || [];
                    this.$store.commit('setHightPriDataSources', list);
                } else {
                    this.$message.error(res.returnMsg);
                }
            });
        },
        // 区域格式化
        formatDistrict(srcData, isProvince) {
            if (isProvince) {
                srcData = _.orderBy(srcData, ['provinceCode', 'cityCode'], ['asc', 'asc']);
            }
            const tree = [];
            const cityData = [];
            let cityObj = {};
            let countyObj = {};
            const mapIds = {};
            const mapIdToNames = {};
            for (const {
                provinceName,
                provinceCode,
                cityName,
                cityCode,
                districtName,
                districtCode,
            } of srcData) {
                // id
                mapIds[provinceCode] = `${provinceCode}`;
                mapIds[cityCode] = `${provinceCode}/${cityCode}`;
                mapIds[districtCode] = `${provinceCode}/${cityCode}/${districtCode}`;
                //
                mapIdToNames[provinceCode] = provinceName;
                mapIdToNames[cityCode] = `${provinceName}/${cityName}`;
                mapIdToNames[districtCode] = `${provinceName}/${cityName}/${districtName}`;
                // 设置树形数据
                if (cityObj.label !== provinceName) {
                    cityObj = {
                        label: provinceName,
                        value: provinceCode,
                        children: [],
                    };
                    tree.push(cityObj);
                }
                // set city
                if (countyObj.label !== cityName) {
                    countyObj = {
                        label: cityName,
                        value: cityCode,
                        children: [],
                    };
                    cityObj.children.push(countyObj);
                    // 收集不同城市的数据
                    if (!['北京市', '天津市', '重庆市', '上海市'].includes(provinceName)) {
                        cityData.push({
                            provinceName,
                            provinceCode,
                            cityName,
                            cityCode,
                        });
                    }
                }
                // 过滤非直辖市
                if (['北京市', '天津市', '重庆市', '上海市'].includes(provinceName)) {
                    cityData.push({
                        provinceName,
                        provinceCode,
                        cityName: districtName,
                        cityCode: districtCode,
                    });
                }
                // set street
                countyObj.children.push({
                    label: districtName,
                    value: districtCode,
                });
            }
            return { tree, cityData, mapIds, mapIdToNames };
        },
    },
};
</script>
<style lang="less" scoped>
#analysis-platform {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    background: #fff;
}

.main-content {
    width: 100%;
    height: 100%;
}
</style>
