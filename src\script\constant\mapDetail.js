const realTimeMonitorFields = [
    { label: '区域名称: ', prop: 'regionName' },
    { label: '区域ID: ', prop: 'regionCode' },
    { label: '地市: ', prop: 'cityName' },
    { label: '图层类型: ', prop: 'classifyName' },
    { label: '开始时间: ', prop: 'startTime' },
    { label: '预设结束时间: ', prop: 'endTime' },
    { label: '所用库: ', prop: 'nowDatasource', format: (val) => `ck${val}` },
];

/**
 * @description 通用的图表数据格式化函数
 * @param {Array} primaryData - 主库数据
 * @param {Array} standData - 备库数据
 * @param {Object} options - 配置项
 * @param {string} options.valueKey - 需要展示的数值字段名
 * @param {string} options.timeKey - 时间字段名（可选）
 * @param {string} options.chartType - 图表类型：'line' 或 'bar'
 * @param {boolean} options.needDataZoom - 是否需要数据缩放功能
 * @returns {Object|undefined} 格式化后的图表配置
 */
const formatChartData = function (primaryData, standData, isDay = false, { valueKey, timeKey = 'timeSpan', chartType = 'line', needDataZoom = false }) {
    if ((!primaryData || !primaryData.length) && (!standData || !standData.length)) return;

    const xData = [];
    const data = [];
    const standbyData = [];

    primaryData.forEach((item, inx) => {
        const standbyItem = standData[inx] || {};
        // 处理x轴数据
        let key = item[timeKey];
        if (timeKey === 'timeSpan') {
            if (isDay) {
                key = new Date(key).format('yyyy-MM-dd');
            } else {
                key = new Date(key).format('HH:mm:ss');
            }
        }
        xData.push(key);
        // 处理y轴数据
        const mainVal = item[valueKey];
        const standbyVal = standbyItem[valueKey];
        data.push(mainVal);
        standbyData.push(standbyVal);
        if (this.isConsistent) {
            this.isConsistent = (mainVal) === standbyVal;
        }
    });

    return {
        xData,
        ...(needDataZoom && { dataZoom: true }),
        series: [
            {
                data,
                type: chartType,
                name: '主库群',
                itemStyle: {
                    color: '#1A75FF'
                }
            },
            {
                data: standbyData,
                type: chartType,
                name: '备用库群',
                itemStyle: {
                    color: '#2EDCAA'
                }
            }
        ]
    };
};

const chartList = [
    {
        title: '总人数',
        prop: 'totalNumber',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        format(primaryData, standData, isDay) {
            return formatChartData.bind(this)(primaryData, standData, isDay, {
                valueKey: 'flowTotalCnt',
                needDataZoom: true
            });
        }
    },
    {
        title: '人流-流入',
        prop: 'crowdIn',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        format(primaryData, standData, isDay) {
            return formatChartData.bind(this)(primaryData, standData, isDay, {
                valueKey: 'flowInCnt',
                needDataZoom: true
            });
        }
    },
    {
        title: '人流-流出',
        prop: 'crowdOut',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        format(primaryData, standData, isDay) {
            return formatChartData.bind(this)(primaryData, standData, isDay, {
                valueKey: 'flowOutCnt',
                needDataZoom: true
            });
        }
    },
    {
        title: '累计到访',
        prop: 'accumulatedVisit',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        format(primaryData, standData, isDay) {
            return formatChartData.bind(this)(primaryData, standData, isDay, {
                valueKey: 'arriveCnt',
                needDataZoom: true
            });
        }
    },
    {
        title: '常驻分布',
        prop: 'residentDistribute',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        format(primaryData, standData, isDay) {
            return formatChartData.bind(this)(primaryData, standData, isDay, {
                valueKey: 'residentCnt',
                needDataZoom: true
            });
        }
    },
    {
        title: '人群画像',
        prop: 'portrait',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        format(primaryData, standData) {
            return formatChartData.bind(this)(primaryData, standData, null, {
                valueKey: 'nub',
                timeKey: 'name',
                chartType: 'bar'
            });
        }
    },
    {
        title: '性别',
        prop: 'sex',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        format(primaryData, standData) {
            return formatChartData.bind(this)(primaryData, standData, null, {
                valueKey: 'nub',
                timeKey: 'name',
                chartType: 'bar'
            });
        }
    },
];

class ChartQueue {
    constructor(lQueue = [], rQueue = []) {
        this.lQueue = [...lQueue];
        this.rQueue = [...rQueue];
    }
    outLQueue() {
        return this.lQueue.pop();
    }
    outRQueue() {
        return this.rQueue.shift();
    }
    pushRQueue(...items) {
        this.rQueue.push(...items);
    }
}

const realTimeChartList = [
    {
        title: '总人数',
        prop: 'totalNumber',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        preload: true,
        format(primaryData, standData, isDay) {
            return formatChartData.bind(this)(primaryData, standData, isDay, {
                valueKey: 'flowTotalCnt',
                needDataZoom: true
            });
        }
    },
    {
        title: '人流-流入',
        prop: 'crowdIn',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        preload: true,
        format(primaryData, standData, isDay) {
            return formatChartData.bind(this)(primaryData, standData, isDay, {
                valueKey: 'flowInCnt',
                needDataZoom: true
            });
        }
    },
    {
        title: '人流-流出',
        prop: 'crowdOut',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        preload: true,
        format(primaryData, standData, isDay) {
            return formatChartData.bind(this)(primaryData, standData, isDay, {
                valueKey: 'flowOutCnt',
                needDataZoom: true
            });
        }
    },
    {
        title: '累计到访',
        prop: 'accumulatedVisit',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        preload: true,
        format(primaryData, standData, isDay) {
            return formatChartData.bind(this)(primaryData, standData, isDay, {
                valueKey: 'arriveCnt',
                needDataZoom: true
            });
        }
    },
    {
        title: '常驻分布',
        prop: 'residentDistribute',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        preload: true,
        format(primaryData, standData, isDay) {
            return formatChartData.bind(this)(primaryData, standData, isDay, {
                valueKey: 'residentCnt',
                needDataZoom: true
            });
        }
    },
    {
        title: '人群画像',
        prop: 'portrait',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        preload: true,
        format(primaryData, standData) {
            return formatChartData.bind(this)(primaryData, standData, null, {
                valueKey: 'nub',
                needDataZoom: true
            });
        }
    },
    {
        title: '性别',
        prop: 'sex',
        tip: '主/备库群数据不一致',
        isConsistent: true,
        chartData: {},
        preload: true,
        format(primaryData, standData) {
            return formatChartData.bind(this)(primaryData, standData, null, {
                valueKey: 'nub',
                needDataZoom: true
            });
        }
    },
];

export {
    realTimeMonitorFields,
    chartList,
    realTimeChartList,
    ChartQueue,
};