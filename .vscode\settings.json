{
    "files.exclude": {
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/CVS": true,
        "**/.DS_Store": true,
        "**/node_modules/*/**": true
    },
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode",

    // Prettier 配置
    "prettier.tabWidth": 4,
    "prettier.useTabs": false,
    "prettier.semi": true,
    "prettier.singleQuote": true,
    "prettier.printWidth": 100,
    "prettier.trailingComma": "none",
    "prettier.bracketSpacing": true,
    "prettier.endOfLine": "auto",

    // 为不同文件类型设置格式化工具
    "[vue]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[json]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[jsonc]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[less]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },

    // ESLint 配置
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "true"
    },
    "eslint.validate": ["javascript", "typescript", "html", "vue"],

    // 其他编辑器设置
    "editor.mouseWheelZoom": true,
    "editor.snippetSuggestions": "top",
    "editor.autoIndent": "advanced",

    // 移除不必要的配置
    "vetur.validation.template": false,
    "window.zoomLevel": -1,
    "workbench.colorTheme": "One Dark Pro"
}
