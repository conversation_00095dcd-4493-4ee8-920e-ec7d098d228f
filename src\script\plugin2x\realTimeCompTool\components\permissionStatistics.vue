<template>
    <div class="permissionStatistics">
        <div class="search">
            <searchBar :fields="fields" :form="form">
                <el-button
                    class="highlyavailable-primary-btn"
                    type="primary"
                    size="small"
                    @click="search()"
                    >查询</el-button
                >
            </searchBar>
        </div>
        <div class="content">
            <overview :resList="overviewList" :span="24 / overviewList.length" :styleType="2" />
            <div class="charts">
                <commonCharts ref="chart" :data="chartData" name="permissionStatisticsTrend">
                </commonCharts>
            </div>
            <dataTable
                class="dataTable"
                :columns="columns"
                :data="tableData"
                stripe
                :isHidePagination="true"
                :updateTable="getTableData"
            >
                <template #queryType="{ row }">
                    <span>{{ queryType[row.queryType] }}</span>
                </template>
            </dataTable>
        </div>
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import { fields, tableColumns } from '../constatnt/permissionStatistics.js';
import commonCharts from '_com/chart/commonCharts.vue';
import overview from '@/script/components/overView/overview.vue';
export default {
    name: 'permissionStatistics',
    components: {
        searchBar,
        dataTable,
        commonCharts,
        overview
    },
    data() {
        const date = new Date();
        date.setDate(date.getDate() - 1);
        return {
            fields: fields(),
            form: {
                startTime: new Date(date).format('YYYY-MM-DD 00:00:00'),
                endTime: new Date().format('YYYY-MM-DD 00:00:00'),
                queryType: 0
            },
            columns: tableColumns,
            tableData: [],
            total: 0,
            chartsData: {
                xAxis: [],
                totalCnt: [],
                timedCnt: [],
                unlinedCnt: []
            },
            overviewList: [
                {
                    count: 0,
                    name: '申请工单总数',
                    color: '#1a75ff',
                    unit: '个',
                    props: 'applyTotalCnt'
                },
                {
                    count: 0,
                    name: '使用工单总数',
                    color: '#1a75ff',
                    unit: '个',
                    props: 'usedTotalCnt'
                }
            ],
            queryType: {
                0: '实时',
                1: '离线'
            }
        };
    },
    computed: {
        chartData() {
            return {
                isNull: !this.chartsData.xAxis.length,
                type: 'pureCommon',
                xAxis: this.chartsData.xAxis,
                legend: {
                    show: true
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function (params) {
                        // 检查参数是否有效
                        if (!params || !params.length) {
                            return '';
                        }
                        let str = `<div style="font-size:14px;font-weight:bold;padding:8px 0;color:#333;">${params[0].name}</div>`;
                        params.forEach((item) => {
                            str += `
						<div style="background:rgba(255,255,255,0.9);box-shadow:6px 0px 20px 0px rgba(34,87,188,0.1);border-radius:4px;padding:6px 10px;margin-top:5px;">
							<div style="display:flex;align-items:center;">
								<span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${item.color};margin-right:5px;"></span>
								<span style="color:#333;">${item.seriesName} ${item.value}个</span>
							</div>
						</div>`;
                        });
                        return str;
                    },
                    extraCssText:
                        'background:linear-gradient(322deg, #FDFEFF 0%, #F4F7FC 100%); border-radius:6px; border:1px solid; border-image:linear-gradient(337deg, rgba(255,255,255,1), rgba(255,255,255,0)) 1 1; backdrop-filter:blur(3.7px); padding:8px 12px; box-shadow:0 4px 12px rgba(0,0,0,0.05);',
                    padding: 0,
                    textStyle: {
                        color: '#333'
                    }
                },
                grid: {
                    left: '3%',
                    right: '3%',
                    bottom: '0',
                    top: '10%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: this.chartsData.xAxis,
                    axisLine: {
                        lineStyle: {
                            color: '#b9babb'
                        }
                    },
                    axisLabel: {
                        color: '#86909C',
                        margin: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    axisLabel: {
                        color: '#86909C'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#E0E0E0',
                            type: 'dashed'
                        }
                    }
                },
                series: [
                    {
                        name: '总工单数',
                        type: 'line',
                        data: this.chartsData.totalCnt || [],
                        smooth: false,
                        showSymbol: true,
                        itemStyle: {
                            color: '#21CCFF'
                        },
                        lineStyle: {
                            width: 2,
                            color: '#21CCFF'
                        }
                    },
                    {
                        name: '实时工单数',
                        type: 'line',
                        data: this.chartsData.timedCnt || [],
                        smooth: false,
                        showSymbol: true,
                        itemStyle: {
                            color: '#8577D3'
                        },
                        lineStyle: {
                            width: 2,
                            color: '#8577D3'
                        }
                    },
                    {
                        name: '离线工单数',
                        type: 'line',
                        data: this.chartsData.unlinedCnt || [],
                        smooth: false,
                        showSymbol: true,
                        itemStyle: {
                            color: '#D377C0'
                        },
                        lineStyle: {
                            width: 2,
                            color: '#D377C0'
                        }
                    }
                ]
            };
        }
    },
    mounted() {
        this.search();
    },
    methods: {
        search() {
            this.getTableData();
        },
        getTableData() {
            this.highGetPost(
                'realTimeToolSeviceApi',
                'queryUserPermission',
                {
                    ...this.form
                },
                '用户数据查询权限统计查询'
            ).then(({ data }) => {
                const { trendList, list } = data;
                this.tableData = list;
                this.overviewList.forEach((item) => {
                    item.count = data[item.props];
                });
                if (trendList.length) {
                    const chartsData = {
                        xAxis: trendList.map((item) => item.time),
                        totalCnt: trendList.map((item) => item.totalCnt),
                        timedCnt: trendList.map((item) => item.timedCnt),
                        unlinedCnt: trendList.map((item) => item.unlinedCnt)
                    };
                    this.chartsData = chartsData;
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
.permissionStatistics {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .search {
        width: 100%;
        height: 44px;
        padding: 1.33rem;
    }
    .content {
        width: 100%;
        flex: 1;
        overflow-y: auto;
        padding: 1.33rem;
    }
}
.charts {
    width: 100%;
    height: 300px;
    margin-bottom: 10px;
}
.dataTable {
    width: 100%;
    height: 500px;
}
</style>
