<template>
    <div class="base-info">
        <div v-for="(item, index) in infoItems" :key="index" class="info-item">
            <span class="title">{{ item.label }}:</span>
            <span class="value">{{ item.value || item.defaultValue || '--' }}</span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'BaseInfo',
    props: {
        infoItems: {
            type: Array,
            default: () => []
        }
    }
};
</script>

<style lang="less" scoped>
.base-info {
    display: flex;
    align-items: center;
    padding: 0 24px;
    height: 40px;
    border-radius: 4px;
    flex-wrap: wrap;
    background-color: #f2f6ff;

    .info-item {
        margin-right: 14px;
        height: 100%;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;

        .title {
            margin-right: 8px;
        }
    }
}
</style>
