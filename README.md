# mtex-static-highlyAvailable

高可用监控

## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

```
cd existing_repo
git remote add origin http://192.168.1.41/rams-webpc/plugin/positional-abilities/mtex-static-highlyavailable.git
git branch -M main
git push -uf origin main
```

## Usage

-   nginx 配置

location /spaceResource/ {
proxy_pass http://192.168.1.143:19380/;
}
location /task-service/ {
proxy_pass http://192.168.1.143:19010/task-service/;
}
location /origin-service/ {
proxy_pass http://192.168.1.143:19010/region-service/;
}
location /mtex/static_dist/highlyavailablePlugin/ {
proxy_pass http://127.0.0.1:8888/mtex/static_dist/highlyavailablePlugin/;
}
