<template>
    <div class="data-statistics">
        <div class="search-form">
            <searchBar :form="form" :fields="fields">
                <el-button type="primary" @click="search">查询</el-button>
            </searchBar>
        </div>
        <div class="order-overview">
            <div class="overview">
                <overview
                    class="overview"
                    :resList="overviewList"
                    :span="24 / overviewList.length"
                    :styleType="2"
                />
            </div>
            <commonCharts class="chart" :data="chartData" />
        </div>
        <dataTable
            class="data-table"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :total="total"
            :updateTable="getTableData"
            size="mini"
            isHideUpLine
        />
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import commonCharts from '_com/chart/commonCharts.vue';
import overview from '@/script/components/overView/overview.vue';
import { fields, tableColumns, getChartData, creatOverviewList } from './constants';
export default {
    name: 'dataStatistics',
    components: {
        searchBar,
        dataTable,
        commonCharts,
        overview
    },
    data() {
        const date = new Date();
        date.setMonth(date.getMonth() - 3);
        return {
            overviewList: creatOverviewList(),
            form: {
                timeType: 1,
                listType: '',
                createUserId: '',
                // startTime: new Date(date).format('YYYY-MM-DD 00:00:00'),
                // endTime: new Date().format('YYYY-MM-DD 00:00:00'),
                startTime: '2024-01-01 16:37:30',
                endTime: '2025-10-02 16:37:30',
                status: 0
            },
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 10
            },
            total: 0,
            chartData: {}
        };
    },
    computed: {
        fields() {
            return fields;
        },
        columns() {
            return tableColumns;
        }
    },
    created() {
        this.search();
    },
    methods: {
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        async getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 10 } = pagination;
            this.highGetPost(
                'newComplaintToolApi',
                'getStatisticsData',
                {
                    ...this.form,
                    pageNum: curPage,
                    pageSize
                },
                '数据统计表格'
            ).then(({ data }) => {
                this.overviewList = creatOverviewList(data.allCount || 0, data.usedCount || 0);
                this.tableData = data.dataList;
                this.total = data.totalSize;
                this.getChart();
            });
        },
        getChart() {
            const xAxis = [];
            const trackCnt = [];
            const trackUseCnt = [];
            const timeTotleCnt = [];
            const timeUseCnt = [];
            this.tableData.forEach((item) => {
                xAxis.push(item.time);
                trackCnt.push(item.userTrajAudAllOrder || 0);
                trackUseCnt.push(item.userTrajAudUsedOrder || 0);
                timeTotleCnt.push(item.realTimeEventAudAllOrder || 0);
                timeUseCnt.push(item.realTimeEventAudUsedOrder || 0);
            });
            this.chartData = getChartData({
                xAxis,
                trackCnt,
                trackUseCnt,
                timeTotleCnt,
                timeUseCnt
            });
        }
    }
};
</script>

<style lang="less" scoped>
.data-statistics {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 16px;
    .search-form {
        padding: 16px 10px 4px;
        background-color: #fff;
        border-radius: 4px;
    }
    .order-overview {
        margin-top: 16px;
        padding: 16px;
        height: 350px;
        background-color: #fff;
        .overview {
            .el-col {
                margin-bottom: 8px;
            }
        }
        .chart {
            height: calc(100% - 72px - 8px);
        }
    }
    .data-table {
        margin-top: 6px;
        padding: 16px;
        flex: 1;
        height: 0;
        background-color: #fff;
        .divider {
            color: #d6dae0;
        }
    }
}
</style>
