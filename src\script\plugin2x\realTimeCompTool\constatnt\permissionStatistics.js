const fields = (Opts = { msisdn: [] }) => {
    return [
        {
            prop: 'startTime',
            label: '开始时间：',
            labelWidth: '70',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss'
            },
            span: 4
        },
        {
            prop: 'endTime',
            label: '结束时间：',
            labelWidth: '70',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss'
            },
            span: 4
        },
        {
            prop: 'queryType',
            label: '状态：',
            labelWidth: '70',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '实时', value: 0 },
                    { label: '离线', value: 1 }
                ]
            },
            span: 3
        },
        {
            span: 2
        }
    ];
};
const tableColumns = [
    {
        prop: 'time',
        label: '日期'
    },
    {
        prop: 'totalCnt',
        label: '总工单数'
    },
    {
        prop: 'queryType',
        label: '类型'
    },
    {
        prop: 'cnt',
        label: '工单数'
    }
];

export { fields, tableColumns };
