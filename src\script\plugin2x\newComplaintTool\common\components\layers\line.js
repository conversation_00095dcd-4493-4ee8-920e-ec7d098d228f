import { setFitView } from '@/script/utils/method';
export default (g) => {
  // 创建图层
  let layer = new g.layer();
  layer.name = 'line';
  g.gis.scene.add(layer);
  layer.visible = true;

  class Line {
    static getLayer() {
      return layer;
    }
    /**
     * @param {Array<{
     *   points: Array<{lat: number, lng: number, ht: number}>,
     *   color: number
     * }>} lineData - Line data array
     */
    static draw(lineData = []) {
      if (!lineData || !lineData.length) return;
      Line.remove();
      
      // 绘制线
      lineData.autoScale = true;
      const lineMesh = g.meshList.line.create(lineData);
      layer.add(lineMesh);

      g.gis.needUpdate = true;
    }
    static remove() {
      layer.removeAll();
    }
    static toMove(points, hasFitView = 1) {
      if (hasFitView) {
        setFitView(points, g, hasFitView);
      }
    }
  }

  return Line;
};
