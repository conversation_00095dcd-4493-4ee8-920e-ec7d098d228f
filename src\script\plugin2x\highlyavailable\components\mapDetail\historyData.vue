<template>
    <div class="history-data">
        <div class="history-data__top">
            <div v-for="item in fields" class="field" :key="item.label">
                <span class="name">{{ item.label }}</span>
                <span class="value">{{
                    item.format ? item.format(detail[item.prop]) : detail[item.prop]
                }}</span>
            </div>
        </div>
        <div class="history-data__body">
            <gisMap @loaded="gisLoaded" />
            <div class="control-panel">
                <el-checkbox class="custom-checkbox" v-model="isCheckHot">总人数热力图</el-checkbox>
                <TimeGranularity
                    v-model="timeConfig"
                    :finalDate="finalDate"
                    @change="handleTimeConfigChange"
                />
            </div>
            <slideDialog>
                <chart
                    class="chart"
                    v-for="chart in chartList"
                    :key="chart.title"
                    v-bind="chart"
                    @jump="jumpDetail"
                />
            </slideDialog>

            <!-- 修改：允许天粒度也可以使用时间轴 -->
            <time-line
                v-if="isCheckHot"
                class="timeline"
                :key="`${startTimeForTimeline}-${endTimeForTimeline}`"
                v-model="curTime"
                :startTime="startTimeForTimeline"
                :endTime="endTimeForTimeline"
                :timeSize="localGranularity"
                @change="handleTimeChange"
            ></time-line>

            <!-- 热力图图例 -->
            <customLegend v-if="isCheckHot && legendList.length" :list="legendList" />
        </div>
    </div>
</template>

<script>
import gisMap from '@/script/components/gisMap/index.vue';
import slideDialog from '@/script/components/slideDialog.vue';
import chart from '@/script/components/chart/index.vue';
import initGrid from '@/script/components/gisMap/layers/Grid';
import { realTimeMonitorFields, chartList } from '@/script/constant/mapDetail';
import TimeGranularity from './timeGranularity.vue';
import dayjs from 'dayjs';
import TimeLine from '@/script/components/timeline.vue';
import {
    getLegendRanges,
    roundToNearestFiveMinutes,
    generateTimeSlices,
} from '@/script/utils/method';
import { mapDetailMixin } from './mixin';

/**
 * 历史数据组件
 * @component history-data
 */
export default {
    name: 'history-data',
    mixins: [mapDetailMixin],
    components: {
        gisMap,
        slideDialog,
        chart,
        TimeGranularity,
        TimeLine,
        customLegend: () => import('@/script/components/custom-legend.vue'),
    },
    data() {
        return {
            chartList,
            fields: realTimeMonitorFields,
            detail: {},
            isCheckHot: false,
            grids: [],

            // 时间相关
            localGranularity: 4, // 默认天粒度
            curTime: '',
            startTimeForTimeline: '',
            endTimeForTimeline: '',
            finalDate: '',

            // 热力图相关
            libraryType: 'primary', // 默认主库群
            libraryList: [],
            numbers: [],
            range: {},

            // 时间配置
            timeConfig: {
                granularity: 4,
                timeRange: this.getDefaultTimeRange(),
            },
        };
    },
    computed: {
        queryRoute() {
            return this.$route.query;
        },
        row() {
            return this.queryRoute;
        },
        legendList() {
            return Object.keys(this.range).map((key) => {
                return {
                    color: this.range[key],
                    text: key,
                };
            });
        },
        isDay() {
            return this.timeConfig.granularity === 4;
        },
    },
    watch: {
        isCheckHot(val) {
            if (val) {
                this.getCrowdHot();
            } else {
                this.Grid.removeAll();
            }
        },
    },
    created() {
        this.initTimeConfig();
    },
    mounted() {
        this.detail = this.row;
    },
    beforeDestroy() {
        this.Grid && this.Grid.destroy();
    },
    methods: {
        /**
         * 初始化时间配置
         */
        initTimeConfig() {
            const { startTime, endTime } = this.row || {};
            if (startTime && endTime) {
                // 初始化时间配置
                const formattedEndTime = dayjs().format('YYYY-MM-DD 00:00:00');
                const formattedStartTime = dayjs(formattedEndTime)
                    .subtract(7, 'day')
                    .format('YYYY-MM-DD 00:00:00');

                this.timeConfig = {
                    granularity: 4, // 默认天粒度
                    timeRange: [formattedStartTime, formattedEndTime],
                };

                // 初始化时间轴时间
                this.startTimeForTimeline = formattedStartTime;
                this.endTimeForTimeline = formattedEndTime;
                this.curTime = this.endTimeForTimeline;
                this.localGranularity = 4;
                this.finalDate = formattedEndTime;
            } else {
                // 如果没有startTime和endTime，则使用当前时间
                const today = dayjs().format('YYYY-MM-DD 00:00:00');
                this.timeConfig = {
                    granularity: 4,
                    timeRange: [today, today],
                };

                // 初始化时间轴时间
                this.startTimeForTimeline = dayjs().format('YYYY-MM-DD 00:00:00');
                this.endTimeForTimeline = dayjs().format('YYYY-MM-DD 00:00:00');
                this.curTime = this.endTimeForTimeline;
                this.finalDate = this.endTimeForTimeline;
            }
            this.$watch('timeConfig.timeRange', (range) => {
                const [startTime, endTime] = range;
                const timeSize = this.timeConfig.granularity;
                if (startTime && endTime) {
                    if (timeSize === 4) {
                        this.startTimeForTimeline = dayjs(startTime).format('YYYY-MM-DD 00:00:00');
                        this.endTimeForTimeline = dayjs(endTime).format('YYYY-MM-DD 00:00:00');
                    } else if (timeSize === 1) {
                        this.startTimeForTimeline = roundToNearestFiveMinutes(startTime, 'down');
                        this.endTimeForTimeline = roundToNearestFiveMinutes(endTime, 'up');
                    } else {
                        this.startTimeForTimeline = startTime;
                        this.endTimeForTimeline = endTime;
                    }
                    this.curTime = this.endTimeForTimeline;
                    this.getCrowdHot();
                }
            });
        },

        /**
         * 获取默认时间范围
         * @returns {Array} 默认时间范围
         */
        getDefaultTimeRange() {
            const today = dayjs().format('YYYY-MM-DD 00:00:00');
            const sevenDaysAgo = dayjs(today).subtract(7, 'day').format('YYYY-MM-DD 00:00:00');
            return [sevenDaysAgo, today];
        },

        /**
         * 地图加载完成回调
         * @param {Object} g - 地图实例
         */
        gisLoaded(g) {
            this.Grid = initGrid(g, {
                click: this.handleGridClick,
            });
            this.getChartData();
            this.getOutline();
        },
        /**
         * 获取热力图颜色
         * @param {number} score - 分数值
         * @param {Object} range - 范围配置
         * @returns {string} 颜色值
         */
        getHeatColor(score, range) {
            const matchedKey = Object.keys(range).find((key) => {
                const [min, max] = key.split('-').map(Number);
                return score >= min && score < max;
            });
            return matchedKey ? parseInt(range[matchedKey].replace('#', '0x')) : '';
        },

        /**
         * 处理时间配置变更
         */
        handleTimeConfigChange() {
            this.localGranularity = this.timeConfig.granularity;
            this.getChartData();
        },

        /**
         * 处理时间轴变更
         */
        handleTimeChange() {
            this.getCrowdHot();
        },

        /**
         * 获取热力图数据
         */
        getCrowdHot() {
            if (!this.isCheckHot) {
                return;
            }
            if (![4, 1].includes(+this.row.regionType)) {
                this.$message.warning('热力模型只支持区县和自定义区域');
                return;
            }

            this.getData(
                {
                    modelList: {
                        modelId: '2',
                        modelParam: {
                            s2Level: appConfig.highS2Level || '17',
                        },
                    },
                    startTime: this.curTime,
                    endTime: this.curTime,
                },
                '热力查询'
            ).then((res) => {
                if (res) {
                    const { primaryResult, standbyResult } = res;
                    const primaryData = primaryResult[0] && primaryResult[0].regionData;
                    const standData = standbyResult[0] && standbyResult[0].regionData;
                    this.libraryList = (primaryData || []).map((item, inx) => {
                        const standItem = (standData || [])[inx] || {};
                        return {
                            ...item,
                            primary: item.total,
                            standby: standItem.total || 0,
                        };
                    });
                    this.formatLibraryData();
                } else {
                    this.Grid.createGrids([]);
                }
            });
        },

        /**
         * 格式化库数据
         */
        formatLibraryData() {
            this.numbers = this.libraryList.map((item) => item[this.libraryType]);
            this.range = getLegendRanges(Math.max(...this.numbers));
            const formattedData = this.libraryList.map((item) => {
                return {
                    centerPoint: {
                        lng: item.longitude,
                        lat: item.latitude,
                    },
                    points: item.vertexes.map((it) => ({
                        lng: it.longitude,
                        lat: it.latitude,
                    })),
                    config: {
                        color: this.getHeatColor(item[this.libraryType], this.range),
                        name: '人数',
                        score: item.count,
                    },
                    data: item,
                };
            });
            this.Grid.createGrids(formattedData, this.setText);
        },
        /**
         * 获取图表数据
         * 重构：根据不同粒度处理时间切片
         */
        getChartData() {
            let startTime, endTime;

            if (this.timeConfig.timeRange && this.timeConfig.timeRange.length === 2) {
                if (this.timeConfig.granularity === 4) {
                    // 天粒度
                    startTime = dayjs(this.timeConfig.timeRange[0]).format('YYYY-MM-DD 00:00:00');
                    endTime = dayjs(this.timeConfig.timeRange[1]).format('YYYY-MM-DD 00:00:00');
                } else {
                    startTime = this.timeConfig.timeRange[0];
                    endTime = this.timeConfig.timeRange[1];
                }
            } else {
                const today = dayjs().format('YYYY-MM-DD 00:00:00');
                startTime = today;
                endTime = today;
            }

            this.getPortrait(startTime, endTime);
            this.getPermanent(startTime, endTime);
        },

        /**
         * 获取人群画像数据
         * 修改：支持时间切片
         */
        async getPortrait(startTime, endTime) {
            try {
                let promises = [];
                const granularity = this.timeConfig.granularity;

                if (granularity === 4) {
                    // 天粒度不需要切片
                    promises = [
                        this.getData(
                            {
                                modelList: {
                                    modelId: '3',
                                    modelParam: {
                                        sex: ['男', '女'],
                                        age: ['18-24', '25-34', '35-44', '45-54', '55岁以上'],
                                    },
                                },
                                startTime,
                                endTime,
                            },
                            '画像查询'
                        ),
                    ];
                } else {
                    // 小时粒度(3)用24小时切片，分钟粒度(1)用2小时切片
                    const interval = granularity === 3 ? 24 : 2;
                    const timeSlices = generateTimeSlices(startTime, endTime, interval);

                    promises = timeSlices.map(([sliceStart, sliceEnd]) =>
                        this.getData(
                            {
                                modelList: {
                                    modelId: '3',
                                    modelParam: {
                                        sex: ['男', '女'],
                                        age: ['18-24', '25-34', '35-44', '45-54', '55岁以上'],
                                    },
                                },
                                startTime: sliceStart,
                                endTime: sliceEnd,
                            },
                            '画像查询'
                        )
                    );
                }

                const results = await Promise.all(promises);
                const validResults = results.filter((res) => res !== null);

                if (validResults.length > 0) {
                    // 如果是天粒度，直接使用第一个结果
                    if (granularity === 4) {
                        const { primaryResult, standbyResult } = validResults[0];
                        const { sex, age } = primaryResult;
                        const { sex: standbySex, age: standbyAge } = standbyResult;
                        const [, , , , , portraitChart, sexChart] = this.chartList;
                        portraitChart.chartData = portraitChart.format(age, standbyAge);
                        sexChart.chartData = sexChart.format(sex, standbySex);
                    } else {
                        // 其他粒度需要合并数据
                        const mergedData = this.mergePortraitData(validResults);
                        const { sex, age } = mergedData.primaryResult;
                        const { sex: standbySex, age: standbyAge } = mergedData.standbyResult;
                        const [, , , , , portraitChart, sexChart] = this.chartList;
                        portraitChart.chartData = portraitChart.format(age, standbyAge);
                        sexChart.chartData = sexChart.format(sex, standbySex);
                    }
                }
            } catch (error) {
                console.error('获取画像数据失败:', error);
                this.$message.error('获取画像数据失败');
            }
        },

        /**
         * 获取主要图表数据
         * 修改：支持时间切片
         */
        async getPermanent(startTime, endTime) {
            try {
                let promises = [];
                const granularity = this.timeConfig.granularity;

                if (granularity === 4) {
                    // 天粒度不需要切片
                    promises = [
                        this.getData(
                            {
                                startTime,
                                endTime,
                                modelList: {
                                    modelId: 7,
                                    modelParam: {
                                        level: 2,
                                        residentType: 1,
                                        top: 100,
                                    },
                                },
                                paramData: {
                                    minVisitNum: 1,
                                    maxVisitNum: 5,
                                },
                            },
                            '主要图表数据查询'
                        ),
                    ];
                } else {
                    // 小时粒度(3)用24小时切片，分钟粒度(1)用2小时切片
                    const interval = granularity === 3 ? 24 : 2;
                    const timeSlices = generateTimeSlices(startTime, endTime, interval);

                    promises = timeSlices.map(([sliceStart, sliceEnd]) =>
                        this.getData(
                            {
                                startTime: sliceStart,
                                endTime: sliceEnd,
                                modelList: {
                                    modelId: 7,
                                    modelParam: {
                                        level: 2,
                                        residentType: 1,
                                        top: 100,
                                    },
                                },
                                paramData: {
                                    minVisitNum: 1,
                                    maxVisitNum: 5,
                                },
                            },
                            '主要图表数据查询'
                        )
                    );
                }

                const results = await Promise.all(promises);
                const validResults = results.filter((res) => res !== null);

                if (validResults.length > 0) {
                    // 如果是天粒度，直接使用第一个结果
                    if (granularity === 4) {
                        const { primaryResult, standbyResult } = validResults[0];
                        const { arriveCrowd, flowCrowd, residentCrowd } = primaryResult;
                        const {
                            arriveCrowd: standArrive,
                            flowCrowd: standFlow,
                            residentCrowd: standResident,
                        } = standbyResult;
                        const [
                            totalNumber,
                            crowdIn,
                            crowdOut,
                            accumulatedVisit,
                            residentDistribute,
                        ] = this.chartList;
                        totalNumber.chartData = totalNumber.format(
                            flowCrowd,
                            standFlow,
                            this.isDay
                        );
                        crowdIn.chartData = crowdIn.format(flowCrowd, standFlow, this.isDay);
                        crowdOut.chartData = crowdOut.format(flowCrowd, standFlow, this.isDay);
                        accumulatedVisit.chartData = accumulatedVisit.format(
                            arriveCrowd,
                            standArrive,
                            this.isDay
                        );
                        residentDistribute.chartData = residentDistribute.format(
                            residentCrowd,
                            standResident,
                            this.isDay
                        );
                    } else {
                        // 其他粒度需要合并数据
                        const mergedData = this.mergePermanentData(validResults);
                        const { arriveCrowd, flowCrowd, residentCrowd } = mergedData.primaryResult;
                        const {
                            arriveCrowd: standArrive,
                            flowCrowd: standFlow,
                            residentCrowd: standResident,
                        } = mergedData.standbyResult;
                        const [
                            totalNumber,
                            crowdIn,
                            crowdOut,
                            accumulatedVisit,
                            residentDistribute,
                        ] = this.chartList;
                        totalNumber.chartData = totalNumber.format(
                            flowCrowd,
                            standFlow,
                            this.isDay
                        );
                        crowdIn.chartData = crowdIn.format(flowCrowd, standFlow, this.isDay);
                        crowdOut.chartData = crowdOut.format(flowCrowd, standFlow, this.isDay);
                        accumulatedVisit.chartData = accumulatedVisit.format(
                            arriveCrowd,
                            standArrive,
                            this.isDay
                        );
                        residentDistribute.chartData = residentDistribute.format(
                            residentCrowd,
                            standResident,
                            this.isDay
                        );
                    }
                }
            } catch (error) {
                console.error('获取常驻数据失败:', error);
                this.$message.error('获取常驻数据失败');
            }
        },

        /**
         * 通用数据获取方法
         */
        getData(params, describe) {
            const { regionType, regionCode } = this.row || {};
            return this.highGetPost(
                'monitorApi',
                'getCrowdHot',
                {
                    regionType: regionType,
                    timeSize: this.timeConfig.granularity,
                    regionId: regionCode,
                    ...params,
                },
                describe
            )
                .then((res) => {
                    if (res.serviceFlag === 'TRUE') {
                        return res.data;
                    }
                    this.$message.error(res.returnMsg);
                    return null;
                })
                .catch((err) => {
                    console.log('err~', err);
                    return null;
                });
        },
        getOutline() {
            if (this.row.regionType !== 1) {
                return;
            }
            this.highGetPost(
                'monitorApi',
                'geAreaOutline',
                { regionId: this.row.regionCode },
                '自定义区域轮廓查询'
            )
                .then((res) => {
                    if (res.serviceFlag === 'TRUE') {
                        const data = res.data.regionList[0] || {};
                        if (data.regionCoors) {
                            const points = data.regionCoors.split(';').map((item) => {
                                const [lng, lat] = item.split(',');
                                return {
                                    lng,
                                    lat,
                                };
                            });
                            this.Grid.drawRegion(points);
                        }
                    } else {
                        this.$message.error(res.returnMsg);
                    }
                })
                .catch((err) => {
                    console.log('err~', err);
                });
        },
        jumpDetail() {
            const { granularity, timeRange } = this.timeConfig;
            const [startTime, endTime] = timeRange;
            const { regionType, regionCode } = this.row;
            this.$router.push({
                name: 'highDetail',
                query: {
                    startTime,
                    endTime,
                    regionType,
                    timeSize: granularity,
                    regionId: regionCode,
                },
            });
        },
        handleGridClick(curGrid) {
            const data = curGrid.data;
            const { granularity } = this.timeConfig;
            const { regionType, regionCode } = this.row;
            this.$router.push({
                name: 'highDetail',
                query: {
                    startTime: this.curTime,
                    endTime: this.curTime,
                    isHot: true,
                    s2Id: data.s2Id,
                    regionType,
                    timeSize: granularity,
                    regionId: regionCode,
                },
            });
        },
    },
};
</script>

<style lang="less" scoped>
.history-data {
    height: 100%;
    &__top {
        padding: 0 20px;
        display: flex;
        gap: 20px;
        align-items: center;
        height: 56px;
        .field {
            .name {
                font-size: 14px;
                color: #6d6d6d;
                font-weight: bold;
            }
            .value {
                font-size: 14px;
                color: #4c4c4c;
            }
            &:last-child {
                margin-right: 0;
            }
        }
    }
    &__body {
        position: relative;
        height: calc(100% - 56px);

        .control-panel {
            position: absolute;
            left: 20px;
            top: 16px;
            display: flex;
            align-items: center;

            .custom-checkbox {
                margin: 0;
                padding: 8px 16px;
                border-right: 1px solid #e4e7ed;
                .common-shadow();

                /deep/ .el-checkbox__label {
                    padding-left: 8px;
                }
            }

            /deep/ .time-granularity {
                margin-left: 12px;
                .common-shadow();
            }
        }

        .chart {
            margin-bottom: 16px;
            &:last-child {
                margin-bottom: 0;
            }
        }

        .timeline {
            width: calc(100% - 490px);
        }
    }
}

.common-shadow {
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1), 0px 1px 1px 0px rgba(0, 0, 0, 0.08);
    background-color: white;
    border-radius: 4px;
}
</style>
