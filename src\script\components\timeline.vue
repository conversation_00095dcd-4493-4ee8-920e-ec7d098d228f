<template>
    <div class="time-line">
        <div class="line">
            <div class="date-line" :class="{ isDay: timeSize === 4 }">
                <span
                    class="date"
                    v-for="i in piece"
                    :style="{ width: i === piece ? 0 : `${100 / (piece - 1)}%` }"
                    :key="i - 1"
                    @click="handleDate(i - 1)"
                >
                    <span class="tip" :class="{ 'is-active': isCurDate(i - 1) }">
                        <div class="date-part">{{ formatDisplayDate(dates[i - 1]) }}</div>
                        <div v-if="timeSize !== 4" class="time-part">
                            {{ formatDisplayTime(dates[i - 1]) }}
                        </div>
                    </span>
                    <div v-if="isCurDate(i - 1)" class="image blue"></div>
                    <div v-else-if="dates[i - 1]" class="image lightblue"></div>
                </span>
                <img
                    class="left"
                    src="@/img/icon/arrowLeft.png"
                    alt="left"
                    @click="toggleDates('left')"
                />
                <img
                    class="right"
                    src="@/img/icon/arrowRight.png"
                    alt="right"
                    @click="toggleDates('right')"
                />
            </div>
        </div>
    </div>
</template>

<script>
import { generateTimeSlots } from '@/script/utils/method.js';
import dayjs from 'dayjs';

export default {
    name: 'timeLine',
    props: {
        value: {
            type: String,
            required: true,
            default: () => '2023-01-01 00:30:00',
            validator: function (val) {
                return dayjs(val).isValid();
            },
        },
        piece: {
            type: Number,
            default: 8,
            validator: (v) => v > 0 && v % 1 === 0,
        },
        startTime: {
            type: String,
            default: '2023-01-01 00:00:00',
            validator: function (val) {
                return dayjs(val).isValid();
            },
        },
        endTime: {
            type: String,
            default: '2023-01-01 23:55:00',
            validator: function (val) {
                return dayjs(val).isValid();
            },
        },
        interval: {
            type: Number,
            default: 5,
            validator: (v) => v > 0 && v % 1 === 0,
        },
        isLoop: {
            type: Boolean,
            default: false,
        },
        /**
         * 时间粒度：1代表5min、3代表1hour、4代表1day
         * @type {Number}
         */
        timeSize: {
            type: Number,
            default: 1,
            validator: (v) => [1, 3, 4].includes(v),
        },
    },
    data() {
        return {
            curPageOfDate: 0,
            timer: null,
            dates: [],
            startPosTime: 0,
            endPosTime: 0,
        };
    },
    computed: {
        time: {
            get() {
                return this.value;
            },
            set(value) {
                this.$emit('input', value);
            },
        },
        /**
         * 根据timeSize计算实际的时间间隔
         * @returns {Number} 实际的时间间隔（分钟）
         */
        actualInterval() {
            switch (this.timeSize) {
                case 3: // 小时粒度
                    return 60;
                case 4: // 天粒度
                    return 24 * 60;
                default: // 默认5分钟粒度
                    return this.interval;
            }
        },
        unitTimeInterval() {
            return this.actualInterval * (this.piece - 1);
        },
    },
    mounted() {
        this.initDates();
        this.startLoop();
    },
    beforeDestroy() {
        this.stopLoop();
    },
    methods: {
        initDates() {
            const halfInterval = this.unitTimeInterval / 2;
            const leftInterval = dayjs(this.time).diff(dayjs(this.startTime), 'minute');
            const rightInterval = dayjs(this.endTime).diff(dayjs(this.time), 'minute');
            if (leftInterval < halfInterval) {
                this.startPosTime = this.startTime;
                const endTime = dayjs(this.startPosTime)
                    .add(this.unitTimeInterval, 'minute')
                    .format('YYYY-MM-DD HH:mm:ss');
                this.endPosTime = endTime > this.endTime ? this.endTime : endTime;
            } else if (rightInterval < halfInterval) {
                this.endPosTime = this.endTime;
                const startTime = dayjs(this.endPosTime)
                    .subtract(this.unitTimeInterval, 'minute')
                    .format('YYYY-MM-DD HH:mm:ss');
                this.startPosTime = startTime < this.startTime ? this.startTime : startTime;
            } else {
                const [leftNum, rightNum] = this.getApproximateAverageFactor(this.piece - 1);
                this.startPosTime = dayjs(this.time)
                    .subtract(leftNum * this.actualInterval, 'minute')
                    .format('YYYY-MM-DD HH:mm:ss');
                this.endPosTime = dayjs(this.time)
                    .add(rightNum * this.actualInterval, 'minute')
                    .format('YYYY-MM-DD HH:mm:ss');
            }

            this.dates = generateTimeSlots(this.startPosTime, this.endPosTime, this.actualInterval);
        },
        /**
         * 格式化显示日期部分
         * @param {string} dateTime - 完整的日期时间字符串
         * @returns {string} 格式化后的日期字符串
         */
        formatDisplayDate(dateTime) {
            if (!dateTime) return '';
            return dayjs(dateTime).format('YYYY-MM-DD');
        },
        /**
         * 格式化显示时间部分
         * @param {string} dateTime - 完整的日期时间字符串
         * @returns {string} 格式化后的时间字符串
         */
        formatDisplayTime(dateTime) {
            if (!dateTime || this.timeSize === 4) return '';
            return dayjs(dateTime).format('HH:mm:ss');
        },
        toggleDates(type) {
            let [startPosTime, endPosTime] = type === 'left' ? this.swipeLeft() : this.swipeRight();
            if (startPosTime && endPosTime) {
                this.startPosTime = startPosTime;
                this.endPosTime = endPosTime;
                this.dates = generateTimeSlots(
                    this.startPosTime,
                    this.endPosTime,
                    this.actualInterval
                );
            }
        },
        swipeRight(unitTimeInterval = this.unitTimeInterval) {
            if (this.endPosTime >= this.endTime) {
                return [];
            }
            const startPosTime = dayjs(this.endPosTime)
                .add(this.actualInterval, 'minute')
                .format('YYYY-MM-DD HH:mm:ss');
            let endPosTime = dayjs(startPosTime)
                .add(unitTimeInterval, 'minute')
                .format('YYYY-MM-DD HH:mm:ss');
            if (endPosTime > this.endTime) {
                endPosTime = this.endTime;
            }
            return [startPosTime, endPosTime];
        },
        swipeLeft(unitTimeInterval = this.unitTimeInterval) {
            if (this.startPosTime <= this.startTime) {
                return [];
            }
            let endPosTime = dayjs(this.startPosTime)
                .subtract(this.actualInterval, 'minute')
                .format('YYYY-MM-DD HH:mm:ss');
            let startPosTime = dayjs(endPosTime)
                .subtract(unitTimeInterval, 'minute')
                .format('YYYY-MM-DD HH:mm:ss');
            if (startPosTime < this.startTime) {
                startPosTime = this.startTime;
                endPosTime = dayjs(startPosTime)
                    .add(unitTimeInterval, 'minute')
                    .format('YYYY-MM-DD HH:mm:ss');
            }
            return [startPosTime, endPosTime];
        },
        handleDate(i) {
            const date = this.dates[i];
            if (!date) return;
            this.time = date;
            this.$emit('change', this.time);
        },
        isCurDate(i) {
            return this.dates && this.dates[i] === this.time;
        },
        startLoop() {
            if (!this.isLoop) return;

            this.timer = setInterval(() => {
                if (this.time === this.dates[this.dates.length - 1]) {
                    const [startPosTime, endPosTime] = this.swipeRight();
                    if (startPosTime && endPosTime) {
                        this.startPosTime = startPosTime;
                        this.endPosTime = endPosTime;
                        this.dates = generateTimeSlots(
                            this.startPosTime,
                            this.endPosTime,
                            this.actualInterval
                        );
                        this.time = this.dates[0];
                    } else {
                        this.stopLoop();
                    }
                } else {
                    const inx = this.dates.findIndex((date) => date === this.time);
                    if (inx) {
                        this.time = this.dates[inx + 1];
                    }
                }
                this.$emit('change', this.time);
            }, this.actualInterval * 60 * 1000);
        },
        stopLoop() {
            clearInterval(this.timer);
            this.timer = null;
        },
        getApproximateAverageFactor(num) {
            const average = num / 2;
            const left = Math.floor(average);
            const right = Math.ceil(average);
            return [left, right];
        },
    },
};
</script>

<style lang="less" scoped>
.time-line {
    position: absolute;
    left: 20px;
    bottom: 16px;
    display: flex;
    background-color: #fff;
    .line {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        padding: 0 40px;
        .date-line {
            display: flex;
            align-items: center;
            position: relative;
            padding-bottom: 10px;
            height: 66px;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            .date {
                position: relative;
                align-self: flex-end;
                width: 100%;
                height: 8px;
                background: #ecf2fe;
                border: 1px solid #bbd3fb;
                z-index: 2;
                .image {
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    cursor: pointer;
                }
                .tip {
                    position: absolute;
                    left: 0;
                    transform: translateX(-50%);
                    top: -42px; /* 增加顶部空间以容纳两行文本 */
                    font-size: 12px;
                    white-space: nowrap;
                    color: #8991a5;
                    text-align: center;

                    .date-part {
                        margin-bottom: 2px;
                    }

                    &.is-active {
                        font-size: 13px;
                        color: #1664ff;
                        cursor: pointer;
                    }
                }
                &:first-of-type {
                    .tip {
                        left: 0;
                        transform: translateX(0);
                    }
                }
                &:last-of-type {
                    .tip {
                        transform: translateX(-100%);
                    }
                }
            }
            .left,
            .right {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
            }
            .left {
                left: -30px;
                cursor: pointer;
            }
            .right {
                right: -30px;
                cursor: pointer;
            }
            &.isDay {
                height: 50px;
                .date {
                    .tip {
                        top: -26px;
                    }
                }
            }
        }
    }
}
.lightblue {
    width: 12px;
    height: 12px;
    border-radius: 6px;
    background: #96bbf8;
    border: 3px solid #ffffff;
}
.blue {
    width: 14px;
    height: 14px;
    border-radius: 7px;
    background: #1664ff;
    border: 3px solid #ffffff;
}
</style>
