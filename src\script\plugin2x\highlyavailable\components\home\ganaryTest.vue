<template>
    <div class="monitor">
        <searchBar :fields="fields" :form="form">
            <template>
                <el-button class="highlyavailable-primary-btn" type="primary" size="small"
                    @click="search()">查询</el-button>
                <div class="showExpand" slot="showExpand" @click="clickExpand">
                    {{ isExpand ? '收起' : '展开' }} &nbsp;<i
                        :class="[isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
                </div>
            </template>
        </searchBar>
        <dataTable class="dataTable" :columns="columns" :data="tableData" :pagination="pagination" :total="total" stripe
            :updateTable="getTableData">
            <template #grayscaleType="{ row }">
                <span>{{ grayscaleTypeList[row.grayscaleType] }}</span>
            </template>
            <template #isValid="{ row }">
                <span class="status-text" :class="`${['red', 'green'][row.isValid]}`">{{ statusList[row.isValid]
                }}</span>
            </template>
            <template #startTime="{ row }">
                <span v-if="row.status !== 3">{{ row.startTime }}</span>
                <span v-else></span>
            </template>
            <template #endTime="{ row }">
                <span v-if="row.status !== 3">{{ row.endTime }}</span>
                <span v-else></span>
            </template>
            <template #operation="{ row }">
                <div class="table-btn">
                    <div v-if="row.isValid === 1" class="right-line right-margin">
                        <el-button class="table-btn-item" type="text" size="mini" @click="handleChange(row)">
                            修改
                        </el-button>
                    </div>

                    <div v-if="row.isValid === 1" class="right-margin">
                        <el-button class="table-btn-item redText" type="text" size="mini" @click="handleDel(row)">
                            注销
                        </el-button>
                    </div>
                    <div v-if="row.isValid === 0" class="right-margin">
                        <el-button class="table-btn-item redText" type="text" size="mini" @click="handleReuse(row)">
                            恢复
                        </el-button>
                    </div>
                </div>
            </template>
        </dataTable>
        <!-- 版本新增 -->
        <addVersion v-if="visibleRegion" :visible.sync="visibleRegion" @updateTable="search" :formData="formData" />
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import {
    fields,
    tableColumns,
    regionTypeList,
    statusList,
    grayscaleTypeList,
} from '@/script/constant/ganaryTest.js';
import dataTable from '_com/tables/dataTableLast.vue';
import addVersion from './addVersion.vue';
export default {
    name: 'monitor',
    components: {
        searchBar,
        dataTable,
        addVersion,
    },
    data() {
        return {
            form: {
                createTime: [],
                updateTime: [],
                isValid: '',
                grayscaleValue: '',
                grayscaleType: '',
            },
            formData: {
                type: 'add'
            },
            columns: tableColumns,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15,
            },
            total: 0,
            statusList,
            grayscaleTypeList,
            visibleRegion: false,
            isExpand: false,
        };
    },
    computed: {
        fields() {
            return fields(this.isExpand);
        },
    },
    mounted() {
        this.search();
    },
    methods: {
        clickExpand() {
            this.isExpand = !this.isExpand;
        },
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            const { grayscaleType, grayscaleOperatorId, grayscaleInterface, isValid, createTime, updateTime } = this.form;
            const [startTime, endTime] = createTime || [];
            const [updateStartTime, updateEndTime] = updateTime || [];
            this.highGetPost(
                'monitorApi',
                'ganaryTestInfo',
                {
                    grayscaleType,
                    grayscaleInterface,
                    grayscaleOperatorId,
                    fromCreateTime: startTime,
                    toCreateTime: endTime,
                    fromUpdateTime: updateStartTime,
                    toUpdateTime: updateEndTime,
                    isValid,
                    pageNum: curPage,
                    pageSize,
                },
                '灰度测试管理查询'
            ).then(({ data }) => {
                this.tableData = data.list;
                this.total = data.totalCount;
            });
        },
        handleReuse(row) {
            let params = {
                grayFrom: {
                    grayscaleType: row.grayscaleType,
                    grayscaleInterface: row.grayscaleInterface,
                    grayscaleOperatorId: row.grayscaleOperatorId,
                    comment: row.comment,
                    isValid: row.isValid,
                },
                grayTo: {
                    grayscaleInterface: row.grayscaleInterface,
                    grayscaleOperatorId: row.grayscaleOperatorId,
                    grayscaleType: row.grayscaleType,
                    comment: row.comment,
                    isValid: 1,
                }
            }
            return this.highGetPost('monitorApi', 'grayUpdate', params, '灰度管理更新').then(
                (res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.$message.success(res.returnMsg);
                        this.search();
                    } else {
                        this.$message.error(res.returnMsg);
                    }
                    return res;
                }
            );
        },
        addVersion() {
            this.visibleRegion = true;
            this.formData = {
                type: 'add',
            };
        },
        handleDel(row) {
            // this.curRow = row;
            // this.deleteRegion = true;
            let params = {
                list: [
                    {
                        grayscaleType: row.grayscaleType,
                        grayscaleInterface: row.grayscaleInterface,
                        grayscaleOperatorId: row.grayscaleOperatorId,
                    },
                ]
            };
            return this.highGetPost('monitorApi', 'grayDelete', params, '灰度管理删除').then(
                (res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.$message.success(res.returnMsg);
                        this.search();
                    } else {
                        this.$message.error(res.returnMsg);
                    }
                    return res;
                }
            );
        },
        handleChange(row) {
            this.visibleRegion = true;
            this.formData = {
                type: 'edit',
                grayscaleType: row.grayscaleType,
                grayscaleOperatorId: row.grayscaleOperatorId,
                grayscaleInterface: row.grayscaleInterface,
                comment: row.comment,
            };
        },
    },
};
</script>

<style lang="less" scoped>
.monitor {
    width: 100%;
    height: 100%;
    padding: 1.33rem;
    display: flex;
    flex-direction: column;
}

.showExpand {
    font-size: 14px;
    color: #3871b3;
    cursor: pointer;
}

.dataTable {
    width: 100%;
    flex: 1;
    height: 0;

    .status-text {
        color: #00a870;
        background: #e8f8f2;
        border-radius: 3px;
        border: 1px solid #00a870;
        font-size: 12px;
        padding: 3px;
    }

    .red {
        color: #e34d59;
        background: #fdecee;
        border: 1px solid #e34d59;
    }

    .green {
        color: #00a870;
        border: 1px solid #00a870;
    }
}

a {
    color: #1664ff;
    text-decoration: underline;
    cursor: pointer;
}

.table-btn {
    display: flex;

    .right-line {
        /deep/.el-button ::after {
            content: '';
            display: block;
            width: 1px;
            height: 12px;
            background-color: #ccc;
            position: absolute;
            top: 8px;
            right: 0;
            margin-right: -7px;
        }
    }

    .right-margin {
        margin-right: 12px;
    }

    &-item {
        position: relative;
        font-size: 14px;

        &.redText {
            color: #ff4d4f;
        }

        &::after {
            position: absolute;
            right: -13px;
            top: 8px;
            width: 1px;
            background: #ebeef5;
            height: 11px;
            content: '';
        }

        &:last-child {
            &::after {
                position: absolute;
                right: -13px;
                top: 8px;
                width: 1px;
                background: #ebeef5;
                height: 0px;
                content: '';
            }
        }
    }
}

.startTime {
    display: flex;
    align-items: center;

    .el-date-editor {
        width: 100%;
    }

    .el-checkbox {
        margin-bottom: 0;

        &__label {
            padding-left: 6px;
        }
    }

    /deep/.el-date-editor.el-input,
    .el-date-editor.el-input__inner {
        width: 208px;
    }

    .slash {
        margin: 0 8px;
    }
}

.warning {
    display: flex;
    align-items: center;
    padding-bottom: 10px;

    img {
        width: 16px;
        height: 16px;
    }

    &-text {
        font-size: 14px;
        color: #ff4d4f;
        line-height: 22px;
        padding-left: 10px;
    }
}
</style>
