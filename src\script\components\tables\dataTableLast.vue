<!-- 数据表格 -->
<template>
	<div
		class="data-table"
		ref="tables"
	>
		<el-table
            ref="elTableRef"
			:size="size"
			:header-cell-style="theadColor"
			style="width: 100%"
			v-bind="$attrs"
			v-on="$listeners"
		>
			<!-- 自定义空内容 -->
			<template #empty>
				<slot name="empty"></slot>
			</template>
			<!-- 列 -->
			<el-table-column
				v-if="isSelectable"
				type="selection"
				reserve-selection
				width="36"
			/>
			<template v-for="item in columns">
				<el-table-column
					v-if="!item.prop"
					:key="item.label"
					:label="item.label"
				>
					<el-table-column
						v-for="child in item.child"
						v-bind="child"
						v-slot="{ row, $childindex }"
						:key="child.prop"
						:show-overflow-tooltip="true"
					>
						<slot
							:name="child.prop"
							:row="row"
							:inx="$childindex"
						>
							{{ row[child.prop] }}
						</slot>
					</el-table-column>
				</el-table-column>
				<el-table-column
					v-else
					v-bind="item"
					v-slot="{ row, $index }"
					:key="item.prop"
					:show-overflow-tooltip="true"
				>
					<slot
						:name="item.prop"
						:row="row"
						:inx="$index"
					>
						{{ row[item.prop] }}
					</slot>
				</el-table-column>
			</template>
		</el-table>
		<!-- 分页 -->
		<div
			class="wrap-pagination"
			:class="{ hideUpLine: isHideUpLine }"
			v-if="!isHidePagination"
		>
			<slot name="pagination">
				<el-pagination
					class="pagination"
					:current-page="pagination.curPage"
					:page-sizes="pagination.pageSizes"
					:page-size="pagination.pageSize"
					:total="total"
					:layout="layout"
					:pager-count="pagination.pagerCount"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
				/>
			</slot>
		</div>
	</div>
</template>

<script>
export default {
	name: 'data-table',
	inheritAttrs: false,
	props: {
		columns: {
			type: Array,
			default: () => [],
			required: true,
		},
		size: {
			type: String,
			default: 'small',
		},
		theadStyle: {
			type: Object,
			default: () => ({}),
		},
		updateTable: {
			type: Function,
			default: () => { },
			validator(value) {
				return typeof value === 'function';
			},
		},
		total: {
			type: Number,
			validator: (value) => {
				return value >= 0;
			},
		},
		layout: {
			type: String,
			default: 'total, sizes, prev, pager, next, jumper',
		},
		isSelectable: {
			type: Boolean,
			default: false,
		},
		pagination: {
			type: Object,
			default: () => ({
				curPage: 1,
				pageSize: 15,
			}),
		},
		isHideUpLine: {
			type: Boolean,
			default: false,
		},
		isHidePagination: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {};
	},
	computed: {
		theadColor() {
			return {
				backgroundColor: '#F6F7FA',
				fontWeight: 'bold',
				color: '#4a4a4a',
				fontSize: '14px',
				...this.theadStyle,
			};
		},
	},
	created() {
		this.initPagination();
	},
	methods: {
		initPagination() {
			const { pageSize, pageSizes, pagerCount } = this.pagination;
			const mapPageSizes = {
				10: [10, 15, 25, 40],
				15: [15, 20, 30, 50],
			};
			if (!pageSizes || !pageSizes.length) {
				this.pagination.pageSizes = mapPageSizes[pageSize];
			}
			if (!pagerCount) {
				this.pagination.pagerCount = 7;
			}
		},
		handleSizeChange(pageSize) {
			Object.assign(this.pagination, {
				curPage: 1,
				pageSize,
			});
			this.updateTable({
				curPage: 1,
				pageSize,
			});
		},
		handleCurrentChange(curPage) {
			this.pagination.curPage = curPage;
			this.updateTable({
				curPage,
				pageSize: this.pagination.pageSize,
			});
		},
	},
};
</script>

<style lang="less" scoped>
.data-table {
	height: 100%;
	display: flex;
	flex-flow: column;
	.el-table {
		flex: 1;
		width: 100%;
		/*    display: flex;
    flex-direction: column; */
		/deep/ .el-table__header-wrapper .gutter {
			background-color: #f6f7fa;
		}
		/deep/.el-table__body-wrapper {
			// flex: 1;
			height: calc(100% - 40px);
			overflow-y: auto;   
            z-index: 1;
			.el-table__row {
				&.success-row {
					background-color: #bfe3ff;
				}
				.cell {
					font-size: 14px;
				}
				&.custom-cell {
					padding: 4px 0;
					.cell {
						padding-left: 4px;
						padding-right: 4px;
						line-height: 20px;
					}
				}
			}
			.el-table__body {
				width: 100% !important;
			}
		}
        /deep/ .el-table__fixed-body-wrapper {
            height: calc(100vh - 40px); // 一个是固定列表格项固定高度，自己设
            overflow-y: auto;
            pointer-events: none; // 禁止左侧固定列滑动
            cursor: default;
        }
	}
	.wrap-pagination {
		margin-top: 13px;
		border-top: 1px solid #ddd;
		text-align: right;
		height: 40px;
		.pagination {
			margin: 8px 0;
			padding-right: 0;
			/deep/ .el-pagination__sizes {
				margin-right: 0;
			}
		}
	}
	.hideUpLine {
		margin-top: 5px;
		border-top: none;
	}
}
</style>
