<template>
    <div class="monitor">
        <searchBar :fields="fields" :form="form">
            <el-button
                class="highlyavailable-primary-btn"
                type="primary"
                size="small"
                @click="search()"
                >查询</el-button
            >
        </searchBar>
        <dataTable
            class="dataTable"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :total="total"
            stripe
            :updateTable="getTableData"
        >
            <template #status="{ row }">
                <span>{{ statusList[row.status] }}</span>
            </template>
            <template #switchCount="{ row }">
                <a @click="goHistory(row)">{{ row.switchCount }}</a>
            </template>
            <template #startTime="{ row }">
                <span v-if="row.status !== 3">{{ row.startTime }}</span>
                <span v-else></span>
            </template>
            <template #endTime="{ row }">
                <span v-if="row.status !== 3">{{ row.endTime }}</span>
                <span v-else></span>
            </template>
            <template #nowDatasource="{ row }">
                <span>ck{{ row.nowDatasource }}</span>
            </template>
            <template #operation="{ row }">
                <div class="table-btn">
                    <div v-if="row.status === 1" class="right-line right-margin">
                        <el-button
                            class="table-btn-item"
                            type="text"
                            size="mini"
                            @click="handleClose(row)"
                        >
                            关闭高可用
                        </el-button>
                    </div>
                    <div v-if="[1, 2].includes(row.status)" class="right-line right-margin">
                        <el-button
                            class="table-btn-item"
                            type="text"
                            size="mini"
                            @click="handleChange(row)"
                        >
                            修改配置
                        </el-button>
                    </div>
                    <div v-if="row.status === 1 && row.regionType === 1" class="right-margin">
                        <el-button
                            class="table-btn-item"
                            type="text"
                            size="mini"
                            @click="goDetail(row)"
                        >
                            查看数据详情
                        </el-button>
                    </div>
                    <div v-if="row.status === 3" class="right-line right-margin">
                        <el-button
                            class="table-btn-item"
                            type="text"
                            size="mini"
                            @click="handleStartUp(row)"
                        >
                            启动配置
                        </el-button>
                    </div>
                    <div v-if="[2, 3].includes(row.status)" class="right-margin">
                        <el-button
                            class="table-btn-item red"
                            type="text"
                            size="mini"
                            @click="handleDel(row)"
                        >
                            删除区域
                        </el-button>
                    </div>
                </div>
            </template>
        </dataTable>
        <!-- 关闭高可用 -->
        <myDialog
            title="关闭高可用"
            type="warning"
            tips="您有预设关闭时间，确定要提前关闭高可用状态？"
            :visible.sync="turnOffHighAvailability"
            :btnList="turnOffBtn"
        ></myDialog>
        <!-- 关闭高可用 -->
        <myDialog
            title="删除区域"
            type="warning"
            tips="您确定要删除该区域？"
            :visible.sync="deleteRegion"
            :btnList="deleteRegionBtn"
        ></myDialog>
        <!-- 启动高可用 -->
        <myDialog
            title="启动高可用"
            :visible.sync="startConfiguration"
            :btnList="startConfigurationBtn"
        >
            <searchBar :fields="startFields" :form="startForm">
                <div slot="startTime" class="startTime">
                    <el-checkbox v-model="startForm.startNow" @change="handleStartForm"
                        >立即开始</el-checkbox
                    >
                    <span class="slash">/</span>
                    <el-date-picker
                        v-model="startForm.startTime"
                        type="datetime"
                        placeholder="选择日期时间"
                    >
                    </el-date-picker>
                </div>
            </searchBar>
        </myDialog>
        <!-- 修改高可用 -->
        <myDialog
            title="修改高可用"
            :visible.sync="modifyConfiguration"
            :btnList="modifyConfigurationBtn"
        >
            <div>
                <div v-if="curRow.isValid" class="warning">
                    <img src="../../../../../img/icon/warning.png" alt="" />
                    <div class="warning-text">高可用已开启，开始时间不可更改！</div>
                </div>
                <searchBar :fields="startFieldsList" :form="modifyForm">
                    <div slot="startTime" class="startTime">
                        <el-checkbox v-model="modifyForm.startNow" @change="handleModifyForm"
                            >立即开始</el-checkbox
                        >
                        <span class="slash">/</span>
                        <el-date-picker
                            v-model="modifyForm.startTime"
                            type="datetime"
                            placeholder="选择日期时间"
                        >
                        </el-date-picker>
                    </div>
                </searchBar>
            </div>
        </myDialog>
        <!-- 区域新增 -->
        <addRegion v-if="visibleRegion" :visible.sync="visibleRegion" @updateTable="search" />
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import {
    fields,
    tableColumns,
    regionTypeList,
    statusList,
    startFields
} from '@/script/constant/monitor.js';
import dataTable from '_com/tables/dataTableLast.vue';
import myDialog from '_com/dialog/index.vue';
import { getAreasToCity } from '@/script/utils/method.js';
import addRegion from './addRegion.vue';
export default {
    name: 'monitor',
    components: {
        searchBar,
        dataTable,
        myDialog,
        addRegion
    },
    data() {
        return {
            form: {
                time: [],
                status: 0,
                classifyName: [],
                cityName: [],
                regionCodeList: '',
                regionName: ''
            },
            startFields,
            startForm: {
                startNow: false,
                startTime: '',
                endTime: ''
            },
            modifyForm: {
                startNow: false,
                startTime: '',
                endTime: ''
            },
            columns: tableColumns,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            total: 0,
            regionTypeList,
            statusList,
            turnOffHighAvailability: false,
            modifyConfiguration: false,
            startConfiguration: false,
            deleteRegion: false,
            turnOffBtn: [
                {
                    name: '关闭',
                    class: 'blue-btn',
                    listeners: {
                        click: () => {
                            const row = this.curRow;
                            this.edit({
                                regionCodeList: [row.regionCode],
                                regionType: row.regionType, // 区域类型 1：自定义区域，2：省，3：市，4：区县
                                endTime: '1970-12-06 18:00:00'
                            }).then((res) => {
                                this.turnOffHighAvailability = false;
                                this.getTableData(this.pagination);
                            });
                        }
                    }
                }
            ],
            deleteRegionBtn: [
                {
                    name: '删除',
                    class: 'red-btn',
                    listeners: {
                        click: () => {
                            this.highGetPost(
                                'monitorApi',
                                'delete',
                                { regionCodeList: [this.curRow.regionCode] },
                                '高可用监控删除'
                            )
                                .then((res) => {
                                    if (res.serviceFlag === 'TRUE') {
                                        this.$message.success(res.returnMsg);
                                        this.deleteRegion = false;
                                        this.search();
                                    } else {
                                        this.$message.error(res.returnMsg);
                                    }
                                })
                                .catch(() => {});
                        }
                    }
                }
            ],
            startConfigurationBtn: [
                {
                    name: '启动',
                    class: 'blue-btn',
                    listeners: {
                        click: () => {
                            const row = this.curRow;
                            let { startTime, endTime } = this.startForm;
                            if (!startTime || !endTime) {
                                return this.$message.warning('时间范围未填写！');
                            }
                            this.edit({
                                regionCodeList: [row.regionCode],
                                regionType: row.regionType, // 区域类型 1：自定义区域，2：省，3：市，4：区县
                                startTime,
                                endTime
                            }).then((res) => {
                                this.startConfiguration = false;
                                Object.assign(this.startForm, {
                                    startNow: false,
                                    startTime: '',
                                    endTime: ''
                                });
                                this.getTableData(this.pagination);
                            });
                        }
                    }
                }
            ],
            modifyConfigurationBtn: [
                {
                    name: '修改',
                    class: 'blue-btn',
                    listeners: {
                        click: () => {
                            const { endTime } = this.modifyForm;
                            if (!endTime) {
                                return this.$message.warning('预设结束时间未填写！');
                            }
                            const row = this.curRow;
                            this.edit({
                                regionCodeList: [row.regionCode],
                                regionType: row.regionType, // 区域类型 1：自定义区域，2：省，3：市，4：区县
                                // nowDatasource: 1,
                                // primaryDatasource: 1,
                                // standbyDatasource: 2,
                                startTime: row.startTime,
                                endTime
                            }).then((res) => {
                                this.modifyConfiguration = false;
                                Object.assign(this.modifyForm, {
                                    startNow: false,
                                    startTime: '',
                                    endTime: ''
                                });
                                this.getTableData(this.pagination);
                            });
                        }
                    }
                }
            ],
            curRow: {},
            visibleRegion: false
        };
    },
    computed: {
        startFieldsList() {
            const fields = JSON.parse(JSON.stringify(startFields));
            if (this.curRow.isValid) {
                fields.shift();
            }
            return fields;
        },
        fields() {
            const { highDistricts, highLayers } = this.$store.getters;
            return fields({
                citys: getAreasToCity(highDistricts, true),
                layerIds: highLayers
            });
        }
    },
    watch: {
        modifyConfiguration(val) {
            if (!val) {
                Object.assign(this.modifyForm, {
                    startNow: false,
                    startTime: '',
                    endTime: ''
                });
            }
        }
    },
    mounted() {
        this.search();
    },
    methods: {
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            const { regionName, regionCodeList, cityName, classifyName, status, time } = this.form;
            const [startTime, endTime] = time;
            this.highGetPost(
                'monitorApi',
                'selectAdditional',
                {
                    regionName,
                    regionCodeList: regionCodeList ? [regionCodeList] : null,
                    cityCode: (cityName || []).filter((city) => !city.includes('all')),
                    classifyCode: classifyName,
                    status,
                    startTime,
                    endTime,
                    isValid: 1,
                    pageNum: curPage,
                    pageSize
                },
                '高可用监控查询'
            ).then(({ data }) => {
                this.tableData = data.list;
                this.total = data.pageTotal;
            });
        },
        edit(params) {
            return this.highGetPost('monitorApi', 'update', params, '高可用监控更新').then(
                (res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.$message.success(res.returnMsg);
                    } else {
                        this.$message.error(res.returnMsg);
                    }
                    return res;
                }
            );
        },
        addRegion() {
            this.visibleRegion = true;
        },
        goDetail(row) {
            this.$router.push({
                name: 'mapDetail',
                query: row
            });
        },
        handleChange(row) {
            this.curRow = row;
            Object.assign(this.modifyForm, {
                startNow: false,
                startTime: row.startTime,
                endTime: row.endTime
            });
            this.modifyConfiguration = true;
        },
        handleStartUp(row) {
            this.startForm = {
                startNow: false,
                startTime: '',
                endTime: ''
            };
            this.curRow = row;
            this.startConfiguration = true;
        },
        handleClose(row) {
            this.curRow = row;
            this.turnOffHighAvailability = true;
        },
        handleDel(row) {
            this.curRow = row;
            this.deleteRegion = true;
        },
        handleStartForm(val) {
            if (val) {
                this.startForm.startTime = new Date().format('yyyy-MM-dd HH:mm:ss');
            } else {
                this.startForm.startTime = '';
            }
        },
        handleModifyForm(val) {
            if (val) {
                this.modifyForm.startTime = new Date().format('yyyy-MM-dd HH:mm:ss');
            } else {
                this.modifyForm.startTime = '';
            }
        },
        goHistory(row) {
            const { regionName, regionCode, regionType } = row;
            this.$router.push({
                path: 'history',
                query: {
                    regionName,
                    regionCode,
                    regionType
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
.monitor {
    width: 100%;
    height: 100%;
    padding: 1.33rem;
    display: flex;
    flex-direction: column;
}
.dataTable {
    width: 100%;
    flex: 1;
    height: 0;
}
a {
    color: #1664ff;
    text-decoration: underline;
    cursor: pointer;
}
.table-btn {
    display: flex;
    .right-line {
        /deep/.el-button ::after {
            content: '';
            display: block;
            width: 1px;
            height: 12px;
            background-color: #ccc;
            position: absolute;
            top: 8px;
            right: 0;
            margin-right: -7px;
        }
    }
    .right-margin {
        margin-right: 12px;
    }
    &-item {
        position: relative;
        font-size: 14px;
        &.red {
            color: #ff4d4f;
        }
        &::after {
            position: absolute;
            right: -13px;
            top: 8px;
            width: 1px;
            background: #ebeef5;
            height: 11px;
            content: '';
        }
        &:last-child {
            &::after {
                position: absolute;
                right: -13px;
                top: 8px;
                width: 1px;
                background: #ebeef5;
                height: 0px;
                content: '';
            }
        }
    }
}
.startTime {
    display: flex;
    align-items: center;
    .el-date-editor {
        width: 100%;
    }
    .el-checkbox {
        margin-bottom: 0;
        &__label {
            padding-left: 6px;
        }
    }
    /deep/.el-date-editor.el-input,
    .el-date-editor.el-input__inner {
        width: 208px;
    }
    .slash {
        margin: 0 8px;
    }
}
.warning {
    display: flex;
    align-items: center;
    padding-bottom: 10px;
    img {
        width: 16px;
        height: 16px;
    }
    &-text {
        font-size: 14px;
        color: #ff4d4f;
        line-height: 22px;
        padding-left: 10px;
    }
}
</style>
