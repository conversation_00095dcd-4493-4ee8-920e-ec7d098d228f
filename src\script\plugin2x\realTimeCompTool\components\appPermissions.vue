<template>
    <div class="appPermissions">
        <commonComp
            ref="commonComp"
            :fieldsList="fields"
            :formList="form"
            :tableColumns="tableColumns"
            :dialogFieldsList="dialogFields"
            :dialogFormList="dialogForm"
            @getTableData="getTableData"
            @handleDelete="handleDelete"
            @addOrEdit="addOrEdit"
            @restore="restore"
        ></commonComp>
    </div>
</template>

<script>
import commonComp from './common.vue';
import { fields, tableColumns, dialogFields } from '../constatnt/appPermissions.js';
export default {
    name: 'appPermissions',
    components: {
        commonComp
    },
    data() {
        return {
            fields: fields(),
            form: {
                complaintType: 1,
                startTime: '',
                endTime: '',
                userId: ''
            },
            tableColumns,
            dialogFields: dialogFields,
            dialogForm: {
                complaintType: 1,
                isValid: 0,
                userId: '',
                userName: ''
            }
        };
    },
    methods: {
        getTableData(pagination = {}, form, cb) {
            const { curPage = 1, pageSize = 15 } = pagination;
            this.highGetPost(
                'realTimeToolSeviceApi',
                'getAuthList',
                {
                    ...form,
                    pageNum: curPage,
                    pageSize
                },
                '获取权限列表'
            ).then(({ data }) => {
                cb && cb(data);
            });
        },
        handleAdd() {
            this.$refs.commonComp.handleAdd();
        },
        handleDelete(row, cb) {
            this.highGetPost(
                'realTimeToolSeviceApi',
                'authDelete',
                {
                    id: row.id
                },
                '删除权限信息'
            ).then((res) => {
                cb && cb(res);
            });
        },
        addOrEdit(form, type, row, cb) {
            if (type === 'add') {
                this.highGetPost(
                    'realTimeToolSeviceApi',
                    'authAdd',
                    {
                        ...form
                    },
                    '新增权限'
                ).then((res) => {
                    cb && cb(res);
                });
            } else {
                this.highGetPost(
                    'realTimeToolSeviceApi',
                    'authUpdate',
                    {
                        id: row.id,
                        ...form
                    },
                    '修改权限信息'
                ).then((res) => {
                    cb && cb(res);
                });
            }
        },
        restore(row, { id }, cb) {
            this.highGetPost(
                'realTimeToolSeviceApi',
                'authUpdate',
                {
                    id,
                    ...row,
                    isValid: 1
                },
                '恢复投诉应用权限'
            ).then((res) => {
                cb && cb(res);
            });
        }
    }
};
</script>

<style lang="less" scoped>
.appPermissions {
    width: 100%;
    height: 100%;
}
</style>
