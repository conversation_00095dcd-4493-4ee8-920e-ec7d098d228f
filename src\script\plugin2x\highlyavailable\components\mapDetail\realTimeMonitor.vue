<template>
    <div class="realTime-monitor">
        <div class="realTime-monitor__top">
            <div class="libraryType field">
                <span class="name">请选择库: </span>
                <el-radio-group
                    class="radio-group"
                    :value="libraryType"
                    @input="handleLibraryTypeChange"
                    size="small"
                    :key="isToggleLibrary"
                >
                    <el-radio-button label="primary">主群库</el-radio-button>
                    <el-radio-button label="standby">备用群库</el-radio-button>
                </el-radio-group>
            </div>
            <div v-for="item in fields" class="field" :key="item.label">
                <span class="name">{{ item.label }}</span>
                <span class="value">{{
                    item.format ? item.format(detail[item.prop]) : detail[item.prop]
                }}</span>
            </div>
        </div>
        <div class="realTime-monitor__body">
            <gisMap @loaded="gisLoaded" />
            <!--  -->
            <el-checkbox class="custom-checkbox" v-model="isCheckHot">总人数热力图</el-checkbox>
            <!-- charts -->
            <slideDialog>
                <chart
                    class="chart"
                    v-for="chart in chartList"
                    :ref="chart.prop"
                    :key="chart.title"
                    :isConsistent="chart.isConsistent"
                    v-bind="chart"
                    @jump="jumpDetail"
                    @data-zoom-left="(curChart, cb) => handleLeftZoom(curChart, chart.prop, cb)"
                    @data-zoom-right="(curChart, cb) => handleDataZoom(curChart, chart.prop, cb)"
                >
                    <el-select
                        slot="tool"
                        v-if="chart.prop === 'portrait'"
                        v-model="curAge"
                        class="w-84"
                        size="mini"
                        @change="handleAge"
                    >
                        <el-option v-for="val in ageOpts" :key="val" :value="val" />
                    </el-select>
                    <el-select
                        slot="tool"
                        v-else-if="chart.prop === 'sex'"
                        v-model="curSex"
                        class="w-84"
                        size="mini"
                        @change="handleSex"
                    >
                        <el-option v-for="val in sexOpts" :key="val" :value="val" />
                    </el-select>
                </chart>
            </slideDialog>
            <!--  -->
            <time-line
                v-if="isCheckHot"
                class="timeline"
                v-model="curTime"
                :startTime="startTime"
                :endTime="endTime"
                :interval="5"
                isLoop
                @change="handleTimeChange"
            ></time-line>
            <!--  -->
            <customLegend v-if="isCheckHot && legendList.length" :list="legendList" />
        </div>
    </div>
</template>

<script>
import gisMap from '@/script/components/gisMap/index.vue';
import slideDialog from '@/script/components/slideDialog.vue';
import chart from '@/script/components/chart/index.vue';
import TimeLine from '@/script/components/timeline.vue';
import initGrid from '@/script/components/gisMap/layers/Grid';
import { realTimeMonitorFields, realTimeChartList, ChartQueue } from '@/script/constant/mapDetail';
import {
    getLegendRanges,
    roundToNearestFiveMinutes,
    generateTimeSlices
} from '@/script/utils/method';
import dayjs from 'dayjs';
import { mapDetailMixin } from './mixin';
export default {
    name: 'realTime-monitor',
    mixins: [mapDetailMixin],
    components: {
        gisMap,
        slideDialog,
        chart,
        TimeLine,
        customLegend: () => import('@/script/components/custom-legend.vue')
    },
    data() {
        return {
            chartList: realTimeChartList,
            fields: realTimeMonitorFields,
            libraryType: 'primary',
            detail: {},
            isCheckHot: false,
            curTime: new Date().format('yyyy-MM-dd HH:mm:ss'),
            startTime: '',
            endTime: '',
            curChartTime: '',
            libraryList: [],
            numbers: [],
            range: {},
            curAge: '18-24',
            curSex: '男',
            ageOpts: ['18-24', '25-34', '35-44', '45-54', '55岁以上'],
            sexOpts: ['男', '女'],
            totalNumberQueue: null,
            crowdInQueue: null,
            crowdOutQueue: null,
            accumulatedVisitQueue: null,
            residentDistributeQueue: null,
            portraitQueue: null,
            sexQueue: null,
            isToggleLibrary: false
        };
    },
    computed: {
        queryRoute() {
            return this.$route.query;
        },
        row() {
            return this.queryRoute;
        },
        legendList() {
            return Object.keys(this.range).map((key) => {
                return {
                    color: this.range[key],
                    text: key
                };
            });
        },
        isCk1() {
            return this.row.nowDatasource === 1;
        }
    },
    watch: {
        isCheckHot(val) {
            if (val) {
                this.getCrowdHot();
            } else {
                this.Grid.removeAll();
            }
        }
    },
    created() {
        if (this.isCk1) {
            this.libraryType = 'standby';
        }
        this.initTime();
    },
    mounted() {
        this.detail = this.row;
        this.getChartData();
    },
    beforeDestroy() {
        this.Grid && this.Grid.destroy();
        clearInterval(this.chartTimer);
    },
    methods: {
        setQueue(lQueue = []) {
            // 将队列根据当前索引分割成左右两个数组
            this.totalNumberQueue = new ChartQueue(lQueue);
            this.crowdInQueue = new ChartQueue(lQueue);
            this.crowdOutQueue = new ChartQueue(lQueue);
            this.accumulatedVisitQueue = new ChartQueue(lQueue);
            this.residentDistributeQueue = new ChartQueue(lQueue);
            this.portraitQueue = new ChartQueue(lQueue);
            this.sexQueue = new ChartQueue(lQueue);
        },
        initTime() {
            const { startTime, endTime } = this.row;
            if (startTime && endTime) {
                this.startTime = roundToNearestFiveMinutes(startTime, 'down');
                this.endTime = roundToNearestFiveMinutes(endTime, 'up');
            }
            this.curTime = roundToNearestFiveMinutes(
                dayjs(this.curTime).subtract(15, 'minute').format('YYYY-MM-DD HH:mm:ss'),
                'down'
            );
            this.curChartTime = this.curTime;
            this.setQueue(generateTimeSlices(this.startTime, this.curChartTime));
        },
        gisLoaded(g) {
            this.Grid = initGrid(g, {
                click: this.handleGridClick
            });
            this.getOutline();
            this.startLoopChart();
        },
        getCrowdHot() {
            if (!this.isCheckHot) {
                return;
            }
            if (![4, 1].includes(+this.row.regionType)) {
                this.$message.warning('热力模型只支持区县和自定义区域');
                return;
            }
            this.getData(
                {
                    modelList: {
                        modelId: '2',
                        modelParam: {
                            s2Level: appConfig.highS2Level || '17'
                        }
                    },
                    startTime: this.curTime,
                    endTime: this.curTime

                    // timeSize: '4',
                    // regionType: '1',
                    // regionId: 'SH1580143044169170944',
                    // startTime: '2024-10-31 00:00:00',
                    // endTime: '2024-11-06 00:00:00',
                },
                '热力查询'
            ).then((res) => {
                if (res) {
                    const { primaryResult, standbyResult } = res;
                    const primaryData = primaryResult[0] && primaryResult[0].regionData;
                    const standData = standbyResult[0] && standbyResult[0].regionData;
                    this.libraryList = (primaryData || []).map((item, inx) => {
                        const standItem = standData[inx];
                        return {
                            ...item,
                            primary: item.total,
                            standby: standItem.total
                        };
                    });
                    this.Grid.createGrids(
                        this.formatData(this.libraryList, this.libraryType),
                        this.setText
                    );
                } else {
                    this.Grid.createGrids([]);
                }
            });
        },
        getChartData() {
            const curItem = this.totalNumberQueue.outLQueue();
            this.crowdInQueue.outLQueue();
            this.crowdOutQueue.outLQueue();
            this.accumulatedVisitQueue.outLQueue();
            this.residentDistributeQueue.outLQueue();
            this.portraitQueue.outLQueue();
            this.sexQueue.outLQueue();
            if (curItem) {
                this.getPermanent(curItem);
                this.getPortrait(curItem);
                this.getSex(curItem);
            }
        },
        async getPortrait([start, end], prop) {
            return this.getData(
                {
                    modelList: {
                        modelId: '3',
                        modelParam: {
                            age: [this.curAge]
                        }
                    },
                    startTime: start,
                    endTime: end,
                    pageNum: 1,
                    pageSize: 50
                    // test data
                    /*  regionType: 1,
                    regionId: 'SH1580143044169170944',
                    timeSize: 1,
                    startTime: '2024-11-06 00:00:00',
                    endTime: '2024-11-07 00:00:00',
                    pageNum: 1,
                    pageSize: 2, */
                },
                '画像查询'
            ).then((res) => {
                if (!res) {
                    return this.$message.warning('画像查询-暂无数据');
                }
                const { primaryResult, standbyResult } = res;
                const { age } = primaryResult;
                const { age: standbyAge } = standbyResult;
                const curChart = this.chartList.find((item) => item.prop === 'portrait');
                if (prop) {
                    return curChart.format(age, standbyAge);
                }
                curChart.chartData = curChart.format(age, standbyAge);
            });
        },
        async getSex([start, end], prop) {
            return this.getData(
                {
                    modelList: {
                        modelId: '3',
                        modelParam: {
                            sex: [this.curSex]
                        }
                    },
                    startTime: start,
                    endTime: end,
                    pageNum: 1,
                    pageSize: 50
                    // test data
                    /*  regionType: 1,
                    regionId: 'SH1580143044169170944',
                    timeSize: 1,
                    startTime: '2024-11-06 00:00:00',
                    endTime: '2024-11-07 00:00:00',
                    pageNum: 1,
                    pageSize: 2, */
                },
                '年龄查询'
            ).then((res) => {
                if (!res) {
                    return this.$message.warning('年龄查询-暂无数据');
                }
                const { primaryResult, standbyResult } = res;
                const { sex } = primaryResult;
                const { sex: standbySex } = standbyResult;
                const curChart = this.chartList.find((item) => item.prop === 'sex');
                if (prop) {
                    return curChart.format(sex, standbySex);
                }
                curChart.chartData = curChart.format(sex, standbySex);
            });
        },
        async getPermanent([start, end], prop) {
            if (!start || !end) {
                return this.$message.warning('时间参数不存在！');
            }
            return this.getData(
                {
                    startTime: start,
                    endTime: end,
                    modelList: {
                        modelId: 7,
                        modelParam: {
                            level: 2,
                            residentType: 1,
                            top: 100
                        }
                    },
                    paramData: {
                        minVisitNum: 1,
                        maxVisitNum: 5
                    },
                    pageNum: 1,
                    pageSize: 50
                },
                '主要图表数据查询'
            ).then((res) => {
                if (!res) {
                    return this.$message.warning('主要图表数据查询异常');
                }

                const { primaryResult, standbyResult } = res;
                const { arriveCrowd, flowCrowd, residentCrowd } = primaryResult;
                const {
                    arriveCrowd: standArrive,
                    flowCrowd: standFlow,
                    residentCrowd: standResident
                } = standbyResult;
                const [totalNumber, crowdIn, crowdOut, accumulatedVisit, residentDistribute] =
                    this.chartList;

                const totalNumberData = totalNumber.format(flowCrowd, standFlow);
                const crowdInData = crowdIn.format(flowCrowd, standFlow);
                const crowdOutData = crowdOut.format(flowCrowd, standFlow);
                const accumulatedVisitData = accumulatedVisit.format(arriveCrowd, standArrive);
                const residentDistributeData = residentDistribute.format(
                    residentCrowd,
                    standResident
                );

                if (prop) {
                    switch (prop) {
                        case 'totalNumber':
                            return totalNumberData;
                        case 'crowdIn':
                            return crowdInData;
                        case 'crowdOut':
                            return crowdOutData;
                        case 'accumulatedVisit':
                            return accumulatedVisitData;
                        case 'residentDistribute':
                            return residentDistributeData;
                    }
                } else {
                    totalNumber.chartData = totalNumberData;
                    crowdIn.chartData = crowdInData;
                    crowdOut.chartData = crowdOutData;
                    accumulatedVisit.chartData = accumulatedVisitData;
                    residentDistribute.chartData = residentDistributeData;
                }
            });
        },
        async handleLeftZoom(chart, prop, callback) {
            const queue = this[`${prop}Queue`];
            const shift = queue.outLQueue();
            if (!shift || !shift.length) return;
            let data;
            if (prop === 'portrait') {
                data = await this.getPortrait(shift, prop);
            } else if (prop === 'sex') {
                data = await this.getSex(shift, prop);
            } else {
                data = await this.getPermanent(shift, prop);
            }
            if (data) {
                const { xData, series } = data || {};
                const [first, second] = series;
                // 追加数据到两个 series

                const mainData = chart.getOption().series[0].data;
                const srcXData = chart.getOption().xAxis[0].data;
                // 扩展 xAxis
                chart.setOption({
                    xAxis: {
                        data: xData.concat(srcXData)
                    },
                    series: [
                        {
                            data: first.data.concat(mainData)
                        },
                        {
                            data: second.data.concat(chart.getOption().series[1].data)
                        }
                    ]
                });
                // 调整 dataZoom 的 end 范围
                // const defaultWidthOfSlider = 100,
                //     defaultNumbers = 12,
                //     defaultStart = 30,
                //     minSpacing = 5,
                // end = 60;
                // const spacing = (defaultNumbers / (mainData.length || 1)) * defaultWidthOfSlider;
                // const validSpacing = Math.max(spacing, minSpacing);
                // const start = 25;
                chart.setOption({
                    dataZoom: [{ start: 25, end: 45 }]
                });
            }
            callback && callback();
        },
        async handleDataZoom(chart, prop, callback) {
            const queue = this[`${prop}Queue`];
            const shift = queue.outRQueue();
            if (!shift || !shift.length) return;
            let data;
            if (prop === 'portrait') {
                data = await this.getPortrait(shift, prop);
            } else if (prop === 'sex') {
                data = await this.getSex(shift, prop);
            } else {
                data = await this.getPermanent(shift, prop);
            }
            if (data) {
                const { xData, series } = data || {};
                const [first, second] = series;
                // 追加数据到两个 series
                chart.appendData({ seriesIndex: 0, data: first.data });
                chart.appendData({ seriesIndex: 1, data: second.data });

                const mainData = chart.getOption().series[0].data;
                // 扩展 xAxis
                chart.setOption({
                    xAxis: {
                        data: chart.getOption().xAxis[0].data.concat(xData)
                    }
                });
                // 调整 dataZoom 的 end 范围
                const defaultWidthOfSlider = 100,
                    defaultNumbers = 12,
                    defaultStart = 30,
                    minSpacing = 5,
                    end = 90;
                const spacing = (defaultNumbers / (mainData.length || 1)) * defaultWidthOfSlider;
                const validSpacing = Math.max(spacing, minSpacing);
                const start = validSpacing > end ? defaultStart : end - validSpacing;
                chart.setOption({
                    dataZoom: [{ start, end }]
                });
            }
            callback && callback();
        },
        getData(params, describe) {
            const { regionType, regionCode } = this.row;
            return this.highGetPost(
                'monitorApi',
                'getCrowdHot',
                {
                    regionType,
                    timeSize: 1,
                    regionId: regionCode,
                    ...params
                },
                describe
            )
                .then((res) => {
                    if (res.serviceFlag === 'TRUE') {
                        return res.data;
                    }
                    this.$message.error(res.returnMsg);
                    return null;
                })
                .catch((err) => {
                    console.log('err~', err);
                    return null;
                });
        },
        getOutline() {
            if (this.row.regionType !== 1) {
                return;
            }
            this.highGetPost(
                'monitorApi',
                'geAreaOutline',
                {
                    regionId: this.row.regionCode,
                    searchType: 2
                },
                '自定义区域轮廓查询'
            )
                .then((res) => {
                    if (res.serviceFlag === 'TRUE') {
                        const data = res.data.regionList[0] || {};
                        if (data.regionCoors) {
                            const points = data.regionCoors.split(';').map((item) => {
                                const [lng, lat] = item.split(',');
                                return {
                                    lng,
                                    lat
                                };
                            });
                            this.Grid.drawRegion(points);
                        }
                    } else {
                        this.$message.error(res.returnMsg);
                    }
                })
                .catch((err) => {
                    console.log('err~', err);
                });
        },
        formatData(list, countType) {
            this.numbers = list.map((item) => item[countType]);
            this.range = getLegendRanges(Math.max(...this.numbers));
            return list.map((item) => {
                return {
                    centerPoint: {
                        lng: item.longitude,
                        lat: item.latitude
                    },
                    points: item.vertexes.map((it) => ({
                        lng: it.longitude,
                        lat: it.latitude
                    })),
                    config: {
                        color: this.getHeatColor(item[countType], this.range),
                        name: '人数',
                        score: item.count
                    },
                    data: item
                };
            });
        },
        startLoopChart() {
            if (this.chartTimer) {
                clearInterval(this.chartTimer);
            }
            this.chartTimer = setInterval(() => {
                const currentTime = dayjs(this.curChartTime);
                // this.curChartTime = currentTime.add(5, 'minute').format('YYYY-MM-DD HH:mm:ss');
                this.curChartTime = currentTime.add(10, 'second').format('YYYY-MM-DD HH:mm:ss');
                [
                    this.totalNumberQueue,
                    this.crowdInQueue,
                    this.crowdOutQueue,
                    this.accumulatedVisitQueue,
                    this.residentDistributeQueue,
                    this.portraitQueue,
                    this.sexQueue
                ].forEach((queue, inx) => {
                    const keys = [
                        'totalNumber',
                        'crowdIn',
                        'crowdOut',
                        'accumulatedVisit',
                        'residentDistribute',
                        'portrait',
                        'sex'
                    ];
                    if (!queue.rQueue.length) {
                        const key = keys[inx];
                        const ref = this.$refs[key][0];
                        queue.pushRQueue([
                            currentTime.format('YYYY-MM-DD HH:mm:ss'),
                            this.curChartTime
                        ]);
                        ref && this.handleDataZoom(ref.chart, key);
                    } else {
                        const lastItem = queue.outRQueue();
                        const [start] = lastItem;
                        const newSubQueue = generateTimeSlices(start, this.curChartTime);
                        if (newSubQueue.length > 0) {
                            queue.pushRQueue(...newSubQueue);
                        }
                    }
                });
                if (this.curChartTime > this.endTime) {
                    clearInterval(this.chartTimer);
                    return;
                }
            }, 5 * 60 * 1000);
        },
        handleTimeChange() {
            this.getCrowdHot();
        },
        getHeatColor(score, range) {
            const matchedKey = Object.keys(range).find((key) => {
                const [min, max] = key.split('-').map(Number);
                return score >= min && score < max;
            });
            return matchedKey ? parseInt(range[matchedKey].replace('#', '0x')) : '';
        },
        handleAge() {
            const queue = generateTimeSlices(this.startTime, this.curChartTime);
            this.portraitQueue = new ChartQueue(queue);
            const pop = this.portraitQueue.outLQueue();
            this.getPortrait(pop);
        },
        handleSex() {
            const queue = generateTimeSlices(this.startTime, this.curChartTime);
            this.sexQueue = new ChartQueue(queue);
            const pop = this.sexQueue.outLQueue();
            this.getSex(pop);
        },
        jumpDetail() {
            const { regionType, regionCode } = this.row;
            this.$router.push({
                name: 'highDetail',
                query: {
                    startTime: this.startTime,
                    endTime: this.curChartTime,
                    regionType,
                    timeSize: 1,
                    regionId: regionCode
                }
            });
        },
        handleGridClick(curGrid) {
            const data = curGrid.data;
            const { regionType, regionCode } = this.row;
            this.$router.push({
                name: 'highDetail',
                query: {
                    startTime: this.curTime,
                    endTime: this.curTime,
                    isHot: true,
                    s2Id: data.s2Id,
                    regionType,
                    timeSize: 1,
                    regionId: regionCode
                }
            });
        },
        handleLibraryTypeChange(newType) {
            if (newType === this.libraryType) return;
            if (this.isCk1) {
                this.libraryType = newType;
                this.Grid.createGrids(this.formatData(this.libraryList, newType), this.setText);
                return;
            }
            this.$confirm(
                `确定要切换到${newType === 'primary' ? '主群库' : '备用群库'}吗?`,
                '提示',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }
            )
                .then(() => {
                    this.libraryType = newType;
                    let primaryDatasource, standbyDatasource;
                    if (this.libraryType === 'standby') {
                        primaryDatasource = 1;
                        standbyDatasource = this.row.nowDatasource;
                    } else {
                        primaryDatasource = this.row.nowDatasource;
                        standbyDatasource = 1;
                    }
                    this.highGetPost(
                        'monitorApi',
                        'update',
                        {
                            regionCodeList: [this.row.regionCode],
                            regionType: this.row.regionType,
                            primaryDatasource,
                            standbyDatasource
                        },
                        '高可用监控更新'
                    ).then((res) => {
                        if (res.serviceFlag === 'TRUE') {
                            this.$message.success(res.returnMsg);
                            this.getCrowdHot();
                        } else {
                            this.$message.error(res.returnMsg);
                        }
                        return res;
                    });
                })
                .catch(() => {
                    // 用户取消操作，不做任何处理
                    this.isToggleLibrary = !this.isToggleLibrary;
                });
        }
    }
};
</script>

<style lang="less" scoped>
.realTime-monitor {
    height: 100%;
    &__top {
        display: flex;
        gap: 20px;
        padding: 0 20px;
        display: flex;
        align-items: center;
        height: 56px;
        .libraryType {
            white-space: nowrap;
        }
        .field {
            .name {
                font-size: 14px;
                color: #6d6d6d;
                font-weight: bold;
            }
            .value {
                font-size: 14px;
                color: #4c4c4c;
            }
            .radio-group {
                /deep/ .el-radio-button__orig-radio:checked + .el-radio-button__inner {
                    background-color: #1664ff;
                    border-color: #1664ff;
                }
            }
            &:last-child {
                margin-right: 0;
            }
        }
    }
    &__body {
        position: relative;
        height: calc(100% - 56px);
        .custom-checkbox {
            position: absolute;
            left: 20px;
            top: 16px;
            margin-bottom: 0;
            padding: 8px 16px;
            background-color: white;
            box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1), 0px 1px 1px 0px rgba(0, 0, 0, 0.08);
            border-radius: 4px;
            /deep/ .el-checkbox__label {
                padding-left: 8px;
            }
        }
        .chart {
            margin-bottom: 16px;
            &:last-child {
                margin-bottom: 0;
            }
        }
        .timeline {
            width: calc(100% - 490px);
        }
    }
}
.w-84 {
    width: 84px;
}
</style>
