<!-- 区域对选择、省/市/县组件 -->
<template>
    <div v-loading="isLoading" class="multiArea-select">
        <div class="multiArea-select__area">
            <selectArea
                v-model="checkedList"
                :selectOps="selectAreaOps"
                :treeData="regionTree"
                :prefix.sync="prefix"
                @checkedArea="handleChecked"
                @checkedAreaAll="handleCheckedAll"
                isMultiple
            />
        </div>
        <div class="multiArea-select__mall">
            <selectScene
                ref="selectSceneRef"
                :checkedData="checkedData"
                :options="sceneOptions"
                :selectOps="sceneTypes"
                :choiceAreaType.sync="choiceAreaType"
                @selectOps="handleSelect"
                @sceneCheck="handleSceneChecked2"
                @toggleAreaType="toggleAreaType"
                v-on="$listeners"
            />
        </div>
        <div class="multiArea-select__selected">
            <selectedTags title="区域名称" :tags="tags" @delTags="deleteTag" :total="total" />
        </div>
    </div>
</template>

<script>
import selectArea from './selectArea.vue';
import selectScene from './selectScene.vue';
import selectedTags from './selectedTags.vue';
export default {
    name: 'multiArea-select',
    inject: ['getTags', 'setRegionData'],
    props: {
        regionType: {
            type: String,
            default: '',
        },
        selectLabel: {
            type: String,
            default: '',
        },
        regionList: {
            type: Array,
            default: [],
        },
    },
    components: {
        selectArea,
        selectScene,
        selectedTags,
    },
    data() {
        return {
            mapPanelLevels: {
                省份: 1,
                地市: 2,
                区县: 3,
            },
            selectAreaOps: [
                { label: '地市', value: 1 },
                { label: '区县', value: 2 },
            ],
            choiceAreaType: 1,
            // 默认回显数据
            checkedList: [],
            tags: [],
            checkedScenes: [],
            selectVal: 0,
            prefix: '',
            sceneOptions: [],
            mapSceneOptions: [],
            checkedData: [[]],
            total: 0,
            nowArea: [],
            isLoading: false,
        };
    },
    computed: {
        power() {
            return window.baseCompExt2.userInfor.admin;
        },
        regionTree() {
            return this.$store.state.highAvailable.regionTree;
        },
        sceneTypes() {
            return this.$store.state.highAvailable.sceneTypes;
        },
        curSceneType() {
            return this.sceneTypes.find((item) => item.value === this.selectVal).label;
        },
        selectList() {
            return this.checkedData[this.selectVal];
        },
        regionNames() {
            return this.$store.state.highAvailable.regionNames;
        },
        checkLabels() {
            return this.checkedList.map((value) => {
                const values = value.split('/');
                return this.regionNames[values[values.length - 1]];
            });
        },
    },
    watch: {
        tags(newTags) {
            if (Array.isArray(newTags)) {
                const tagLabels = [];
                const tagValues = [];
                newTags.forEach(({ label, value, name, sceneType, province }) => {
                    if (value === `${sceneType}-all`) {
                        const values = this.mapSceneOptions[sceneType]
                            .map(({ label, value }) => ({ regionName: label, regionId: value }))
                            .slice(1);
                        tagValues.push(...values);
                    } else {
                        tagValues.push({
                            regionName: name,
                            regionId: value,
                            province,
                        });
                    }
                    tagLabels.push(label);
                });
                this.getTags(tagLabels);
                this.setRegionData(this.selectLabel, tagValues);
            }
        },
    },
    mounted() {
        this.initRegionData();
    },
    methods: {
        initRegionData() {
            const checkDataOfAll = this.checkedData[0];
            this.regionList.forEach((item) => {
                checkDataOfAll.push(item.regionId);
                this.tags.push({
                    label: item.regionName,
                    name: item.regionName,
                    value: item.regionId,
                    province: item.province,
                    sceneType: 0,
                });
            });
        },
        getSceneOps(isToggleScene) {
            // 若为切换场景，则判断是否需要重新获取下拉项
            if (
                isToggleScene &&
                this.mapSceneOptions[this.selectVal] &&
                this.mapSceneOptions[this.selectVal].length
            ) {
                this.sceneOptions = this.mapSceneOptions[this.selectVal];
                this.setAllCheck();
                return;
            }
            const { province, city, district } = this.formatCheckedData(this.checkedList);
            if (!province.length) {
                this.$message({
                    type: 'error',
                    message: '省份列表必填！',
                });
                return;
            }
            this.isLoading = true;
            this.highGetPost(
                'commentApi',
                'getAoi',
                {
                    province, //参考维表
                    city, //参考维表
                    district, //参考维表
                    shapeType: 2,
                    searchType: this.choiceAreaType,
                    layerIds: [this.selectVal].filter(Boolean), //参考图层维表
                    pageIndex: 0, //指定查询第几页默认第1页
                    rowsPage: 10000000, //指定每页行数，默认1000行
                },
                'aoi查询'
            )
                .then((rcvData) => {
                    this.isLoading = false;
                    const rest = (rcvData.data.regionList || []).map((item) => {
                        const areas = new Set([
                            item.provinceName,
                            item.cityName,
                            item.districtName,
                        ]);
                        return {
                            fullName: [...areas, '.', this.curSceneType, '.', item.regionName]
                                .filter(Boolean)
                                .join(''),
                            label: item.regionName,
                            value: item.regionId,
                            province: item.province,
                        };
                    });
                    this.nowArea = rest.map((item) => {
                        return {
                            ...item,
                        };
                    });
                    let prefix = this.prefix;
                    if (district.length > 1 || city.length > 1) {
                        prefix = this.getPrefixes(this.checkLabels);
                    }
                    const all = {
                        fullName: `${prefix}.${this.curSceneType}.全部(${rest.length})`,
                        label: '全部',
                        value: `${this.selectVal}-all`,
                    };
                    this.total = rest.length;
                    this.checkedData[this.selectVal];
                    this.sceneOptions = [all, ...rest];
                    this.mapSceneOptions[this.selectVal] = this.sceneOptions;
                    this.setAllCheck();
                    let timer = setTimeout(() => {
                        this.initCheck();
                        clearTimeout(timer);
                    }, 300);
                })
                .catch((err) => {
                    this.$exloaded1x();
                    this.isLoading = false;
                    let options = {
                        title: '搜索区域名称',
                        content: `详细内容：${err}`,
                    };
                    this.$popupMessageWindow(options);
                });
        },
        // 设置全选按钮状态
        setAllCheck() {
            const flag = this.checkArea();
            if (!flag && this.checkedData[this.selectVal].includes(`${this.selectVal}-all`)) {
                this.checkedData[this.selectVal].shift();
            } else if (
                flag &&
                this.checkedData[this.selectVal] &&
                !this.checkedData[this.selectVal].includes(`${this.selectVal}-all`)
            ) {
                this.checkedData[this.selectVal].unshift(`${this.selectVal}-all`);
            }
        },
        // 设置是否已选
        initCheck() {
            if (!this.tags.length) {
                return;
            }
            const tagList = this.tags.map((item) => item.value);
            const nowList = this.nowArea.map((item) => item.value);
            const list = this.checkedData[this.selectVal];
            tagList.forEach((item, index) => {
                if (!list.includes(item) && nowList.includes(item)) {
                    const list = this.checkedData[this.selectVal].concat(item);
                    this.$set(this.checkedData, this.selectVal, list);
                }
            });
        },
        // 待优化
        formatCheckedData(checkedList) {
            const province = [];
            const city = [];
            const district = [];
            checkedList.forEach((values) => {
                const [provinceVal, cityVal, districtVal] = values.split('/');
                province.push(provinceVal);
                city.push(cityVal);
                district.push(districtVal);
            });
            return {
                province: [...new Set(province)],
                city: [...new Set(city.filter(Boolean))],
                district: district.filter(Boolean),
            };
        },
        // 地市选中发生改变
        handleChecked(item, checkedItems, isChangeOfArea) {
            // 省市类型发生根本性改变，取消所有区域选中/删除所有区域名称
            if (isChangeOfArea) {
                this.mapSceneOptions = this.mapSceneOptions.map(() => []);
                // this.clearScenes();
            }
            if (this.checkedList.length) {
                const flag = this.mapSceneOptions.every((child) => !child.length) ? true : false;

                this.getSceneOps(flag);
            }
        },
        handleCheckedAll(data) {},
        // 判断是否全部包含
        checkArea() {
            const list = this.regionList.map((item) => item.regionId);
            const nowAreaList = this.nowArea.map((item) => item.value);
            const flag = nowAreaList.every((item) => list.includes(item));
            return flag;
        },
        handleSceneChecked2(item, status, check) {
            const curAllVal = `${this.selectVal}-all`;
            if (status) {
                if (item.value === curAllVal) {
                    // 直接选择全部
                    this.checkedData[this.selectVal] = this.sceneOptions.map((item) => item.value);
                    const idList = this.tags.map((item) => item.value);
                    this.nowArea.forEach((child) => {
                        if (!idList.includes(child.value)) {
                            this.tags.push({
                                label: child.fullName,
                                name: child.label,
                                value: child.value,
                                province: child.province,
                                sceneType: this.selectVal,
                            });
                        }
                    });
                } else {
                    const index = this.tags.findIndex((child) => child.value === item.value);
                    if (index === -1) {
                        this.tags.push({
                            label: item.fullName,
                            name: item.label,
                            value: item.value,
                            province: item.province,
                            sceneType: this.selectVal,
                        });
                    } else {
                        this.$message.warning('区域已选择');
                    }
                    this.$nextTick(() => {
                        const flag = this.checkArea();
                        if (
                            flag &&
                            !this.checkedData[this.selectVal].includes(`${this.selectVal}-all`)
                        ) {
                            this.checkedData[this.selectVal].unshift(curAllVal);
                        }
                    });
                }
            } else {
                if (item.value === curAllVal) {
                    // 直接取消全部
                    this.passCheckedAll();
                    // 删除所有tags
                    // this.delCurTags();
                    this.nowArea.forEach((child) => {
                        const tagInx = this.tags.findIndex((tag) => tag.value === child.value);
                        this.tags.splice(tagInx, 1);
                        this.checkedData.forEach((cItem) => {
                            const checkIndex = cItem.findIndex((cItemC) => cItemC === child.value);
                            if (checkIndex > -1) {
                                this.checkedData[this.selectVal].splice(checkIndex, 1);
                            }
                        });
                    });
                } else {
                    const tagInx = this.tags.findIndex((tag) => tag.value === item.value);
                    this.tags.splice(tagInx, 1);
                    this.$nextTick(() => {
                        const flag = this.checkArea();
                        if (
                            !flag &&
                            this.checkedData[this.selectVal].includes(`${this.selectVal}-all`)
                        ) {
                            this.checkedData[this.selectVal].shift();
                        }
                    });
                }
            }
        },
        handleSceneChecked(item, status) {
            const curAllVal = `${this.selectVal}-all`;
            if (status) {
                if (item.value === curAllVal) {
                    // 直接选择全部
                    this.checkedData[this.selectVal] = this.sceneOptions.map((item) => item.value);
                    // 选择tags
                    this.addFirstItem(curAllVal);
                } else if (
                    // 间接选中全部
                    this.selectList.length ===
                    this.sceneOptions.length - 1
                ) {
                    this.checkedData[this.selectVal].unshift(curAllVal);
                    // 选择tags
                    this.addFirstItem(curAllVal);
                } else {
                    this.tags.push({
                        label: item.fullName,
                        name: item.label,
                        value: item.value,
                        province: item.province,
                        sceneType: this.selectVal,
                    });
                }
            } else {
                if (item.value === curAllVal) {
                    // 直接取消全部
                    this.passCheckedAll();
                } else {
                    const tagInx = this.tags.findIndex((tag) => tag.value === item.value);
                    this.tags.splice(tagInx, 1);
                }
            }
        },
        passCheckedAll() {
            this.checkedData[this.selectVal] = [];
        },
        delCurTags() {
            this.tags = this.tags.filter(
                (tag) => !this.sceneOptions.some((scene) => scene.value === tag.value)
            );
        },
        // 去除所有项，添加首项
        addFirstItem(curAllVal) {
            this.delCurTags();
            const allItem = this.sceneOptions.find((scene) => scene.value === curAllVal);
            if (allItem) {
                this.tags.push({
                    label: allItem.fullName,
                    name: allItem.label,
                    value: allItem.value,
                    sceneType: this.selectVal,
                });
            }
        },
        // 切换了场景类型
        handleSelect(value) {
            this.selectVal = value;
            if (!this.checkedData[value]) {
                this.checkedData[value] = [];
            }
            this.getSceneOps(true);
        },
        toggleAreaType() {
            this.$nextTick(() => {
                this.getSceneOps();
            });
        },
        clearScenes() {
            this.tags = [];
            this.checkedData = this.checkedData.map(() => []);
            this.mapSceneOptions = this.mapSceneOptions.map(() => []);
        },
        deleteTag(curTag, clearAll) {
            if (clearAll) {
                this.tags = [];
                this.checkedData = this.checkedData.map(() => []);
                return;
            }
            // 删除tags
            const tagInx = this.tags.findIndex((tag) => tag.value === curTag.value);
            this.tags.splice(tagInx, 1);
            // 同步选中状态
            const curDelSceneType = curTag.sceneType;
            if (curTag.value === `${curDelSceneType}-all`) {
                this.$set(this.checkedData, curDelSceneType, []);
            } else {
                const checkedData = this.checkedData[curDelSceneType].filter(
                    (value) => value !== curTag.value
                );
                this.$set(this.checkedData, curDelSceneType, checkedData);
            }
            this.$nextTick(() => {
                this.setAllCheck();
            });
        },
        getPrefixes(checkLabels) {
            const firstLabels = [...new Set(checkLabels[0].split('/'))];
            const firstLabel = firstLabels.slice(0, firstLabels.length - 1).join('');
            const restLabels = checkLabels.map((label) => {
                const labels = label.split('/');
                return labels[labels.length - 1];
            });
            return `${firstLabel}(${restLabels.join('、')})`;
        },
    },
};
</script>
<style lang="less" scoped>
.multiArea-select {
    display: inline-flex;

    &__mall {
        margin-right: 6px;
    }

    &__selected {
        width: 400px;
    }
}
</style>
