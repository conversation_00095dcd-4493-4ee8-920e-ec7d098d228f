import GatewayAxios from './gatewayAxios.js';
import { baseMixin } from 'mtex-rams-core';
const operateUserId = '111112222233333';
const sourceSystemId = '22244445555';
const sourceSystemName = '555666';
//真实调用接口使用的租户信息。
const POST_SUCCESS = '000001';
export let rel_operateUserId = '';
export let rel_sourceSystemId = '';
export let rel_sourceSystemName = '';
export default {
    userInfor: {
        provinceCode: '',
        provinceName: ''
    },
    install($vue, store) {
        //全局混入
        $vue.mixin(baseMixin);

        $vue.prototype.$CreateAxiox = ($this) => {
            return new GatewayAxios(rel_operateUserId, rel_sourceSystemId, rel_sourceSystemName);
        };
        $vue.prototype.$Update = ($this) => {
            let umName = $this.$store.getters.user.name;
            let gateway = new GatewayAxios(operateUserId, sourceSystemId, sourceSystemName);
            gateway
                .update(umName)
                .then((res) => {
                    let data = res.data;
                    rel_operateUserId = data.operateUserId;
                    rel_sourceSystemId = data.sourceSystemId;
                    rel_sourceSystemName = data.sourceSystemName;
                    this.userInfor = {
                        provinceCode: data.provinceCode,
                        provinceName: data.provinceName
                    };
                })
                .catch((err) => {
                    console.log('错误信息：', err);
                });
        };
    },

    getTenant(umName) {
        return new Promise((resolve, reject) => {
            let gateway = new GatewayAxios(operateUserId, sourceSystemId, sourceSystemName);
            gateway
                .update(umName)
                .then((res) => {
                    if (res.returnCode == POST_SUCCESS) {
                        let data = res.data || [];
                        rel_operateUserId = data.operateUserId;
                        rel_sourceSystemId = data.sourceSystemId;
                        rel_sourceSystemName = data.sourceSystemName;
                        this.userInfor = {
                            provinceCode: data.provinceCode,
                            provinceName: data.provinceName
                        };
                    }
                    resolve(res);
                    return;
                })
                .catch((err) => {
                    console.log('错误信息：', err);
                    reject({
                        success: err,
                        errorMessage: err
                    });
                });
        });
    },

    async initTenant(umName) {
        const tenantData = await this.getTenant(umName);
        return tenantData;
    }
};
