<script>
/**
 * @description 自定义图例组件
 */
export default {
    functional: true,
    name: 'custom-legend',
    props: {
        /**
         * @description 图例列表数据
         * @type {Array<{color: string, text: string, className?: string}>}
         * @default []
         */
        list: {
            type: Array,
            default: () => [
                { color: '#57782C', text: '畅通' },
                { color: '#ADCA87', text: '基本畅通' },
                { color: '#CEC101', text: '轻度拥堵' },
                { color: '#EE9E01', text: '拥堵' },
                { color: '#FE1707', text: '严重拥堵' },
            ],
        },
    },
    render(h, context) {
        return h('div', { class: 'map-legend' }, [
            h(
                'ul',
                { class: 'list' },
                context.props.list.map((item) => {
                    return h('li', { class: `item ${item.className || ''}` }, [
                        h('span', { class: 'dot', style: { 'background-color': item.color } }),
                        h('span', { class: 'text' }, item.text),
                    ]);
                })
            ),
        ]);
    },
};
</script>

<style lang="less" scoped>
.map-legend {
    position: absolute;
    padding: 8px 12px;
    left: 20px;
    bottom: 92px;
    border-radius: 6px;
    background-color: rgba(255, 255, 255); /* 半透明背景 */
    z-index: 3;
    .list {
        margin: 0;
        padding: 0;
        .item {
            display: flex;
            align-items: center;
            list-style: none;
            .dot {
                display: inline-block;
                margin-right: 6px;
                width: 12px;
                height: 12px;
            }
            .text {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.65);
            }
        }
    }
}
</style>
