<!-- 区域选择、省/市/县组件 -->
<template>
    <div class="subArea-select" :class="isVertical ? '' : ''">
        <selectArea
            class="subArea-select__select"
            :class="mapWidths[regionType]"
            ref="selectAreaRef"
            v-model="checkedList"
            :tags.sync="tags"
            :treeData="regionTree"
            :panelLevels="panelLevels"
            v-bind="$attrs"
        />
        <selectedTags
            class="subArea-select__show"
            :class="isVertical ? mapWidths[regionType] : ''"
            :panelLevels="panelLevels"
            v-bind="$attrs"
            :tags="tags"
            @delTags="delTags"
        />
    </div>
</template>

<script>
import selectArea from './selectArea.vue';
import selectedTags from './selectedTags.vue';
export default {
    name: 'sub-area-select',
    inheritAttrs: false,
    inject: ['getTags', 'setRegionData'],
    props: {
        isVertical: {
            type: Boolean,
            default: false,
        },
        regionType: {
            type: String,
            default: '',
        },
        regionList: {
            type: Array,
            default: [],
        },
        selectLabel: {
            type: String,
            default: '',
        },
        panelLevels: {
            type: Number,
            default: 3,
        },
    },
    components: {
        selectArea,
        selectedTags,
    },
    data() {
        return {
            // 默认回显数据
            checkedList: [],
            tags: [],
            mapWidths: {
                省份: 'w-194',
                地市: 'w-260',
                区县: 'w-380',
            },
        };
    },
    computed: {
        regionTree() {
            return this.$store.state.highAvailable.regionTree;
        },
        regionIds() {
            return this.$store.state.highAvailable.regionIds;
        },
        regionNames() {
            return this.$store.state.highAvailable.regionNames;
        },
    },
    mounted() {
        this.initCheckData(this.regionList);
        this.$watch(
            'tags',
            (newTags) => {
                if (Array.isArray(newTags)) {
                    this.updateTags(
                        newTags.map(({ label, value }) => {
                            const values = value.split('/');
                            return {
                                label: label,
                                lastId: values[values.length - 1],
                            };
                        })
                    );
                }
            },
            { immediate: true }
        );
    },
    methods: {
        initCheckData(regionList) {
            this.checkedList = [];
            this.tags = [];
            const mapCheckData = {};
            regionList.forEach(({ regionName, regionId }) => {
                let val = this.regionIds[regionId];
                if (this.panelLevels === 2) {
                    const values = val.split('/');
                    if (values.length === 3) {
                        val = [values[0], values[2]].join('/');
                    }
                }
                this.checkedList.push(val);
                this.tags.push({
                    value: val,
                    label: regionName,
                });
                mapCheckData[val] = regionName;
            });
            this.$nextTick(() => {
                this.$refs.selectAreaRef.initData(this.checkedList[0] || '', mapCheckData);
            });
        },
        delTags(tag, clearAll) {
            if (clearAll) {
                this.tags = [];
                this.checkedList = [];
                this.$refs.selectAreaRef.checkedAll = false;
                return;
            }
            this.tags = this.tags.filter((item) => item.label !== tag.label);
            this.checkedList = this.checkedList.filter((val) => val !== tag.value);
        },
        updateTags(tags) {
            const tagLabels = [];
            const tagValues = [];
            tags.forEach((tag) => {
                tagLabels.push(tag.label);
                // tagValues.push(tag.lastId);
                tagValues.push({
                    regionName: tag.label,
                    regionId: tag.lastId,
                });
            });
            if (this.isVertical) {
                this.$emit('collectTags', tagLabels, tagValues);
            } else {
                this.getTags(tagLabels);
                this.setRegionData(this.selectLabel, tagValues);
            }
        },
    },
};
</script>
<style lang="less" scoped>
.subArea-select {
    display: flex;
    &__select {
        margin-bottom: 8px;
    }
    &__show {
        width: 380px;
        // flex: 1;
        overflow: hidden;
    }
}
.flex-vertical {
    flex-direction: column;
}
.w-194 {
    width: 194px;
}
.w-260 {
    width: 260px;
}
.w-380 {
    width: 380px;
}
</style>
