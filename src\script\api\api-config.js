import GatewayAxios from '../common/gatewayAxios.js';
import { rel_operateUserId, rel_sourceSystemId, rel_sourceSystemName } from '../common/baseCompExt';
// post 请求
const promiseFun = function (url, params = {}) {
    const API = new GatewayAxios(rel_operateUserId, rel_sourceSystemId, rel_sourceSystemName, true);
    return new Promise((resolve, reject) => {
        const requestParmas = {
            requestData: params
        };
        API.post(url, requestParmas).then((res) => {
            resolve(res);
        }).catch((err) => {
            console.log('err: ', err);
            reject(err);
        });
    });
};
export default promiseFun;