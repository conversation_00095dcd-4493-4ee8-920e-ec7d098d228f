const fields = [
    // {
    //     prop: 'gridId',
    //     label: '栅格ID：',
    //     labelWidth: '70',
    //     element: 'el-input',
    //     bind: {
    //         placeholder: '请输入',
    //         clearable: true,
    //     },
    //     span: 3,
    // },
    /*     {
            prop: 'gridName',
            label: '栅格名称：',
            labelWidth:'70',
            element: 'el-input',
            attrs: {
                placeholder: '请输入',
                clearable: true,
            },
            span: 3,
        }, */
    {
        prop: 'time',
        label: '数据时间：',
        labelWidth: '40',
        element: 'el-date-picker',
        bind: {
            type: 'datetimerange',
            'range-separator': '-',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            format: 'yyyy-MM-dd HH:mm:00',
            'value-format': 'yyyy-MM-dd HH:mm:00'
        },
        span: 7
    },
    {
        span: 2
    }
];
const tableColumns = [
    {
        prop: 'gridId',
        label: '区域ID'
    },
    /*     {
            prop: 'gridName',
            label: '栅格名称',
        }, */
    {
        prop: 'time',
        label: '数据时间'
    },
    {
        prop: 'mainLibraryGroup',
        label: '主库群'
    },
    {
        prop: 'backupStorageGroup',
        label: '备用库群'
    }
];

export { fields, tableColumns };
