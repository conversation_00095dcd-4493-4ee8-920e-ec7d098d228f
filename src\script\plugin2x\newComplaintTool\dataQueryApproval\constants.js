// 状态枚举
const fields = [
    {
        prop: 'listType',
        label: '投诉类型:',
        element: 'el-select',
        bind: {
            placeholder: '请选择',
            clearable: true
        },
        slot: {
            element: 'el-option',
            enums: [
                { label: '用户轨迹稽核', value: 1 },
                { label: '实时事件稽核', value: 2 }
            ]
        },
        span: 5
    },
    {
        prop: 'queryMsisdn',
        label: '申请查询号码:',
        element: 'el-input',
        bind: {
            placeholder: '请输入',
            clearable: true
        },
        span: 5
    },
    {
        prop: 'status',
        label: '状态:',
        element: 'el-select',
        bind: {
            placeholder: '请选择',
            clearable: true
        },
        slot: {
            element: 'el-option',
            enums: [
                { label: '申请中', value: 0 },
                { label: '同意', value: 1 },
                { label: '驳回', value: 2 },
                { label: '禁止', value: 3 },
                { label: '数据准备中', value: 4 },
                { label: '数据准备完成', value: 5 },
                { label: '数据准备失败', value: 6 }
            ]
        },
        span: 4
    },
    {
        prop: 'taskName',
        label: '申请任务:',
        element: 'el-input',
        bind: {
            placeholder: '请输入',
            clearable: true
        },
        span: 5
    },
    {
        span: 5
    }
];

const statusList = ['申请中', '同意', '驳回', '禁止','数据准备中', '数据准备完成', '数据准备失败'];

// 表格列配置
const tableColumns = [
    {
        prop: 'id',
        label: 'ID'
    },
    {
        prop: 'applicantNameCh',
        label: '申请人'
    },
    {
        prop: 'operateUserId',
        label: '租户ID'
    },
    {
        prop: 'listType',
        label: '投诉类型'
    },
    {
        prop: 'workOrderName',
        label: '工单名称'
    },
    {
        prop: 'queryMsisdn',
        label: '用户号码'
    },
    {
        prop: 'filePath',
        label: '附件',
        width: 200
    },
    {
        prop: 'authStime',
        label: '授权开始时间',
        width: 120
    },
    {
        prop: 'authEtime',
        label: '授权结束时间',
        width: 120
    },
    {
        prop: 'dataStime',
        label: '数据开始时间',
        width: 120
    },
    {
        prop: 'dataEtime',
        label: '数据结束时间',
        width: 120
    },
    {
        prop: 'taskName',
        label: '申请任务'
    },
    {
        prop: 'applicationReason',
        label: '申请原因'
    },
    {
        prop: 'status',
        label: '状态'
    },
    {
        prop: 'approverNameCh',
        label: '审批人'
    },
    {
        prop: 'approvalOpn',
        label: '审批意见'
    },
    {
        prop: 'isValid',
        label: '是否有效'
    },
    {
        prop: 'createTime',
        label: '创建时间',
        width: 120
    },
    {
        prop: 'lastUpdateTime',
        label: '最后修改时间',
        width: 120
    },
    {
        prop: 'operation',
        label: '操作',
        // fixed: 'right',
        width: 120
    }
];

const addFields = [
    {
        prop: 'status',
        label: '状态:',
        labelWidth: '80px',
        element: 'el-select',
        bind: {
            placeholder: '请选择',
            clearable: true
        },
        rules: [{ required: true, message: '请选择', trigger: 'change' }],
        slot: {
            element: 'el-option',
            enums: [
                { label: '申请中', value: 0 },
                { label: '同意', value: 1 },
                { label: '驳回', value: 2 },
                { label: '禁止', value: 3 },
                { label: '数据准备中', value: 4 },
                { label: '数据准备完成', value: 5 },
                { label: '数据准备失败', value: 6 }
            ]
        }
    },
    {
        prop: 'approvalOpn',
        label: '申请意见:',
        labelWidth: '80px',
        element: 'el-input',
        rules: [{ required: true, message: '请输入', trigger: 'blur' }],
        bind: {
            type: 'textarea',
            rows: 3,
            placeholder: '请输入',
            clearable: true
        }
    }
];

export { tableColumns, fields, addFields, statusList };
