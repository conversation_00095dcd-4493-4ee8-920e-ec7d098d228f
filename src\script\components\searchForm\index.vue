<script>
import Layout from './components/layout.vue';
import FormItem from './components/formItem.vue';
import MetaItem from './components/meta-item.vue';

export default {
    name: 'SearchForm',
    components: {
        Layout,
        FormItem,
        MetaItem
    },
    props: {
        fields: {
            type: Array,
            required: true
        },
        form: {
            type: Object,
            required: true
        },
        gutter: {
            type: Number,
            default: 12
        },
        customClass: {
            default: 'search-bar',
            type: String
        },
        isLayout: {
            type: Boolean,
            default: true
        },
        isBubbleTip: {
            type: Boolean,
            default: true
        }
    },
    methods: {
        async validForm() {
            try {
                return await this.$refs.searchBarRef.validate();
            } catch (err) {
                return err;
            }
        },
        renderField(field) {
            // 统一处理逻辑：
            // 1. 有element → 渲染组件
            // 2. 无element → 优先用prop插槽，否则用默认插槽
            if (!field.element) {
                return (
                    (this.$scopedSlots[field.prop] && this.$scopedSlots[field.prop]()) ||
                    (this.$scopedSlots.default && this.$scopedSlots.default()) ||
                    this.renderDefaultSlot(field)
                );
            }
            // 使用MetaItem组件
            return this.$createElement(MetaItem, {
                props: { field, form: this.form }
            });
        },

        renderDefaultSlot(field) {
            if (typeof field.slot === 'string') {
                return field.slot;
            }
            return this.$createElement('div', [field.slot]);
        },

        wrapFormItem(content, field) {
            // eslint-disable-next-line no-ternary
            return !field.hasOwnProperty('isNeedFormItem') || field.isNeedFormItem
                ? this.$createElement(
                      FormItem,
                      {
                          props: {
                              field,
                              isBubbleTip: this.isBubbleTip,
                              isLayout: this.isLayout
                          }
                      },
                      [content]
                  )
                : content;
        }
    },
    render(h) {
        return h(
            'el-form',
            {
                ref: 'searchBarRef',
                class: `${this.customClass} ${this.isLayout ? 'w-full' : ''}`,
                props: {
                    model: this.form,
                    size: 'small',
                    inline: true,
                    ...this.$attrs
                },
                on: this.$listeners
            },
            [
                // eslint-disable-next-line no-ternary
                this.isLayout
                    ? h(Layout, {
                          props: { fields: this.fields, gutter: this.gutter },
                          scopedSlots: {
                              default: ({ field }) =>
                                  this.wrapFormItem(this.renderField(field), field)
                          }
                      })
                    : this.fields.map((field) => this.wrapFormItem(this.renderField(field), field))
            ]
        );
    },
    async deactivated() {
        if (this.$listeners.validate) {
            const res = await this.validForm();
            this.$emit('validate', res);
        }
    }
};
</script>
<style lang="less" scoped>
.w-full {
    width: 100%;
}
</style>
