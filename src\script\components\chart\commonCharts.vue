<template>
    <div class="echart-wrapper">
        <el-empty v-if="isEmpty" :image="noData1"></el-empty>
        <div v-else :id="name" class="echart-data"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import { ChartsCon } from './chartsConfigure.js';
const initEcharts = (myChart, options, type, that) => {
    const option = ChartsCon[type](options);
    myChart.clear();
    myChart.setOption(option);
};
export default {
    name: 'commonCharts',
    props: {
        data: {
            type: Object,
            default: () => ({})
        },
        name: {
            type: String,
            default: 'line'
        }
    },
    data() {
        return {
            myChart: '',
            noData1: require('../../../img/noData1.svg')
        };
    },
    computed: {
        isEmpty() {
            if (JSON.stringify(this.data) === '{}' || this.data.isNull) {
                return true;
            }
            return false;
        }
    },
    watch: {
        data: {
            handler(newV) {
                if (newV) {
                    this.initEchart();
                }
            },
            deep: true
        }
    },
    mounted() {
        const timer = setTimeout(() => {
            this.$watch(
                'data',
                (newData) => {
                    this.initEchart();
                },
                { immediate: true, deep: true }
            );
            clearTimeout(timer);
        }, 0);
        window.addEventListener('resize', this.resize);
    },
    activated() {
        this.resize();
    },
    destroyed() {
        window.removeEventListener('resize', this.resize);
    },
    methods: {
        initEchart() {
            if (this.isEmpty) {
                this.myChart && this.myChart.dispose();
                this.myChart = null;
                return;
            }
            this.$nextTick(() => {
                if (!this.myChart) {
                    this.myChart = echarts.init(document.getElementById(this.name));
                    this.$emit('loadChart', this.myChart);
                }
                initEcharts(this.myChart, this.data, this.data.type, this);

                this.myChart.on('click', (params) => {
                    this.$emit('commonChartsClick', params.dataIndex);
                });
            });
        },
        resize() {
            if (this.myChart) {
                this.myChart.resize();
                this.initEchart(); //重绘渲染字体
            }
        }
    }
};
</script>

<style lang="less" scoped>
.echart-wrapper {
    width: 100%;
    height: 100%;
    // border: 1px solid #e7e7e7;
    position: relative;
}
.echart-data {
    width: 100%;
    height: 100%;
}
/deep/.el-empty {
    width: 100%;
    height: 100%;
}
/deep/.el-empty__description {
    margin-top: 10px;
}
/deep/.el-empty__description p {
    line-height: 0;
}
</style>
