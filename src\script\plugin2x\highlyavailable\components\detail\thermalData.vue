<!--
 * @Date: 2025-02-11 11:34:01
 * @LastEditors: liurong <EMAIL>
 * @LastEditTime: 2025-08-06 16:38:38
 * @FilePath: \mtex-static-highlyavailable\src\script\plugin2x\highlyavailable\components\detail\thermalData.vue
-->
<template>
    <div class="thermalData-wrapper">
        <searchBar :fields="fields" :form="form" :isLayout="false">
            <el-button
                class="highlyavailable-primary-btn"
                type="primary"
                size="small"
                @click="search()"
                >查询</el-button
            >
        </searchBar>
        <dataTable
            class="dataTable"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :total="total"
            stripe
            border
            :updateTable="getTableData"
        >
        </dataTable>
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import { fields, tableColumns } from '@/script/constant/thermalData.js';
import dayjs from 'dayjs';
export default {
    name: 'thermalData',
    components: {
        searchBar,
        dataTable,
    },
    data() {
        return {
            form: {
                time: [],
                gridId: '',
                // gridName: '',
            },
            fields,
            columns: tableColumns,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15,
            },
            total: 0,
        };
    },
    computed: {
        queryRoute() {
            return this.$route.query;
        },
    },
    created() {
        this.initData();
        this.search();
    },
    methods: {
        initData() {
            const { isHot, startTime, s2Id } = this.queryRoute;
            if (isHot) {
                // this.form.time = [startTime, endTime];
                this.form.gridId = s2Id;
            } else if (startTime) {
                // const endTime = dayjs(startTime).add(2, 'hour').format('YYYY-MM-DD HH:mm:ss');
                // this.form.time = [startTime, endTime];
            }
            const now = dayjs().format('YYYY-MM-DD HH:mm:00');
            const twoHoursAgo = dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:00');
            this.form.time = [twoHoursAgo, now];
        },
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            const { time } = this.form;
            const [startTime, endTime] = time || [];
            if (dayjs(endTime).diff(dayjs(startTime), 'hour') > 2) {
                this.$message.warning('时间范围不能超过2小时');
                return;
            }
            const { regionType, regionId, timeSize } = this.queryRoute;
            this.highGetPost(
                'monitorApi',
                'getCrowHotData',
                {
                    regionType,
                    timeSize,
                    regionId,
                    startTime,
                    endTime,
                    // s2IdList: gridId ? [gridId] : [],
                    pageNum: curPage,
                    pageSize,
                    modelList: {
                        modelId: '2',
                        modelParam: {
                            s2Level: appConfig.highS2Level || '17',
                        },
                    },
                },
                '高可用监控查询'
            ).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    const data = res.data;
                    const { primaryResult, standbyResult, totalNum } = data;

                    // 处理数据转换为表格所需格式
                    this.tableData = primaryResult.map((primary) => {
                        // 根据timeSpan匹配standby数据
                        const standby =
                            standbyResult.find((item) => item.timeSpan === primary.timeSpan) || {};

                        // 获取所有不重复的s2Id
                        const s2Ids = Array.from(
                            new Set([
                                ...(primary.regionData || []).map((item) => item.regionId),
                                ...(standby.regionData || []).map((item) => item.regionId),
                            ])
                        );

                        // 将每个s2Id的数据转换为一行表格数据
                        return s2Ids.map((s2Id) => {
                            const primaryRegion =
                                // eslint-disable-next-line no-ternary
                                primary.regionData && primary.regionData.length > 0
                                    ? primary.regionData.find((item) => item.regionId === s2Id) || {}
                                    : {};
                            const standbyRegion =
                                // eslint-disable-next-line no-ternary
                                standby.regionData && standby.regionData.length > 0
                                    ? standby.regionData.find((item) => item.regionId === s2Id) || {}
                                    : {};

                            return {
                                time: primary.timeSpan,
                                gridId: s2Id || '-',
                                mainLibraryGroup: primaryRegion.total || 0,
                                backupStorageGroup: standbyRegion.total || 0,
                            };
                        });
                    });

                    // 将二维数组展平为一维数组
                    this.tableData = this.tableData.flat();

                    this.total = totalNum;
                } else {
                    this.$message.warning(res.returnMsg);
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
.thermalData-wrapper {
    width: 100%;
    height: 100%;
    padding: 1.33rem;
    display: flex;
    flex-direction: column;
}
.dataTable {
    width: 100%;
    flex: 1;
    height: 0;
}
</style>
