<template>
    <div class="complaint-menu">
        <el-menu
            :default-active="activeIndex"
            class="el-menu-demo"
            mode="horizontal"
            text-color="#fff"
            active-text-color="#ECF245"
            @select="selectMenu"
        >
            <template v-for="item in menuList">
                <el-menu-item
                    v-if="!item.children"
                    :class="{ disabled: item.disabled }"
                    :key="item.value"
                    :index="item.value"
                >
                    {{ item.label }}
                </el-menu-item>
                <!-- 子菜单 -->
                <el-submenu
                    v-else
                    :index="item.value"
                    :key="`${item.label}-subItem`"
                    popper-class="custom-submenu"
                >
                    <template slot="title">{{ item.label }}</template>
                    <el-menu-item
                        v-for="(child, index) in item.children"
                        :index="child.value"
                        :key="index"
                        :disabled="child.disabled"
                    >
                        {{ child.label }}
                    </el-menu-item>
                </el-submenu>
            </template>
        </el-menu>
    </div>
</template>

<script>
import getMenuList from './constant';

export default {
    name: 'complaintMenu',
    data() {
        return {
            activeIndex: 'complaintBlacklist',
            menuList: getMenuList()
        };
    },
    watch: {},
    mounted() {
        const origin = window.location.href;
        const lastItem = origin.substring(origin.lastIndexOf('/') + 1);
        this.activeIndex = lastItem;
    },
    methods: {
        selectMenu(key) {
            this.$emit('jumpRouter', key);
        }
    }
};
</script>

<style lang="less" scoped>
.complaint-menu {
    height: 48px;
    line-height: 48px;

    /deep/ .el-menu {
        border-bottom: none;
        background: #1565ff !important;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.2);
        .el-menu-item,
        .el-submenu__title {
            font-size: 16px;
        }
        .el-menu-item.disabled {
            color: #c0c4cc !important;
            pointer-events: none;
        }
        .el-submenu__title i {
            color: #fff;
            font-size: 16px;
        }
    }
    /deep/ .el-menu--horizontal > .el-menu-item,
    /deep/ .el-menu--horizontal > .el-submenu .el-submenu__title {
        height: 45px;
        line-height: 48px;
        background-color: transparent !important;
        &:hover {
            background: rgb(31, 94, 195) !important;
        }
    }
}
</style>
