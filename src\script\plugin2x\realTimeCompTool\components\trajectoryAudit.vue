<template>
    <div class="trajectoryAudit">
        <searchBar ref="trajectoryAuditSearch" :fields="fields" :form="form">
            <el-button
                class="highlyavailable-primary-btn"
                type="primary"
                size="small"
                @click="search()"
                >查询</el-button
            >
        </searchBar>
        <div class="content">
            <gisCom id="trajectoryAudit" class="gis" @loaded="loaded"></gisCom>
            <div class="right">
                <div class="tab">
                    <div class="tab-item" :class="{ active: isActive === 1 }" @click="isActive = 1">
                        进入离开详单
                    </div>
                    <div class="tab-item" :class="{ active: isActive === 2 }" @click="isActive = 2">
                        驻留详单
                    </div>
                    <div class="tab-item" :class="{ active: isActive === 3 }" @click="isActive = 3">
                        未进入详单
                    </div>
                </div>
                <dataTable
                    class="dataTable"
                    :columns="columns"
                    :data="tableData"
                    :pagination="pagination"
                    :total="total"
                    stripe
                    :updateTable="getTableData"
                >
                </dataTable>
                <div class="statisticalData">
                    <div class="title">统计数据</div>
                    <overview :resList="overviewList" :span="24" :styleType="2" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import { fields, tableColumns } from '../constatnt/trajectoryAudit.js';
import dataTable from '_com/tables/dataTableLast.vue';
import gisCom from '_com/gisMap/index.vue';
import linePoint from '_com/gisMap/layers/linePoint.js';
import overview from '@/script/components/overView/overview.vue';
import { setFitView } from '@/script/utils/method.js';
export default {
    name: 'trajectoryAudit',
    components: {
        searchBar,
        dataTable,
        gisCom,
        overview
    },
    data() {
        return {
            fields: fields(),
            form: {
                complaintType: 0,
                phoneNumber: '',
                startTime: '',
                endTime: '',
                regionId: '',
                stayDuration: 10
            },
            columns: tableColumns['1'],
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            total: 0,
            overviewList: [
                {
                    count: 0,
                    name: '进入次数',
                    color: '#1a75ff',
                    unit: '次',
                    props: 'inSize'
                },
                {
                    count: 0,
                    name: '离开次数',
                    color: '#1a75ff',
                    unit: '次',
                    props: 'outSize'
                },
                {
                    count: 0,
                    name: '驻留时长',
                    color: '#1a75ff',
                    unit: '分钟',
                    props: 'stayTimeCnt'
                }
            ],
            isActive: 1
        };
    },
    watch: {
        isActive: {
            handler(newV) {
                this.columns = tableColumns[newV];
                this.search();
            }
        }
    },
    created() {
        this.getWhitePhone();
    },
    methods: {
        getWhitePhone() {
            this.highGetPost('realTimeToolSeviceApi', 'getWhitePhone', {}, '获取白名单号码').then(
                ({ data }) => {
                    this.fields = fields({
                        msisdn: data
                    });
                }
            );
        },
        search() {
            this.pagination.curPage = 1;
            this.getTableData(this.pagination);
        },
        async getTableData(pagination = {}) {
            await this.$refs['trajectoryAuditSearch'].$refs.searchBarRef.validate();
            const { curPage = 1, pageSize = 15 } = pagination;
            this.highGetPost(
                'realTimeToolSeviceApi',
                'analyTrajectoryDetail',
                {
                    ...this.form,
                    pageNum: curPage,
                    pageSize,
                    type: this.isActive
                },
                '用户电子围栏轨迹稽核查询'
            ).then(({ data }) => {
                this.tableData = data.list;
                this.total = data.pageTotal;
                this.overviewList.forEach((item) => {
                    item.value = data[item.props];
                });
                this.renderOutline(data.region);
                this.queryTrajectoryDetails();
            });
        },
        // 转化经纬度
        transCoors(coorsType, coors) {
            if (coorsType === 'BD09') {
                return this.g.math.bd09_To_Gps84(coors);
            } else if (coorsType === 'GCJ02') {
                return this.g.math.gcj02_To_Gps84(coors);
            }
            return coors;
        },
        renderOutline(region) {
            this.outlineLayer && this.outlineLayer.removeAll();
            if (!region) {
                return;
            }
            const { shapeType, circle, regionCoors, coorsType } = region;
            let g = this.g;
            let outline = null;
            if (shapeType === 1) {
                const coors = this.transCoors(coorsType, {
                    lat: circle.centerLatitude,
                    lng: circle.centerLongitude
                });
                let circleData = [
                    {
                        ...coors,
                        ht: 1,
                        color: 0x409eff,
                        radius: circle.radius,
                        angle: 360,
                        dir: 40
                    }
                ];
                g.meshList.circle.opacity = 0.4;
                outline = g.meshList.circle.create(circleData);
                g.cameraControl.move(coors);
                g.cameraControl.zoom = 15;
            } else {
                const rectData = regionCoors
                    .split(';')
                    .map((item) => item.split(','))
                    .map((item) => {
                        return this.transCoors(coorsType, {
                            lat: item[1],
                            lng: item[0]
                        });
                    });
                let regularData = g.math.lsRegular(rectData.map((item) => [item.lng, item.lat]));
                let data = [
                    { ls: regularData, ht: 0 * g.HM, layers: [{ maxH: 0, color: 0x409eff }] }
                ];
                g.meshList.plane.opacity = 0.4;
                outline = g.meshList.plane.create(data);
                setFitView(rectData, g, 2);
            }
            this.outlineLayer.add(outline);
            g.gis.needUpdate = true;
        },
        queryTrajectoryDetails() {
            this.highGetPost(
                'realTimeToolSeviceApi',
                'queryTrajectoryDetails',
                {
                    ...this.form,
                    phoneNumber: this.form.userPhone
                },
                '投诉用户指定时间轨迹详情查询'
            ).then(({ data }) => {
                const { list } = data;
                if (!list.length) {
                    return;
                }
                this.linePointLayer.createLinePoint(
                    [
                        {
                            points: list.map((item) => {
                                return {
                                    lat: item.lat,
                                    lng: item.lng,
                                    ht: 0
                                };
                            }),
                            color: 0x1664ff
                        }
                    ],
                    list.map((item) => {
                        return {
                            lat: item.lat,
                            lng: item.lng,
                            ht: 0.1,
                            width: 20,
                            color: 0x1664ff
                        };
                    }),
                    null,
                    2
                );
            });
        },
        loaded(g) {
            this.linePointLayer = linePoint(g, {
                name: '点连线',
                material: g.meshList.img.getMaterial({
                    url: require('../../../../img/gis/circle-point.png'),
                    opacity: 1
                })
            });
            this.g = g;
            this.outlineLayer = new g.layer();
            this.outlineLayer.name = '轮廓';
            g.gis.scene.add(this.outlineLayer);
            this.outlineLayer.visible = true;
        }
    }
};
</script>

<style lang="less" scoped>
.trajectoryAudit {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 1.33rem;
}
.content {
    width: 100%;
    flex: 1;
    display: flex;
    .gis {
        width: calc(70% - 10px);
        height: 100%;
        margin-right: 10px;
    }
    .right {
        width: 30%;
        height: 100%;
        display: flex;
        flex-direction: column;
        .tab {
            width: 100%;
            height: 40px;
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            &-item {
                padding: 10px;
                background: #fff;
                border-radius: 4px;
                border: 1px solid #9fcaff;
                color: #42506c;
                margin-right: 10px;
                cursor: pointer;
                &.active {
                    background: #f4f9fe;
                    color: #1a75ff;
                }
            }
        }
        .statisticalData {
            display: flex;
            flex-direction: column;
            .title {
                font-size: 16px;
                font-weight: bold;
                margin: 10px 0;
            }
        }
        /deep/.overview__main .data .count {
            padding-left: 120px;
        }
    }
}
</style>
