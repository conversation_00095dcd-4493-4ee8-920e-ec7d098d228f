<template>
    <div class="legend">
        <div class="title">图例</div>
        <div class="legend-item">
            <img class="legend-item-icon" src="@/img/gis/innerStationIcon.png" />
            <span class="legend-item-text">轨迹线路上基站</span>
        </div>
        <div class="legend-item">
            <img class="legend-item-icon" src="@/img/gis/outStationIcon.png" />
            <span class="legend-item-text">区域外基站</span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'legend',
};
</script>

<style lang="less" scoped>
.legend {
    position: absolute;
    left: 16px;
    bottom: 16px;
    padding: 10px 12px;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    .title {
        margin-bottom: 4px;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
    }
    .legend-item {
        display: flex;
        align-items: center;
        height: 24px;
        .legend-item-icon {
           
        }
        .legend-item-text {
            margin-left: 6px;
            font-weight: 400;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
        }
    }
}
</style>
