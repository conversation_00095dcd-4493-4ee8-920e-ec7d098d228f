<template>
    <el-dialog
        class="approval-Order"
        title="审批"
        :visible="visible"
        width="400px"
        :close-on-click-modal="false"
        :append-to-body="true"
        destroy-on-close
        @close="handleClose"
    >
        <searchBar ref="searchBar" class="search-bar" :fields="fields" :form="form" />
        <!-- approvalWorkOrder -->
        <div slot="footer">
            <el-button size="mini" @click="handleClose">取消</el-button>
            <el-button type="primary" size="mini" @click="sure">确定</el-button>
        </div>
    </el-dialog>
</template>
<script>
import searchBar from '_com/searchForm/index.vue';
import { addFields } from '../constants';
export default {
    name: 'approval-Order',
    components: {
        searchBar
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        row: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            form: {
                status: '',
                approvalOpn: ''
            }
        };
    },
    computed: {
        fields() {
            return addFields;
        }
    },
    created() {},
    methods: {
        async addRule() {
            const valid = await this.$refs.searchBar.validForm();
            if (!valid) {
                return this.$message.warning('存在必填项未填写');
            }
            this.highGetPost(
                'newComplaintToolApi',
                'approvePermission', // 请确认实际接口方法名
                {
                    id: this.row.id,
                    ...this.form
                },
                '权限审批'
            ).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg);
                    this.$emit('updateTable');
                    this.handleClose();
                } else {
                    this.$message.warning(res.returnMsg);
                }
            });
        },
        async sure() {
            this.addRule();
        },
        handleClose() {
            this.$emit('update:visible', false);
        }
    }
};
</script>
<style lang="less" scoped>
@import url('../../../../../style/dialog.less');
.approval-Order {
    height: 100%;
    .search-bar {
        padding: 0 4px;
        margin-top: 20px;
    }
}
</style>
