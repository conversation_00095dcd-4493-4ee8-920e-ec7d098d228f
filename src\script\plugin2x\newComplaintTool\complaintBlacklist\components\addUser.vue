<template>
    <el-dialog
        class="add-user"
        :title="`${typeLabel}`"
        :visible="visible"
        width="400px"
        :close-on-click-modal="false"
        :append-to-body="true"
        destroy-on-close
        @close="handleClose"
    >
        <searchBar ref="searchBar" class="search-bar" :fields="fields" :form="form" />
        <!--  -->
        <div slot="footer">
            <el-button size="mini" @click="handleClose">取消</el-button>
            <el-button type="primary" size="mini" @click="sure">确定</el-button>
        </div>
    </el-dialog>
</template>
<script>
import searchBar from '_com/searchForm/index.vue';
import { addUserFields } from '../constants';
export default {
    name: 'add-user',
    components: {
        searchBar
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        row: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            form: {
                msisdn: '',
                isValid: 1
            }
        };
    },
    computed: {
        userInfo() {
            return frameService.getUser();
        },
        fields() {
            return addUserFields;
        },
        isEdit() {
            return Boolean(Object.keys(this.row || {}).length);
        },
        typeLabel() {
            return this.isEdit ? '修改' : '新增';
        }
    },
    created() {
        if (this.isEdit) {
            this.initData();
        }
    },
    methods: {
        initData() {
            Object.assign(this.form, {
                msisdn: this.row.msisdn,
                isValid: this.row.isValid
            });
        },
        async addRule() {
            const valid = await this.$refs.searchBar.validForm();
            if (!valid) {
                return this.$message.warning('存在必填项未填写');
            }
            this.highGetPost(
                'newComplaintToolApi',
                'addBlackWhiteRoster', // 请确认实际接口方法名
                {
                    ...this.form
                },
                '新增投诉黑名单'
            ).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg);
                    this.$emit('updateTable');
                    this.handleClose();
                } else {
                    this.$message.warning(res.returnMsg);
                }
            });
        },
        async editRule() {
            const valid = await this.$refs.searchBar.validForm();
            if (!valid) {
                return this.$message.warning('存在必填项未填写');
            }
            this.highGetPost(
                'newComplaintToolApi',
                'modifyBlackWhiteRoster', // 请确认实际接口方法名
                {
                    id: this.row.id,
                    ...this.form
                },
                '修改投诉黑名单'
            ).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg);
                    this.$emit('updateTable');
                    this.handleClose();
                } else {
                    this.$message.warning(res.returnMsg);
                }
            });
        },
        async sure() {
            if (this.isEdit) {
                this.editRule();
            } else {
                this.addRule();
            }
        },
        handleClose() {
            this.$emit('update:visible', false);
        }
    }
};
</script>
<style lang="less" scoped>
@import url('../../../../../style/dialog.less');
.add-user {
    height: 100%;
    .search-bar {
        padding: 0 4px;
        margin-top: 20px;
    }
}
</style>
