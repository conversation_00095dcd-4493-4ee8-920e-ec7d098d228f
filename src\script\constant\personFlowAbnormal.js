const fields = (mapOpts = {}) => {
    return [
        {
            prop: 'chName',
            label: '省份:',
            labelWidth: '50px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
            },
            slot: {
                element: 'el-option',
                enums: mapOpts.chName.map(i => ({ label: i.label, value: i.label })),
            },

            span: 3,
        },
        {
            prop: 'createTime',
            label: '创建时间:',
            labelWidth: '80px',
            element: 'el-date-picker',
            bind: {
                clearable: true,
                type: 'datetimerange',
                'range-separator': '-',
                'start-placeholder': '开始时间',
                'end-placeholder': '结束时间',
                format: 'yyyy-MM-dd HH:00:00',
                'value-format': 'yyyyMMddHH',
            },
            span: 8,
        },
        {
            span: 13
        }
    ];
};

const tableColumns = [
    {
        prop: 'time',
        label: '时间',
    },
    {
        prop: 's2Id',
        label: '栅格S2ID',
    },
    {
        prop: 'number',
        label: '人数',
    },
    {
        prop: 's2IdBase',
        label: '栅格S2ID基准值',
    },
    {
        prop: 'numberRate',
        label: '人数波动率(%)',
    },
    {
        prop: 'operation',
        label: '操作',
    },
];

export {
    fields,
    tableColumns,
};