/*
 * @Date: 2025-07-03 09:00:02
 * @LastEditors: liurong <EMAIL>
 * @LastEditTime: 2025-07-31 20:17:08
 * @FilePath: \mtex-static-knowledgebased:\project\mtex-static-highlyavailable\src\script\api\module\newComplaintTool.js
 */
import norMalConfig from '../normal-config';
import { fileConfig, norMalConfigGet } from '../normal-config';

const _url = '/newComplaintTool';
const newComplaintTool = {
    newComplaintToolApi: {
        /* 新投诉应用黑名单列表 */
        // 投诉黑名单列表
        getblackWhiteRosterList(params) {
            return norMalConfig(`${_url}/blackWhiteRoster/pageList`, params);
        },
        // 新增投诉黑名单
        addBlackWhiteRoster(params) {
            return norMalConfig(`${_url}/blackWhiteRoster/add`, params);
        },
        // 修改投诉黑名单
        modifyBlackWhiteRoster(params) {
            return norMalConfig(`${_url}/blackWhiteRoster/modify`, params);
        },
        // 删除投诉黑名单
        delBlackWhiteRoster(params) {
            return norMalConfig(`${_url}/blackWhiteRoster/remove`, params);
        },
        /* 数据查询申请 */
        // 数据查询申请列表
        getPermissionList(params) {
            return norMalConfig(`${_url}/permission/list`, params);
        },
        // 权限删除
        delPermission(params) {
            return norMalConfig(`${_url}/permission/del`, params);
        },
        // 权限审批
        approvePermission(params) {
            return norMalConfig(`${_url}/permission/approve`, params);
        },
        //权限申请(新建)
        addPermission(params) {
            return fileConfig(`${_url}/permission/add`, params);
        },
        //权限修改
        updatePermission(params) {
            return fileConfig(`${_url}/permission/update`, params);
        },
        //附件下载
        downloadFile(params) {
            return norMalConfigGet(`${_url}/permission/download`, params, {
                responseType: 'blob'
            });
        },
        //数据查询应用统计
        getStatisticsData(params) {
            return norMalConfig(`${_url}/inspect/data/applied/statistics/query`, params);
        },
        getAreaOptions(params) {
            return norMalConfig(`${_url}/inspect/iop/region/query`, params);
        },
        getBaseStations(params) {
            return norMalConfig(`${_url}/inspect/queryPageDetails`, params);
        },
        getInAreaStations(params) {
            return norMalConfig(`${_url}/inspect/queryBaseStationList`, params);
        }
    }
};
export default newComplaintTool;
