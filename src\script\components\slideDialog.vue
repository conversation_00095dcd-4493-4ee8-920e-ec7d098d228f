<template>
    <transition name="fade">
        <div class="slide-dialog" :style="{ [direction]: 0 }">
            <div class="slide-dialog__content" :style="contentStyle">
                <slot></slot>
            </div>
            <img
                class="slide-dialog__icon"
                :style="{ [iconDirection]: '-13px' }"
                :src="getIcon"
                @click="handleIcon"
            />
        </div>
    </transition>
</template>

<script>
import foldIcon from '@/img/icon/fold.png';
import unfoldIcon from '@/img/icon/expand.png';
export default {
    name: 'slide-dialog',
    props: {
        direction: {
            type: String,
            default: 'right',
        },
        width: {
            type: String,
            default: '450px',
        },
    },
    data() {
        return {
            isFold: false,
        };
    },
    computed: {
        getIcon() {
            if (this.direction === 'left') {
                return this.isFold ? foldIcon : unfoldIcon;
            }
            return this.isFold ? unfoldIcon : foldIcon;
        },
        iconDirection() {
            return {
                left: 'right',
                right: 'left',
            }[this.direction];
        },
        contentStyle() {
            if (this.isFold) {
                return {
                    width: 0,
                    padding: 0,
                };
            }
            return {
                width: this.width,
                padding: '20px',
            };
        },
    },
    methods: {
        handleIcon() {
            this.isFold = !this.isFold;
        },
    },
};
</script>

<style lang="less" scoped>
.slide-dialog {
    position: absolute;
    top: 0;
    height: 100%;
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(0px);
    box-sizing: border-box;
    &__content {
        height: 100%;
        overflow: auto;
        width: 100%;
    }
    &__icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
    }
}
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
    opacity: 0;
}
</style>
