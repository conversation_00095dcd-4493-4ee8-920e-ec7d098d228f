<template functional>
    <el-row class="overview" :gutter="props.gutter">
        <el-col
            v-for="(
                { icon, count, name, tooltip, color, unit, iconStyle, cardStyle }, index
            ) in props.resList"
            :key="index"
            :span="props.span"
        >
            <div class="overview__main" :style="cardStyle">
                <img class="image" :src="icon" :style="iconStyle" v-if="icon" />
                <div class="data" :class="`data-${props.styleType}`">
                    <el-tooltip class="item" effect="dark" :content="tooltip" placement="top">
                        <div class="name">{{ name }}</div>
                    </el-tooltip>
                    <span class="count" :style="{ color }">
                        {{ count }}
                        <span class="unit" :style="{ color }" v-if="unit">{{ unit }}</span>
                    </span>
                </div>
            </div>
        </el-col>
    </el-row>
</template>

<script>
export default {
    name: 'overview',
    props: {
        resList: {
            type: Array,
            default: () => []
        },
        span: {
            type: Number,
            default: 6
        },
        gutter: {
            type: Number,
            default: 18
        },
        styleType: {
            type: Number,
            default: 1 // 1: 上下, 2: 左右
        }
    }
};
</script>

<style lang="less" scoped>
.overview {
    .el-col {
        margin-bottom: 18px;
    }
    &__main {
        display: flex;
        padding: 14px 14px;
        // height: 94px;
        background: #f4f9fe;
        border-radius: 4px;
        border: 1px solid #9fcaff;

        .image {
            border: 1px dashed #cccccc;
            width: 60px;
            height: 60px;
        }
        .data {
            margin-left: 12px;
            flex: 1;
            width: 0;
            display: flex;
            .count {
                vertical-align: baseline;
                font-weight: bold;
                font-family: MicrosoftYaHeiSemibold;
                font-size: 28px;
                color: #1a75ff;
                line-height: 32px;
                text-align: left;
                font-style: normal;
                .unit {
                    vertical-align: baseline;
                    font-weight: 400;
                    font-family: MicrosoftYaHei;
                    font-size: 20px;
                    color: #5a8beb;
                    line-height: 32px;
                    text-align: left;
                    font-style: normal;
                }
            }
            .name {
                display: inline-block;
                font-weight: bold;
                font-family: MicrosoftYaHeiSemibold;
                font-size: 20px;
                color: #42506c;
                line-height: 32px;
                text-align: right;
                font-style: normal;
            }
        }

        .data-1 {
            flex-direction: column;
            justify-content: space-around;
        }

        .data-2 {
            flex-direction: row;
            gap: 16px;
            flex-wrap: wrap;
            align-content: center;

            .name {
                margin: auto 0;
            }
        }
    }
}
.gray {
    color: #727579;
}
</style>
