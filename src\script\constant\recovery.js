import overviewIcon from '@/img/overviewicon.png';

const getOverview = (data = {}) => {
    const { countOfRecoveryByButton = 0, countOfRecovery = 0 } = data;
    return [
        {
            icon: overviewIcon,
            count: countOfRecoveryByButton,
            name: '故障一键恢复使用次数汇总统计：',
            color: '#1a75ff',
            unit: '次',
            tooltip: '用户点击切换主备库的次数统计'
        },
        {
            icon: overviewIcon,
            count: countOfRecovery,
            name: '故障一键恢复按业务使用次数汇总统计：',
            color: '#1a75ff',
            unit: '次',
            tooltip: '租户查询服务时，api自动切换的次数统计'
        }
    ];
};

const fields = () => {
    return [
        {
            prop: 'time',
            label: '时间:',
            labelWidth: '46px',
            element: 'el-date-picker',
            bind: {
                clearable: true,
                type: 'datetimerange',
                'range-separator': '-',
                'start-placeholder': '开始日期',
                'end-placeholder': '结束日期',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss'
            },
            span: 8
        },
        {
            span: 16
        }
    ];
};
const tableColumns = [
    {
        prop: 'time',
        label: '时间'
    },
    {
        prop: 'totalUserModel',
        label: '总人数模型'
    },
    {
        prop: 'flowModel',
        label: '人流模型'
    },
    {
        prop: 'portraitModel',
        label: '画像模型'
    },
    {
        prop: 'heatModel',
        label: '热力模型'
    },
    {
        prop: 'visitsModel',
        label: '累计到访模型'
    },
    {
        prop: 'residentModel',
        label: '常驻分布模型'
    },
    {
        prop: 'stayDurationCount',
        label: '驻留时长'
    }
];
export { fields, tableColumns, getOverview };
