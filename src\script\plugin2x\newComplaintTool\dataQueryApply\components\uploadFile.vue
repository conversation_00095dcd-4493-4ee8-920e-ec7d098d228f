<template>
    <div class="upload-file" :class="{ 'h-32': fileList.length === 0 }">
        <div class="upload-file__upload">
            <el-upload
                ref="uploadRef"
                class="upload"
                action=""
                accept=".png,.jpg,.jpeg,.pdf"
                :before-upload="handleBefore"
                :on-success="handleSuccess"
                :on-remove="handleRemove"
                :file-list="fileList"
                :http-request="handleRequest"
                :headers="headers"
                auto-upload
                drag
            >
                <el-button class="upload-btn" size="small" icon="el-icon-upload">上传</el-button>
            </el-upload>
        </div>
    </div>
</template>

<script>
export default {
    name: 'upload-file',
    props: {
        value: {
            type: Array,
            default: () => [[]]
        }
    },
    model: {
        prop: 'value',
        event: 'change'
    },
    data() {
        return {
            file: {},
            headers: remoteAjaxUtil.getReplayAttacksHeader()
        };
    },
    computed: {
        fileList: {
            get() {
                return this.value;
            },
            set(newValue) {
                this.$emit('change', newValue);
            }
        }
    },
    methods: {
        handleRequest(option) {
            option.onSuccess({}, option.file);
        },
        handleBefore(file) {
            // 校验
            const filename = file.name;
            if (!/\.png$|\.jpg$|\.jpeg$|\.pdf$|\.doc$|\.docx$/.test(filename.toLowerCase())) {
                this.$message.warning('文件上传格式不正确，请上传pdf、word、png、jpg、jpeg格式');
                return false;
            }
            // 文件大小不超过10M校验
            if (file.size > 10 * 1024 * 1024) {
                this.$message.warning('文件大小不超过10M');
                return false;
            }
            return true;
        },
        handleSuccess(res, file) {
            const fileList = [...this.fileList, file];
            this.$emit('change', fileList);
        },
        handleRemove(file) {
            const uid = file.uid;
            const fileList = this.fileList.filter((item) => item.uid !== uid);
            this.$emit('change', fileList);
        }
    }
};
</script>

<style lang="less" scoped>
.upload-file {
    &__upload {
        padding: 0;
        .upload {
            height: auto;
            /deep/ .el-upload {
                width: 100%;
                .el-upload__input {
                    display: none;
                }
                .el-upload-dragger {
                    width: 100%;
                    height: auto;
                    border: none;
                    border-radius: 0;
                    text-align: left;
                }
            }
            /deep/ .el-upload-list {
                .el-upload-list__item {
                    margin-top: 4px;
                    padding: 0 6px;
                    width: 100%;
                    background: #f6f8fa;
                    border-radius: 2px;
                    line-height: 28px;
                }
                .el-upload-list__item-name {
                    margin-right: 20px;
                    text-align: left;
                    word-break: break-all;
                    white-space: normal;
                }
            }
            .upload-btn /deep/ .el-icon-upload {
                font-size: 14px;
                color: #c0c4cc;
                margin: 0;
                line-height: 12px;
            }
        }
    }
    &.h-32 {
        height: 32px;
    }
}
.blue {
    color: #0091ff;
    cursor: pointer;
    text-decoration: underline;
    font-weight: 500;
}
</style>
