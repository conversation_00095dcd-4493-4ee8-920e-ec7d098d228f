import dayjs from 'dayjs';
const formatAreas = (srcData) => {
    const result = [];
    let provinceObj = {};
    let cityObj = {};
    for (const {
        provinceName,
        provinceCode,
        districtName,
        districtCode,
        cityName,
        cityCode
    } of srcData) {
        // set province
        if (provinceObj.label !== provinceName) {
            provinceObj = {
                label: provinceName,
                value: provinceCode,
                children: []
            };
            result.push(provinceObj);
        }
        // set city
        if (cityObj.label !== cityName) {
            cityObj = {
                label: cityName,
                value: cityCode,
                children: []
            };
            provinceObj.children.push(cityObj);
        }
        // set district
        cityObj.children.push({
            label: districtName,
            value: districtCode
        });
    }
    return result;
};
const getAreasToCity = (srcDistricts = [], isNeedAllSelect = false) => {
    const result = [];
    for (const { value, label, children } of srcDistricts) {
        const newItem = {
            label,
            value,
            children: isNeedAllSelect
                ? [
                      {
                          label: '全部',
                          value: `${value}-all`
                      }
                  ]
                : []
        };
        result.push(newItem);
        for (const { value, label } of children) {
            newItem.children.push({
                label,
                value
            });
        }
    }
    return result;
};
const throttle = function (func, throttleMs) {
    let lastExecTime = 0;
    return function (...args) {
        const currentTime = Date.now();
        if (currentTime - lastExecTime >= throttleMs) {
            func.apply(this, args);
            lastExecTime = currentTime;
        }
    };
};
// eslint-disable-next-line complexity
const generateTimeSlots = (
    startTime = '2023-01-01 00:00:00',
    endTime = '2023-01-01 23:55:00',
    interval = 5
) => {
    // 验证
    if (typeof interval !== 'number' || interval < 1 || !Number.isInteger(interval)) {
        throw new Error('Interval must be a positive integer');
    }

    // 解析开始和结束时间
    const start = dayjs(startTime);
    const end = dayjs(endTime);

    if (!start.isValid() || !end.isValid()) {
        console.error('Invalid date format. Use YYYY-MM-DD HH:mm:ss');
        return [];
    }

    if (start.isAfter(end)) {
        console.error('Start time must be earlier than end time');
        return [];
    }

    // 处理天粒度的特殊情况 (interval = 1440，即24小时)
    if (interval === 1440) {
        const times = [];
        let current = start;

        while (current.isSame(end) || current.isBefore(end)) {
            // 确保天粒度的时间格式为 YYYY-MM-DD 00:00:00
            times.push(current.format('YYYY-MM-DD 00:00:00'));
            current = current.add(1, 'day');
        }

        return times;
    }

    // 确保时间是按照间隔对齐的（非天粒度情况）
    const startMinutes = start.minute();
    if (startMinutes % interval !== 0 && interval < 1440) {
        console.error(`Start time minutes must be in ${interval}-minute increments`);
        return [];
    }

    const endMinutes = end.minute();
    if (endMinutes % interval !== 0 && interval < 1440) {
        console.error(`End time minutes must be in ${interval}-minute increments`);
        return [];
    }

    const times = [];
    let current = start;

    while (current.isSame(end) || current.isBefore(end)) {
        times.push(current.format('YYYY-MM-DD HH:mm:ss'));
        current = current.add(interval, 'minute');
    }

    return times;
};
/**
 * 将时间格式化为最接近的5分钟的倍数
 * @param {string|Date} date - 需要格式化的日期
 * @param {string} direction - 格式化方向 'up' 向上取整 'down' 向下取整
 * @param {string} format - 返回的日期格式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
const roundToNearestFiveMinutes = (date, direction = 'down', format = 'YYYY-MM-DD HH:mm:ss') => {
    const d = dayjs(date);
    const minutes = d.minute();
    const remainder = minutes % 5;

    let adjustedMinutes;
    if (direction === 'up') {
        adjustedMinutes = minutes + (5 - remainder);
    } else {
        adjustedMinutes = minutes - remainder;
    }

    return d.minute(adjustedMinutes).second(0).format(format);
};
const getPieces = (tarArr, piece) => {
    if (tarArr.length <= piece) {
        return [tarArr];
    }
    const res = [];
    let curArr = tarArr;
    while (curArr.length > piece) {
        res.push(curArr.slice(0, piece));
        curArr = curArr.slice(piece);
    }
    res.push(curArr);
    return res;
};
// 获取坐标群里的各个方向的极值点
const getCoordinateExtremum = (coordinates = []) => {
    if (coordinates.length <= 4) {
        return coordinates;
    }
    let minLat, minLng, maxLat, maxLng;
    for (const item of coordinates) {
        if (!minLat || item.lat < minLat.lat) {
            minLat = item;
        }
        if (!maxLat || item.lat > maxLat.lat) {
            maxLat = item;
        }
        if (!minLng || item.lng < minLng.lng) {
            minLng = item;
        }
        if (!maxLng || item.lng > maxLng.lng) {
            maxLng = item;
        }
    }
    return [minLat, maxLat, minLng, maxLng];
};
/**
 * 获取图例范围和对应颜色（使用对象）
 * @param {number} max - 最大数值
 * @returns {Object} 返回范围和颜色的映射对象
 */
const getLegendRanges = (max) => {
    // 处理特殊情况
    if (max === 0) return { '0-0': '#00BE42' };

    // 计算等分值
    let step = max / 4;

    // 根据最大值判断是否需要向上取整或保留小数
    if (max >= 10) {
        step = Math.ceil(step);
    } else {
        step = Number(step.toFixed(2));
    }

    return {
        [`${step * 3}-${max + 1}`]: '#FF3333',
        [`${step * 2}-${step * 3}`]: '#FFA040',
        [`${step}-${step * 2}`]: '#FFFF02',
        [`0-${step}`]: '#00BE42'
    };
};
/**
 * 将时间按指定间隔进行切片
 * @param {string} startTime - 开始时间，格式为 'yyyy-MM-dd HH:mm:ss'
 * @param {string} endTime - 结束时间，格式为 'yyyy-MM-dd HH:mm:ss'
 * @param {number} interval - 切片间隔，单位为小时，默认为2
 * @returns {string[][]} 返回按序排列的时间切片，二维数组形式
 */
const generateTimeSlices = (startTime, endTime, interval = 2) => {
    const result = [];
    let current = dayjs(startTime);
    const end = dayjs(endTime);

    if (!current.isValid() || !end.isValid()) {
        console.error('Invalid date format. Use YYYY-MM-DD HH:mm:ss');
        return [];
    }

    if (current.isAfter(end)) {
        console.error('Start time must be earlier than end time');
        return [];
    }

    while (current.isBefore(end)) {
        const sliceEnd = current.add(interval, 'hour');
        if (sliceEnd.isBefore(end) || sliceEnd.isSame(end)) {
            result.push([
                current.format('YYYY-MM-DD HH:mm:ss'),
                sliceEnd.format('YYYY-MM-DD HH:mm:ss')
            ]);
            current = sliceEnd;
        } else {
            result.push([current.format('YYYY-MM-DD HH:mm:ss'), end.format('YYYY-MM-DD HH:mm:ss')]);
            break;
        }
    }

    return result;
};
// 将格式为类似"2025042215"的时间转为"2025-04-22 15:00:00"，自动补零
const timeFormat = (time) => {
    if (!time) return '';
    // 移除所有非数字字符
    const digits = time.toString().replace(/\D/g, '');
    if (digits.length < 8) return time;
    const year = digits.substring(0, 4);
    const month = digits.substring(4, 6);
    const day = digits.substring(6, 8);
    const hour = digits.length >= 10 ? digits.substring(8, 10) : '00';
    const minute = digits.length >= 12 ? digits.substring(10, 12) : '00';
    const second = digits.length >= 14 ? digits.substring(12, 14) : '00';
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};

// 地图适配大小
const setFitView = (data, g, size) => {
    let GIS = g;
    //得到最大最小经纬度
    let points = data;
    //计时2s后执行
    const timer = setTimeout(() => {
        //根据最大最小值计算GIS渲染的半径以及中心点；
        let easy = GIS.cameraControl.computeZoomByPoints(points, size || 1);

        //返回的数据中已经存在radius（半径）以及target（中心点），因为缩放需要的字段名称是targetPoint，这里赋值一下；
        easy.targetPoint = easy.target;

        //根据半径中心点进行缩放（可能是放大，可能是缩小，可用于制作钻取效果）
        GIS.cameraControl.gradualChangeRadius(easy);

        clearTimeout(timer);
    }, 1000);
};

export {
    formatAreas,
    getAreasToCity,
    throttle,
    generateTimeSlots,
    getPieces,
    getCoordinateExtremum,
    getLegendRanges,
    roundToNearestFiveMinutes,
    generateTimeSlices,
    timeFormat,
    setFitView
};
