
/**
 * mtex-static-templ-plugin 模板工程
 */

import routerItems from './routerItems';
import { ramsCoreDefInit } from 'mtex-rams-core';
import store from '../store/index';
import commonMixins from '@/script/mixins/commonMixins.js';
import highlyavailableCompExt from '../common/baseCompExt.js';
import '../../style/index.less';
import '@/style/public.less';

ramsCoreDefInit();

//Plugin应用模块的混入
const pluginMixins = [];
global_constant.mounted.push((Vue) => {
    window.highlyavailableCompExt = highlyavailableCompExt;
    Vue.use({
        install ($vue, store) {
            $vue.mixin(commonMixins);
        },
    });
});
// vuex混入
if (!global_constant.store) {
    global_constant.store = [];
}
global_constant.store.push(store);

global_constant.scrollStyle = {
    useDefault: false, //是否使用浏览器默认滚动条（只有为false的时候，其它属性才生效）
    width: 10, //滚动条宽度
    trackRadius: '0px', //滚动条轨道的圆角
    trackColor: 'rgb(240, 240, 240, 0.6)', //滚动条轨道的颜色
    thumbRadius: '4px', //滚动条滑块的圆角
    thumbColor: 'rgba(0, 0, 0, 0.2)', //滚动条滑块的颜色
};

for (let i = 0; i < routerItems.length; i++) {
    let routeItem = routerItems[i];
    if (!routeItem.component && !routeItem.jsload) {
        let routeMutiple = routeItem.multiple || false;
        routeItem.component = loadView(routeItem.path, routeMutiple);
    }
    global_constant.routes.push(routeItem);
}
const loadModule = (module, multiple) => {
    let moduleName = insideModuleGetName();
    let moduleMixin = insideModuleGetMixin();
    if (multiple) {
        let _module = $.extend(true, {}, module).default;
        if (!_module.mixins) {
            _module.mixins = [];
        }
        _module.mixins.push(moduleMixin);
        _module.mixins.push(...pluginMixins);
        _module.name = moduleName;
        return _module;
    }
    if (module.default.name != 'js_others') {
        if (!module.default.mixins) {
            module.default.mixins = [];
        }
        module.default.mixins.push(moduleMixin);
        module.default.mixins.push(...pluginMixins);
        module.default.name = moduleName;
    }
    return module;
};

export function loadView (subPath, multiple) {
    return () => {
        systemUtil.popupLoading(true, null, '正在加载应用文件，请稍候...');
        return import( /* webpackChunkName: "view-[request]" */ `../plugin2x/${subPath}Plugin.vue`)
            .then(module => {
                systemUtil.popupLoading(false, null);
                if (frameService.getUser()) {
                    return window.highlyavailableCompExt.initTenant(frameService.getUser().name).then((res) => {
                        if (!res.data) {
                            let title = '当前账号未绑定租户信息，请联系管理员添加!';
                            let code = res.returnCode;
                            if (code && code === '999999') {
                                // 超时设置
                                title = res.returnMessage;
                            } else {
                                window.unBindingTenant = true;
                            }
                            systemUtil.popupMessage('提示', title);
                        }
                        const data = loadModule(module, multiple);
                        return data;
                    });
                }
                const data = loadModule(module, multiple);
                return data;

            }).catch(err => {
                insideModuleLoadError(err);
            });
    };
}