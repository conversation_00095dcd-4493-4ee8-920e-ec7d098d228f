// 状态枚举
const fields = [
    {
        prop: 'msisdn',
        label: '用户号码:',
        element: 'el-input',
        bind: {
            placeholder: '请输入',
            clearable: true
        },
        span: 5
    },
    {
        prop: 'startTime',
        label: '开始时间:',
        element: 'el-date-picker',
        bind: {
            placeholder: '请选择',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss'
        },
        span: 4
    },
    {
        prop: 'endTime',
        label: '结束时间:',
        element: 'el-date-picker',
        bind: {
            placeholder: '请选择',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss'
        },
        span: 4
    },
    {
        span: 11
    }
];

// 表格列配置
const tableColumns = [
    {
        prop: 'id',
        label: '字段ID'
    },
    {
        prop: 'createUserId',
        label: '租户ID'
    },
    {
        prop: 'userName',
        label: '用户账户'
    },
    {
        prop: 'msisdn',
        label: '用户号码'
    },
    {
        prop: 'isValid',
        label: '是否生效'
    },
    {
        prop: 'createTime',
        label: '创建时间'
    },
    {
        prop: 'lastUpdateTime',
        label: '最后修改时间'
    },
    {
        prop: 'operation',
        label: '操作',
        width: 100
    }
];

const addUserFields = [
    {
        prop: 'msisdn',
        label: '用户号码:',
        element: 'el-input',
        bind: {
            placeholder: '请输入',
            clearable: true
        },
        rules: [{ required: true, message: '请输入', trigger: 'blur' }]
    },
    {
        prop: 'isValid',
        label: '是否生效:',
        element: 'el-select',
        bind: {
            placeholder: '请选择'
        },
        rules: [{ required: true, message: '请选择', trigger: 'change' }],
        slot: {
            element: 'el-option',
            enums: [
                { label: '是', value: 1 },
                { label: '否', value: 0 }
            ]
        }
    }
];

export { tableColumns, fields, addUserFields };
