---
description: 
globs: 
alwaysApply: true
---
### 通用规则
1. 默认情况下，所有回复都必须是中文，而且需要在开头称呼用户为"Coder：" 
2. 复杂需求拆解成小任务，分步实现，每完成一个小任务后再继续  
3. 代码实现前后要仔细检查，确保类型定义完整、组件 props 正确  
4. 在已有功能基础上添加新功能时，必须确保：  
   - 不影响原有功能和组件复用性  
   - 不添加其他功能、代码、逻辑、文件、配置、依赖  
5. 遵循项目架构设计，保持代码风格与 TypeScript/ESLint 规范一致  
6. 组件设计遵循单一职责原则，不混合多个变更  
7. 在进行组件设计规划时，符合"第一性原理"  
8. 在代码实现时，符合"KISS原则"和"SOLID原则"  
9. 优先使用现有组件库和 hooks，避免重复代码  
10. 不引入不必要的依赖，优先使用项目已有库  
11. 确保代码可读性，复杂逻辑添加注释，组件 props 类型详细定义  
12. 代码变更范围最小化，避免修改公共组件、全局状态  
13. 实现后进行基本逻辑自检，确保状态管理与生命周期正确  
14. 如有疑问，先询问再修改，不要擅自改变组件 API 设计  
17. 执行影响较大的修改前，自动检测组件依赖关系，分析影响范围  
 
### 代码质量优化
18. 代码生成后，自动优化（移除未使用 imports、合并重复样式）  
19. 对可能影响性能的代码（如不必要的重渲染、大型循环）提供优化建议  
20. 确保异常处理和加载状态管理，防止白屏和渲染错误  
 
### 架构感知
21. 优先分析现有组件库与状态管理模式，避免创建冗余组件  
22. 如遇架构不清晰，先梳理组件层次与数据流，再执行修改  

 