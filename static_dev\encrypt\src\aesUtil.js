import { JSEncrypt } from '../jsencrypt/lib/JSEncrypt'
import CryptoJS from 'crypto-js'
let _key
let _iv

function encryptAES(param) {
  let { key, iv, PublicKey: publicKey } = appConfig
  key = getAes<PERSON>ey(key, publicKey)
  iv = getAesIv(iv, publicKey)
  key = CryptoJS.enc.Utf8.parse(key) //十六位十六进制数作为密钥
  iv = CryptoJS.enc.Utf8.parse(iv) //十六位十六进制数作为密钥偏移量
  let srcs = CryptoJS.enc.Utf8.parse(param)
  let encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  })
  return encrypted.toString()
}

function getAesKey(key, publicKey) {
  if (!_key) {
    _key = decryptRSA(key, publicKey)
  }
  return _key
}

function getAesIv(iv, publicKey) {
  if (!_iv) {
    _iv = decryptRSA(iv, publicKey)
  }
  return _iv
}

function encryptRSA(str, publicKey, keyGenSize) {
  const encrypt = new JSEncrypt({
    default_key_size: keyGenSize,
  })
  encrypt.setPrivateKey(getPrivateKey(publicKey))
  return encrypt.encryptPrivate(str)
}

function decryptRSA(encrypted, publicKey, keyGenSize = 2048) {
  const decrypt = new JSEncrypt({
    default_key_size: keyGenSize,
  })
  decrypt.setPublicKey(getPubKey(publicKey))
  return decrypt.decryptPublic(encrypted)
}

function encryptRSAByPublic(str, publicKey, keyGenSize = 2048) {
  const encrypt = new JSEncrypt({
    default_key_size: keyGenSize,
  })
  encrypt.setPublicKey(getPubKey(publicKey))
  return encrypt.encrypt(str)
}

function decryptRSAByPrivate(encrypted, privateKey, keyGenSize = 2048) {
  const decrypt = new JSEncrypt({
    default_key_size: keyGenSize,
  })
  decrypt.setPrivateKey(getPrivateKey(privateKey))
  return decrypt.decrypt(encrypted)
}

function getPubKey(key) {
  return `-----BEGIN PUBLIC KEY-----${key}-----END PUBLIC KEY-----`
}

function getPrivateKey(key) {
  return `-----BEGIN PRIVATE KEY-----${key}-----END PRIVATE KEY-----`
}

//AES解密
/* 
参数：
    result：string，（必填）需要解密的字符串
 */
function decryptAES(result) {
  let { key, iv, PublicKey: publicKey } = appConfig
  key = getAesKey(key, publicKey)
  iv = getAesIv(iv, publicKey)
  key = CryptoJS.enc.Utf8.parse(key) //十六位十六进制数作为密钥
  iv = CryptoJS.enc.Utf8.parse(iv) //十六位十六进制数作为密钥偏移量
  //先进行base64解密
  let base64string = CryptoJS.enc.Base64.parse(result.replace(/[\r\n]/g, '')) //解密之前先移除换行
  let base64Word = base64string.toString()
  let encryptedHexStr = CryptoJS.enc.Hex.parse(base64Word)
  let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
  let decrypt = CryptoJS.AES.decrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  })
  let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
  return decryptedStr.toString()
}

function encryptDES(str) {
  let { key, iv, PublicKey: publicKey } = appConfig
  key = getAesKey(key, publicKey)
  iv = getAesIv(iv, publicKey)
  let srcs = CryptoJS.enc.Utf8.parse(str)
  let encrypted = CryptoJS.DES.encrypt(srcs, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  })
  return encrypted.toString()
}

function decryptDES(result) {
  let { key, iv, PublicKey: publicKey } = appConfig
  key = getAesKey(key, publicKey)
  iv = getAesIv(iv, publicKey)
  let base64string = CryptoJS.enc.Base64.parse(result.replace(/[\r\n]/g, ''))
  let base64Word = base64string.toString()
  let encryptedHexStr = CryptoJS.enc.Hex.parse(base64Word)
  let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
  let decrypt = CryptoJS.DES.decrypt(srcs, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  })
  let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
  return decryptedStr.toString()
}

export default {
  encryptAES,
  decryptAES,
  encryptRSA,
  decryptRSA,
  encryptRSAByPublic,
  decryptRSAByPrivate,
  encryptDES,
  decryptDES
}
