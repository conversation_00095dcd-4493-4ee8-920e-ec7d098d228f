<template>
	<div class="detailData-wrapper">
		<searchBar
			:fields="fields"
			:form="form"
			:isLayout="false"
		>
			<el-button
				class="highlyavailable-primary-btn"
				type="primary"
				size="small"
				@click="search()"
			>查询</el-button>
		</searchBar>
		<dataTable
			class="dataTable"
			:columns="columns"
			:data="tableData"
			:pagination="pagination"
			:total="total"
			stripe
			border
			:updateTable="getTableData"
		>
			<template #time="{ row }">
				{{ timeFormat(row.time) }}
			</template>
		</dataTable>
	</div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import { fields, tableColumns } from '@/script/constant/detailData.js';
import { timeFormat } from '@/script/utils/method.js';
import dayjs from 'dayjs';

export default {
	name: 'detailData',
	components: {
		searchBar,
		dataTable,
	},
	data() {
		return {
			form: {
				time: null,
			},
			fields,
			columns: tableColumns,
			tableData: [],
			pagination: {
				curPage: 1,
				pageSize: 20,
			},
			total: 0,
		};
	},
	computed: {
		queryRoute() {
			return this.$route.query;
		},
	},
	mounted() {
		if (this.queryRoute.time) {
			const today = this.queryRoute.time.slice(0, 8);
			const start = today + '00';
			const end = dayjs(today).add(1, 'day').format('YYYYMMDD00');
			this.form.time = [start, end];
		}
		this.search();
	},
	methods: {
		timeFormat,
		search() {
			this.pagination.curPage = 1;
			this.getTableData();
		},
		getTableData(pagination = {}) {
			const { curPage = 1, pageSize = 15 } = pagination;
			let { time: queryTime, s2Id } = this.queryRoute;
			const { time } = this.form;
			if (time) queryTime = undefined; // 如果表单中有时间，就不使用路由中的时间查询
			const [startTime, endTime] = time || [];
			this.highGetPost(
				'positionServiceApi',
				'getAlarmDetails',
				{
					s2Id,
					startTime,
					endTime,
					time: queryTime,
					pageNumber: curPage,
					pageSize
				},
				'栅格人流异常告警服务详情查询'
			).then(({ data }) => {
				this.tableData = data.data.map((item) => {
					return {
						time: item.time,
						s2Id: item.s2Id,
						number: item.userCount,
						s2IdBase: item.baselineValue,
						numberRate: item.fluctuationRate,
					};
				});
				this.total = data.total;
			});
		},
	},
};
</script>

<style lang="less" scoped>
.detailData-wrapper {
	width: 100%;
	height: 100%;
	padding: 1.33rem;
	display: flex;
	flex-direction: column;
}
.dataTable {
	width: 100%;
	flex: 1;
	height: 0;
}
</style>
