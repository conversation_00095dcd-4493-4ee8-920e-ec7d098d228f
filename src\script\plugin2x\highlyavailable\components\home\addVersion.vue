<template>
  <el-dialog class="add-version" :title="formData.type == 'add' ? '新增版本' : '编辑版本'" top="80px" width="40%"
    :visible="visible" append-to-body destroy-on-close :close-on-click-modal="false" @close="handleClose">
    <!-- 主体 -->
    <div class="add-region__main">
      <searchBar :fields="fields" :form="form">
        <div slot="grayscaleValueList" style="width: 100%;">
          <div v-for="(item, index) in form.grayscaleValueList" :key="index"
            :class="{ 'grayscale-value-item': formData.type == 'add' }">
            <el-input :class="{ 'item-input-text': formData.type == 'add' }" v-model="form.grayscaleValueList[index]"
              placeholder="请输入"></el-input>
            <span v-if="formData.type == 'add'" @click.stop="deleteItem(index)" class="item-text">-</span>
            <span type="text" @click.stop="form.grayscaleValueList.push('')" class="item-text"
              v-if="index === form.grayscaleValueList.length - 1 && formData.type == 'add'">+</span>
          </div>
        </div>
        <div slot="grayscaleInterfaceList" style="width: 100%;">
          <div v-for="(item, index) in form.grayscaleInterfaceList" :key="index"
            :class="{ 'grayscale-value-item': formData.type == 'add' }">
            <el-input :class="{ 'item-input-text': formData.type == 'add' }"
              v-model="form.grayscaleInterfaceList[index]" placeholder="请输入"></el-input>
            <span v-if="formData.type == 'add'" @click.stop="deleteGrayscaleInterfaceItem(index)"
              class="item-text">-</span>
            <span type="text" @click.stop="form.grayscaleInterfaceList.push('')" class="item-text"
              v-if="index === form.grayscaleInterfaceList.length - 1 && formData.type == 'add'">+</span>
          </div>
        </div>
        <div slot="grayIdAndFace" style="width: 100%;">
          <div v-for="(item, index) in form.grayIdAndFace" :key="index"
            :class="{ 'grayscale-value-item': formData.type == 'add' }">
            <el-input :class="{ 'item-input-text': formData.type == 'add' }" v-model="form.grayIdAndFace[index].id"
              placeholder="请输入租户"></el-input>
            <el-input :class="{ 'item-input-text': formData.type == 'add' }" v-model="form.grayIdAndFace[index].face"
              placeholder="请输入接口"></el-input>
            <span v-if="formData.type == 'add'" @click.stop="deleteGrayIdAndFaceItem(index)" class="item-text">-</span>
            <span type="text" @click.stop="form.grayIdAndFace.push({ id: '', face: '' })" class="item-text"
              :style="{ opacity: index === form.grayIdAndFace.length - 1 && formData.type == 'add' ? 1 : 0 }">+</span>
          </div>
        </div>
      </searchBar>
    </div>
    <!-- 底部 -->
    <div slot="footer" class="add-region__footer">
      <el-button size="small" @click="handleClose">取消</el-button>
      <el-button type="primary" size="small" @click="sure">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
export default {
  name: "addVersion",
  components: { searchBar },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({ type: 'add' })
    }
  },
  data() {
    return {
      form: {
        grayscaleType: '',
        grayscaleValueList: [''],
        comment: '',
        grayscaleInterfaceList: [''],
        grayIdAndFace: [{
          id: '',
          face: '',
        }]
      },
      isFirstLoad: true,
    };
  },
  methods: {
    sure() {
      if (this.formData.type == 'add') {
        this.addGray()
      } else {
        this.editGray()
      }
    },
    editGray() {
      let params = {
        grayFrom: this.oldData,
        grayTo: {
          grayscaleType: this.form.grayscaleType,
          grayscaleInterface: this.form.grayscaleInterfaceList[0] ? this.form.grayscaleInterfaceList[0] : '-',
          grayscaleOperatorId: this.form.grayscaleValueList[0] ? this.form.grayscaleValueList[0] : '-',
          comment: this.form.comment,
        }
      }
      if (this.form.grayscaleType == 3 && (!this.form.grayscaleInterfaceList[0] || !this.form.grayscaleValueList[0])) {
        this.$message.warning('请填写完整租户和接口信息！');
        return;
      }
      return this.highGetPost('monitorApi', 'grayUpdate', params, '灰度管理更新').then(
        (res) => {
          if (res.serviceFlag === 'TRUE') {
            this.$message.success(res.returnMsg);
            this.$emit("update:visible", false);
            this.$emit('updateTable');
          } else {
            this.$message.error(res.returnMsg);
          }
          return res;
        }
      );
    },
    addGray() {
      console.log('表格数据', this.form);
      let list = [];
      let isValidFlag = [];
      if (this.form.grayscaleType == 3) {
        this.form.grayIdAndFace.forEach((item, index) => {
          if (!item.id || !item.face) {
            isValidFlag.push(false);
          }
          let obj = {
            grayscaleInterface: item.face ? item.face : '-',
            grayscaleType: this.form.grayscaleType,
            grayscaleOperatorId: item.id ? item.id : '-',
            comment: this.form.comment
          }
          list.push(obj)
        })
      } else {
        let forIndex = this.form.grayscaleValueList.length > this.form.grayscaleInterfaceList.length ? 'grayscaleValueList' : 'grayscaleInterfaceList';
        this.form[forIndex].forEach((item, index) => {
          let obj = {
            grayscaleInterface: this.form.grayscaleInterfaceList[index] ? this.form.grayscaleInterfaceList[index] : '-',
            grayscaleType: this.form.grayscaleType,
            grayscaleOperatorId: this.form.grayscaleValueList[index] ? this.form.grayscaleValueList[index] : '-',
            comment: this.form.comment
          }
          list.push(obj)
        })
      }
      if (isValidFlag.indexOf(false) != -1) {
        this.$message.warning('请填写完整租户&接口信息！');
        return;
      }
      let params = {
        list,
      }
      this.addsend(params);

    },
    addsend(params) {
      return this.highGetPost('monitorApi', 'grayAdd', params, '灰度管理新增').then(
        (res) => {
          if (res.serviceFlag === 'TRUE') {
            this.$message.success(res.returnMsg);
            this.$emit("update:visible", false);
            this.$emit('updateTable');
          } else {
            this.$message.error(res.returnMsg);
          }
          return res;
        }
      );
    },
    deleteItem(index) {
      if (this.form.grayscaleValueList.length === 1) {
        return;
      }
      this.form.grayscaleValueList.splice(index, 1)
    },
    deleteGrayIdAndFaceItem(index) {
      if (this.form.grayIdAndFace.length === 1) {
        return;
      }
      this.form.grayIdAndFace.splice(index, 1)
    },

    deleteGrayscaleInterfaceItem(index) {
      if (this.form.grayscaleInterfaceList.length === 1) {
        return;
      }
      this.form.grayscaleInterfaceList.splice(index, 1)
    },

    handleClose() {
      this.$emit("update:visible", false);
    },
  },
  computed: {
    fields() {
      let list = [
        {
          prop: 'grayscaleType',
          label: '灰度依凭类型:',
          labelWidth: '120px',
          element: 'el-select',
          bind: {
            placeholder: '请选择',
            clearable: true,
          },
          slot: {
            element: 'el-option',
            enums: [
              { label: '租户ID', value: 1 },
              { label: '接口链接', value: 2 },
              { label: '租户&接口共同作用', value: 3 },
            ],
          },
          span: 24,
        },
        {
          prop: 'grayscaleInterfaceList',
          label: '新增接口:',
          labelWidth: '120px',
          span: 24,
          // element: 'el-input',
          // bind: {
          //   placeholder: '请输入',
          //   clearable: true,
          // },
        },
        {
          prop: 'grayscaleValueList',
          label: '新增租户:',
          labelWidth: '120px',
          span: 24,
        },
        {
          prop: 'grayIdAndFace',
          label: '新增租户&接口:',
          labelWidth: '120px',
          span: 24,
        },
        {
          prop: 'comment',
          label: '备注:',
          labelWidth: '120px',
          element: 'el-input',
          bind: {
            placeholder: '请输入',
            clearable: true,
          },
          span: 24,
        },
      ];
      if (this.form.grayscaleType == 1) {
        list = list.filter(item => !['grayscaleInterfaceList', 'grayIdAndFace'].includes(item.prop))
      } else if (this.form.grayscaleType == 2) {
        list = list.filter(item => !['grayscaleValueList', 'grayIdAndFace'].includes(item.prop))
      } else if (this.form.grayscaleType == 3 && this.formData.type == 'add') {
        list = list.filter(item => !['grayscaleValueList', 'grayscaleInterfaceList'].includes(item.prop))
      } else if (this.form.grayscaleType == 3 && this.formData.type == 'edit') {
        list = list.filter(item => !['grayIdAndFace'].includes(item.prop))
      }
      else if (!this.form.grayscaleType) {
        list = list.filter(item => !['grayscaleValueList', 'grayIdAndFace', 'grayscaleInterfaceList'].includes(item.prop))
      }
      return list;
    },
  },
  watch: {
    'form.grayscaleType': {
      handler() {
        let oldData = this.form;
        this.form.grayscaleValueList = this.isFirstLoad ? oldData.grayscaleValueList : [''];
        this.form.grayscaleInterfaceList = this.isFirstLoad ? oldData.grayscaleInterfaceList : [''];
        this.form.grayIdAndFace = this.isFirstLoad ? oldData.grayIdAndFace : [{
          id: '',
          face: '',
        }];
        this.isFirstLoad = false;
      },
      immediate: false,
    },
    formData: {
      handler(val) {
        if (val.type === 'edit') {
          this.oldData = val;
          console.log('编辑数据', val);
          this.form.comment = val.comment || '';
          this.form.grayscaleType = val.grayscaleType || '';
          this.form.grayscaleValueList = [val.grayscaleOperatorId || ''];
          this.form.grayscaleInterfaceList = [val.grayscaleInterface || ''];
        }
      },
      deep: true, // 深度监听
      immediate: true // 立即执行一次
    }
  }
};
</script>

<style lang="less" scoped>
.add-version {
  .grayscale-value-item {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-top: 10px;

    .item-input-text {
      width: 80%;
    }

    .item-text {
      font-size: 16px;
      cursor: pointer;
      color: #1664FF;
    }
  }

  &__main {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  &__footer {
    text-align: center;
  }

  /deep/ .el-dialog {
    display: flex;
    flex-direction: column;
    border-radius: 10px;
  }

  /deep/ .el-dialog__header {
    position: relative;
    padding: 20px;
    border-bottom: 1px solid #ccc;

    .el-dialog__title {
      font-size: 18px;
      font-weight: bold;
    }

    .el-dialog__headerbtn {
      top: 19px;
      font-size: 18px;
    }
  }

  /deep/ .el-dialog__body {
    padding: 20px;
    flex: 1;
    height: 0;
  }

  /deep/ .el-dialog__footer {
    padding: 20px;
    border-top: 1px solid #ccc;
  }
}
</style>
