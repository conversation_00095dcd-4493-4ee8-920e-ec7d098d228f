<template>
    <div class="time-granularity">
        <el-select v-model="localGranularity" @change="handleGranularityChange">
            <el-option label="天" :value="4" />
            <el-option label="小时" :value="3" />
            <el-option label="分钟" :value="1" />
        </el-select>
        <div class="separator"></div>
        <div class="picker-wrapper">
            <template v-if="localGranularity === 4">
                <el-date-picker
                    v-model="localTimeRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd 00:00:00"
                    :clearable="false"
                    :picker-options="dayPickerOptions"
                    @change="handleTimeChange"
                />
            </template>
            <template v-else-if="localGranularity === 3">
                <el-date-picker
                    v-model="localTimeRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="yyyy-MM-dd HH:00"
                    value-format="yyyy-MM-dd HH:00:00"
                    :clearable="false"
                    :picker-options="{
                        format: 'HH:00',
                        ...dayPickerOptions,
                    }"
                    @change="handleTimeChange"
                />
            </template>
            <template v-else>
                <el-date-picker
                    v-model="localTimeRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="yyyy-MM-dd HH:mm"
                    value-format="yyyy-MM-dd HH:mm:00"
                    :clearable="false"
                    :picker-options="dayPickerOptions"
                    @change="handleTimeChange"
                />
            </template>
        </div>
    </div>
</template>

<script>
import dayjs from 'dayjs';

/**
 * 时间粒度选择组件
 * @component time-granularity
 */
export default {
    name: 'time-granularity',
    props: {
        /**
         * 组件值，包含粒度和时间范围
         * @property {Object} value
         * @property {number} value.granularity - 时间粒度：4-天，3-小时，1-分钟
         * @property {Array} value.timeRange - 时间范围，包含开始和结束时间
         */
        value: {
            type: Object,
            default: () => {
                const today = dayjs().format('YYYY-MM-DD 00:00:00');
                return {
                    granularity: 4,
                    timeRange: [today, today],
                };
            },
        },
        finalDate: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            localGranularity: this.value.granularity,
            localTimeRange:
                this.value.timeRange || this.getDefaultTimeRange(this.value.granularity),
            dayPickerOptions: {
                disabledDate: (time) => {
                    let endTime = dayjs().valueOf();
                    if (this.localGranularity === 4) {
                        return time.getTime() > endTime;
                    }
                    if (this.finalDate) {
                        endTime = dayjs(this.finalDate).valueOf();
                    }
                    const sevenDaysAgo = endTime - 7 * 24 * 60 * 60 * 1000;
                    return time.getTime() < sevenDaysAgo || time.getTime() > endTime;
                },
            },
        };
    },
    watch: {
        value: {
            handler(newVal) {
                this.localGranularity = newVal.granularity;
                this.localTimeRange =
                    newVal.timeRange || this.getDefaultTimeRange(newVal.granularity);
            },
            deep: true,
        },
    },
    methods: {
        /**
         * 获取默认时间范围
         * @param {number} granularity - 粒度值
         * @returns {Array} 默认的时间范围
         */
        getDefaultTimeRange(granularity) {
            let startTime, endTime;
            const baseDate = dayjs(this.finalDate);

            switch (granularity) {
                case 4: // 天
                    const endDay = baseDate.format('YYYY-MM-DD');
                    const startDay = baseDate.subtract(7, 'day').format('YYYY-MM-DD');
                    return [`${startDay} 00:00:00`, `${endDay} 00:00:00`];
                case 3: // 小时
                    startTime = baseDate.format('YYYY-MM-DD 00:00:00');
                    endTime = baseDate.format('YYYY-MM-DD 23:00:00');
                    return [startTime, endTime];
                case 1: // 分钟
                    startTime = baseDate.format('YYYY-MM-DD 00:00:00');
                    endTime = baseDate.format('YYYY-MM-DD 23:55:00');
                    return [startTime, endTime];
            }
            return ['', ''];
        },

        /**
         * 处理粒度变更
         * @param {number} val - 新的粒度值
         */
        handleGranularityChange(val) {
            const timeRange = this.getDefaultTimeRange(val);
            this.localTimeRange = timeRange;
            this.emitChange(val, timeRange);
        },

        /**
         * 处理时间变更
         * @param {Array} val - 新的时间范围
         */
        handleTimeChange(val) {
            this.emitChange(this.localGranularity, val);
        },

        /**
         * 触发变更事件
         * @param {number} granularity - 粒度值
         * @param {Array} timeRange - 时间范围
         */
        emitChange(granularity, timeRange) {
            this.$emit('input', {
                granularity,
                timeRange,
            });
            this.$emit('change', {
                granularity,
                timeRange,
            });
        },
    },
};
</script>

<style lang="less" scoped>
.time-granularity {
    display: flex;
    align-items: center;
    height: 36px;

    .el-select {
        width: 80px;
        /deep/ .el-input__inner {
            border: none;
            padding-right: 10px;
            height: 36px;
            line-height: 36px;
        }
        /deep/ .el-input__suffix {
            top: 1px;
        }
    }

    .separator {
        width: 1px;
        height: 24px;
        background-color: #e4e7ed;
    }

    .picker-wrapper {
        width: 340px; // 增加宽度以适应时间范围选择器
        /deep/ .el-input__inner {
            border: none;
            height: 36px;
            line-height: 36px;
        }
        /deep/ .el-input__icon {
            line-height: 36px;
        }
    }
}
</style>
