<template>
    <div class="commonComp">
        <searchBar :fields="fields" :form="form">
            <el-button
                class="highlyavailable-primary-btn"
                type="primary"
                size="small"
                @click="search()"
                >查询</el-button
            >
        </searchBar>
        <dataTable
            class="dataTable"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :total="total"
            stripe
            :updateTable="getTableData"
        >
            <template #listType="{ row }">
                <span>{{ listType[row.listType] }}</span>
            </template>
            <template #complaintType="{ row }">
                <span>{{ complaintType[row.complaintType] }}</span>
            </template>
            <template #isValid="{ row }">
                <span>{{ isValid[row.isValid] }}</span>
            </template>
            <template #status="{ row }">
                <span>{{ status[row.status] }}</span>
            </template>
            <template #isTransfer="{ row }">
                <span>{{ isValid[row.isTransfer] }}</span>
            </template>
            <template #operate="{ row }">
                <div class="table-btn">
                    <el-button
                        class="table-btn-item"
                        type="text"
                        size="mini"
                        @click="handleEdit(row)"
                    >
                        修改
                    </el-button>
                    <el-button
                        v-if="row.isValid === 1"
                        class="table-btn-item redText"
                        type="text"
                        size="mini"
                        @click="handleDelete(row)"
                    >
                        删除
                    </el-button>
                    <el-button
                        v-else
                        class="table-btn-item redText"
                        type="text"
                        size="mini"
                        @click="restore(row)"
                    >
                        恢复
                    </el-button>
                </div>
            </template>
        </dataTable>
        <!-- 编辑/新增 -->
        <myDialog
            :title="dialogTypeName"
            :visible.sync="dialogVisible"
            :btnList="dialogConfigurationBtn()"
        >
            <searchBar :fields="dialogFields" :form="dialogForm"> </searchBar>
        </myDialog>
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import myDialog from '_com/dialog/index.vue';
export default {
    name: 'commonComp',
    components: {
        searchBar,
        dataTable,
        myDialog
    },
    props: {
        fieldsList: {
            type: Array,
            default: () => []
        },
        formList: {
            type: Object,
            default: () => ({})
        },
        tableColumns: {
            type: Array,
            default: () => []
        },
        dialogFormList: {
            type: Object,
            default: () => ({})
        },
        dialogFieldsList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            fields: this.fieldsList,
            form: this.formList,
            columns: this.tableColumns,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            total: 0,
            dialogType: 'add', // add、edit
            dialogVisible: false,
            dialogForm: JSON.parse(JSON.stringify(this.dialogFormList)),
            dialogFields: this.dialogFieldsList,
            listType: {
                1: '白名单',
                2: '黑名单'
            },
            complaintType: {
                1: '实时',
                2: '离线'
            },
            isValid: {
                1: '是',
                0: '否'
            },
            status: {
                0: '申请中',
                1: '同意',
                2: '驳回',
                3: '禁止'
            }
        };
    },
    computed: {
        dialogTypeName() {
            switch (this.dialogType) {
                case 'add':
                    return '新增';
                case 'edit':
                    return '编辑';
                default:
                    return '新增';
            }
        }
    },
    watch: {
        fieldsList: {
            handler(newV) {
                this.fields = newV;
            },
            deep: true
        },
        dialogFieldsList: {
            handler(newV) {
                this.dialogFields = newV;
            },
            deep: true
        }
    },
    mounted() {
        this.search();
    },
    methods: {
        dialogConfigurationBtn() {
            return [
                {
                    name: '确定',
                    class: 'blue-btn',
                    listeners: {
                        click: () => {
                            this.addOrEdit(this.dialogForm);
                        }
                    }
                }
            ];
        },
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        getTableData(pagination = {}) {
            this.$emit('getTableData', pagination, this.form, (res) => {
                this.tableData = res.list;
                this.total = res.totalSize;
            });
        },
        handleAdd() {
            this.dialogType = 'add';
            this.dialogForm = this.dialogFormList;
            this.dialogVisible = true;
        },
        handleEdit(row) {
            this.dialogType = 'edit';
            this.curRow = row;
            this.dialogForm = Object.assign(this.dialogForm, row);
            this.dialogVisible = true;
        },
        handleDelete(row) {
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$emit('handleDelete', row, (res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.$message.success(res.returnMsg);
                        this.search();
                    } else {
                        this.$message.error(res.returnMsg);
                    }
                    this.dialogVisible = false;
                });
            });
        },
        addOrEdit(form) {
            this.$emit('addOrEdit', form, this.dialogType, this.curRow, (res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg);
                    this.search();
                } else {
                    this.$message.error(res.returnMsg);
                }
                this.dialogVisible = false;
            });
        },
        restore(row) {
            this.dialogForm = Object.assign(this.dialogForm, row);
            this.$confirm('确定要恢复吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$emit('restore', this.dialogForm, row, (res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.$message.success(res.returnMsg);
                        this.search();
                    } else {
                        this.$message.error(res.returnMsg);
                    }
                });
            });
        }
    }
};
</script>

<style lang="less" scoped>
.commonComp {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 1.33rem;
}
.dataTable {
    width: 100%;
    flex: 1;
}
.table-btn {
    display: flex;

    &-item {
        position: relative;
        font-size: 14px;

        &.redText {
            color: #ff4d4f;
        }

        &::after {
            position: absolute;
            right: -13px;
            top: 8px;
            width: 1px;
            background: #ebeef5;
            height: 11px;
            content: '';
        }

        &:last-child {
            &::after {
                position: absolute;
                right: -13px;
                top: 8px;
                width: 1px;
                background: #ebeef5;
                height: 0px;
                content: '';
            }
        }
    }
}
</style>
