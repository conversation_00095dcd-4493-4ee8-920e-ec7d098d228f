<template>
    <el-dialog
        class="add-workOrder"
        :title="`${typeLabel}`"
        :visible="visible"
        width="750px"
        :close-on-click-modal="false"
        :append-to-body="true"
        destroy-on-close
        @close="handleClose"
    >
        <searchBar ref="searchBar" class="search-bar" :fields="fields" :form="form" />
        <!--  -->
        <div slot="footer">
            <el-button size="mini" @click="handleClose">取消</el-button>
            <el-button type="primary" size="mini" @click="sure">确定</el-button>
        </div>
    </el-dialog>
</template>
<script>
import searchBar from '_com/searchForm/index.vue';
import { addFields } from '../constants';
export default {
    name: 'add-workOrder',
    components: {
        searchBar
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        row: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            form: {
                listType: '',
                workOrderName: '',
                queryMsisdn: '',
                attachments: [],
                authStime: '',
                authEtime: '',
                dataStime: '',
                dataEtime: '',
                applicationReason: '',
                taskId: '',
                isCreating: false
            }
        };
    },
    computed: {
        userInfo() {
            return frameService.getUser();
        },
        isRealTimeEvenType() {
            return Number(this.form.listType) === 2;
        },
        fields() {
            return addFields(this.isRealTimeEvenType);
        },
        isEdit() {
            return Boolean(Object.keys(this.row || {}).length);
        },
        typeLabel() {
            return this.isEdit ? '修改' : '新增';
        }
    },
    created() {
        if (this.isEdit) {
            this.initData();
        }
    },
    methods: {
        initData() {
            Object.assign(this.form, {
                listType: this.row.listType,
                workOrderName: this.row.workOrderName,
                queryMsisdn: this.row.queryMsisdn,
                // attachments: this.row.attachments,
                authStime: this.row.authStime,
                authEtime: this.row.authEtime,
                dataStime: this.row.dataStime,
                dataEtime: this.row.dataEtime,
                taskId: this.row.taskId,
                applicationReason: this.row.applicationReason
            });
        },
        async addRule() {
            const valid = await this.$refs.searchBar.validForm();
            if (!valid) {
                return this.$message.warning('存在必填项未填写');
            }
            if (this.form.attachments.length === 0) {
                return this.$message.warning('附件未上传');
            }
            if (this.form.listType == 2 && this.form.taskId === '') {
                return this.$message.warning('申请任务ID不能为空');
            }
            if (this.isCreating) return;
            this.isCreating = true;
            const params = new FormData();
            params.append(
                'requestData',
                JSON.stringify({
                    userId: this.userInfo.userId,
                    listType: this.form.listType,
                    workOrderName: this.form.workOrderName,
                    queryMsisdn: this.form.queryMsisdn,
                    authStime: this.form.authStime,
                    authEtime: this.form.authEtime,
                    dataStime: this.form.dataStime,
                    dataEtime: this.form.dataEtime,
                    applicationReason: this.form.applicationReason,
                    taskId: this.form.taskId
                })
            );
            this.form.attachments.forEach((item) => {
                params.append('excels', item.raw);
            });

            this.highGetPost(
                'newComplaintToolApi',
                'addPermission', // 请确认实际接口方法名
                params,
                '权限申请'
            )
                .then(({ data }) => {
                    this.isCreating = false;
                    if (data.serviceFlag === 'TRUE') {
                        this.$message.success(data.returnMsg);
                        this.$emit('updateTable');
                        this.handleClose();
                    } else {
                        this.$message.warning(data.returnMsg);
                    }
                })
                .catch((err) => {
                    this.isCreating = false;
                });
        },
        async editRule() {
            const valid = await this.$refs.searchBar.validForm();
            if (!valid) {
                return this.$message.warning('存在必填项未填写');
            }
            if (this.form.listType == 2 && this.form.taskId === '') {
                return this.$message.warning('申请任务ID不能为空');
            }
            const params = new FormData();
            params.append(
                'requestData',
                JSON.stringify({
                    id: this.row.id,
                    userId: this.userInfo.userId,
                    listType: this.form.listType,
                    workOrderName: this.form.workOrderName,
                    queryMsisdn: this.form.queryMsisdn,
                    authStime: this.form.authStime,
                    authEtime: this.form.authEtime,
                    dataStime: this.form.dataStime,
                    dataEtime: this.form.dataEtime,
                    applicationReason: this.form.applicationReason,
                    taskId: this.form.taskId
                })
            );
            this.form.attachments.forEach((item) => {
                params.append('excels', item.raw);
            });

            this.highGetPost(
                'newComplaintToolApi',
                'updatePermission', // 请确认实际接口方法名
                params,
                '权限修改'
            ).then(({ data }) => {
                if (data.serviceFlag === 'TRUE') {
                    this.$message.success(data.returnMsg);
                    this.$emit('updateTable');
                    this.handleClose();
                } else {
                    this.$message.warning(data.returnMsg);
                }
            });
        },
        async sure() {
            if (this.isEdit) {
                this.editRule();
            } else {
                this.addRule();
            }
        },
        handleClose() {
            this.$emit('update:visible', false);
        }
    }
};
</script>
<style lang="less" scoped>
@import url('../../../../../style/dialog.less');
.add-workOrder {
    height: 100%;
    .search-bar {
        padding: 0 4px;
        margin-top: 20px;
    }
}
</style>
