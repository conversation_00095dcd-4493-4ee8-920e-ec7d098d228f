const fields = (Opts = {}) => {
    return [
        {
            prop: 'complaintType',
            label: '投诉类型：',
            labelWidth: '70',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '实时', value: 1 },
                    { label: '离线', value: 2 }
                ]
            },
            span: 3
        },
        {
            prop: 'userId',
            label: '用户账号：',
            labelWidth: '70',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true
            },
            span: 3
        },
        {
            prop: 'startTime',
            label: '开始时间：',
            labelWidth: '70',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss'
            },
            span: 4
        },
        {
            prop: 'endTime',
            label: '结束时间：',
            labelWidth: '70',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss'
            },
            span: 4
        },
        {
            span: 2
        }
    ];
};
const tableColumns = [
    {
        prop: 'id',
        label: '字段ID'
    },
    {
        prop: 'complaintType',
        label: '投诉类型'
    },
    {
        prop: 'createUserId',
        label: '租户ID'
    },
    {
        prop: 'createUserName',
        label: '租户名称'
    },
    {
        prop: 'userId',
        label: '用户账号'
    },
    {
        prop: 'userName',
        label: '用户名称'
    },
    {
        prop: 'isValid',
        label: '是否生效'
    },
    {
        prop: 'createTime',
        label: '创建时间'
    },
    {
        prop: 'lastUpdateTime',
        label: '最后修改时间'
    },
    {
        prop: 'operate',
        label: '操作'
    }
];

const dialogFields = [
    {
        prop: 'complaintType',
        label: '投诉类型：',
        labelWidth: '70',
        element: 'el-select',
        bind: {
            placeholder: '请选择',
            clearable: true
        },
        slot: {
            element: 'el-option',
            enums: [
                { label: '实时', value: 1 },
                { label: '离线', value: 2 }
            ]
        },
        span: 24
    },
    {
        prop: 'userId',
        label: '用户账号：',
        labelWidth: '70',
        element: 'el-input',
        bind: {
            placeholder: '请输入',
            clearable: true
        },
        span: 24
    },
    {
        prop: 'userName',
        label: '用户名称：',
        labelWidth: '70',
        element: 'el-input',
        bind: {
            placeholder: '请输入',
            clearable: true
        },
        span: 24
    },
    {
        prop: 'isValid',
        label: '是否生效：',
        labelWidth: '70',
        element: 'el-select',
        bind: {
            placeholder: '请选择',
            clearable: true
        },
        slot: {
            element: 'el-option',
            enums: [
                { label: '是', value: 1 },
                { label: '否', value: 0 }
            ]
        },
        span: 24
    }
];

export { fields, tableColumns, dialogFields };
