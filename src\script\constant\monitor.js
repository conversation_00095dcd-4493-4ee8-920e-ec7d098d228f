const fields = (mapOpts = {}) => {
    return [
        {
            prop: 'regionName',
            label: '区域名称:',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true,
            },
            span: 3,
        },
        {
            prop: 'regionCodeList',
            label: '区域ID:',
            labelWidth: '60px',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true,
            },
            span: 3,
        },
        {
            prop: 'cityName',
            label: '地市:',
            labelWidth: '46px',
            element: () => import('@/script/components/selectDistrict.vue'),
            bind: {
                options: mapOpts.citys,
                props: {
                    multiple: true,
                    label: 'label',
                    value: 'value',
                    children: 'children',
                    emitPath: false,
                }
            },
            span: 4,
        },
        {
            prop: 'classifyName',
            label: '图层类型:',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
                multiple: true,
                'collapse-tags': true,
            },
            slot: {
                element: 'el-option',
                enums: mapOpts.layerIds,
            },
            span: 3,
        },
        {
            prop: 'status',
            label: '高可用状态:',
            labelWidth: '88px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '全部', value: 0 },
                    { label: '启用', value: 1 },
                    { label: '关闭', value: 2 },
                ],
            },
            span: 3,
        },
        {
            prop: 'time',
            label: '时间:',
            labelWidth: '46px',
            element: 'el-date-picker',
            bind: {
                clearable: true,
                type: 'datetimerange',
                'range-separator': '-',
                'start-placeholder': '开始日期',
                'end-placeholder': '结束日期',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss',
            },
            span: 6,
        },
        {
            span: 2
        }
    ];
};

const tableColumns = [
    {
        prop: 'regionName',
        label: '区域名称',
    },
    {
        prop: 'regionCode',
        label: '区域ID',
    },
    {
        prop: 'cityName',
        label: '地市',
    },
    {
        prop: 'classifyName',
        label: '图层类型',
    },
    {
        prop: 'status',
        label: '高可用状态',
    },
    {
        prop: 'nowDatasource',
        label: '所用库',
    },
    {
        prop: 'startTime',
        label: '开始时间',
    },
    {
        prop: 'endTime',
        label: '预设结束时间',
    },
    {
        prop: 'switchCount',
        label: '启用高可用次数',
    },
    {
        prop: 'inputUserId',
        label: '租户ID',
    },
    {
        prop: 'sourceSystemName',
        label: '租户名称',
    },
    {
        prop: 'operation',
        label: '操作',
        width: '270',
    },
];

const regionTypeList = {
    1: '自定义区域',
    2: '省',
    3: '市',
    4: '区县'
};

const statusList = {
    0: '全部',
    1: '高可用已开始',
    2: '高可用未开始',
    3: '关闭',
};

const startFields = [
    {
        prop: 'startTime',
        label: '开始时间：',
        labelWidth: '90px',
        span: 24,
        rules: [{ required: true, message: '请选择开始时间！', trigger: 'blur' }],
    },
    {
        prop: 'endTime',
        label: '预设结束时间：',
        labelWidth: '108px',
        element: 'el-date-picker',
        bind: {
            clearable: true,
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss',
        },
        span: 24,
    },
];

export {
    fields,
    tableColumns,
    regionTypeList,
    statusList,
    startFields
};