export default {
    state: {
        highAvailable: {
            districts: [],
            originDistricts: [],
            layers: [],
            regionData: [],
            cityData: [],
            regionTree: [], // 地理区域树形数据
            regionIds: {},
            regionNames: {},
            sceneTypes: [],
            priDataSources: [],
        },
        // 位置服务应用
        positionService: {
            districts: [],
            originDistricts: [],
            layers: [],
            regionData: [],
            cityData: [],
            regionTree: [], // 地理区域树形数据
            regionIds: {},
            regionNames: {},
            sceneTypes: [],
            priDataSources: [],
        }
    },
    getters: {
        highDistricts: (state) => {
            return state.highAvailable.districts;
        },
        highOriginDistricts: (state) => {
            return state.highAvailable.originDistricts;
        },
        highLayers: (state) => {
            return state.highAvailable.layers;
        },
        highPriDataSources: (state) => {
            return state.highAvailable.priDataSources;
        },
        // 位置服务应用
        positionDistricts: (state) => {
            return state.positionService.districts;
        },
        positionOriginDistricts: (state) => {
            return state.positionService.originDistricts;
        },
        positionLayers: (state) => {
            return state.positionService.layers;
        },
        positionPriDataSources: (state) => {
            return state.positionService.priDataSources;
        }
    },
    mutations: {
        setHighDistricts(state, payload) {
            state.highAvailable.districts = payload;
        },
        setHighOriginDistricts(state, payload) {
            state.highAvailable.originDistricts = payload;
        },
        setHighLayers(state, payload) {
            state.highAvailable.layers = payload;
        },
        setHighRegionTree(state, payload) {
            state.highAvailable.regionTree = payload;
        },
        setHighRegionData(state, payload) {
            state.highAvailable.regionData = payload;
        },
        setHighCityData(state, payload) {
            state.highAvailable.cityData = payload;
        },
        setHighRegionIds(state, payload) {
            state.highAvailable.regionIds = payload;
        },
        setHighRegionNames(state, payload) {
            state.highAvailable.regionNames = payload;
        },
        setHighSceneTypes(state, payload) {
            state.highAvailable.sceneTypes = payload;
        },
        setHightPriDataSources(state, payload) {
            state.highAvailable.priDataSources = payload;
        },
        // 位置服务应用
        setPositionDistricts(state, payload) {
            state.positionService.districts = payload;
        },
        setPositionOriginDistricts(state, payload) {
            state.positionService.originDistricts = payload;
        },
        setPositionLayers(state, payload) {
            state.positionService.layers = payload;
        },
        setPositionRegionTree(state, payload) {
            state.positionService.regionTree = payload;
        },
        setPositionRegionData(state, payload) {
            state.positionService.regionData = payload;
        },
        setPositionCityData(state, payload) {
            state.positionService.cityData = payload;
        },
        setPositionRegionIds(state, payload) {
            state.positionService.regionIds = payload;
        },
        setPositionRegionNames(state, payload) {
            state.positionService.regionNames = payload;
        },
        setPositionSceneTypes(state, payload) {
            state.positionService.sceneTypes = payload;
        },
        setPositiontPriDataSources(state, payload) {
            state.positionService.priDataSources = payload;
        }
    },
    actions: {
    },
};