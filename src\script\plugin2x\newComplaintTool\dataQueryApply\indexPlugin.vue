<template>
    <div class="data-apply">
        <div class="search-form">
            <searchBar :form="form" :fields="fields">
                <el-button type="primary" @click="search">查询</el-button>
                <el-button class="add" type="primary" icon="el-icon-plus" @click="add"
                    >新增</el-button
                >
            </searchBar>
        </div>
        <dataTable
            ref="tableView"
            class="data-table"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :total="total"
            :updateTable="getTableData"
            isHideUpLine
        >
            <template #listType="{ row }">
                <span>{{ row.listType == 1 ? '用户轨迹稽核' : '实时事件稽核' }}</span>
            </template>
            <template #filePath="{ row }">
                <template v-if="row.filePath && row.filePath !== ''">
                    <div
                        class="underLine-btn"
                        v-for="(item, index) in row.filePath.split(',')"
                        :key="index"
                        @click="downloadFile(row, item)"
                    >
                        {{ item }}
                    </div>
                </template>
            </template>
            <template #status="{ row }">
                <span>{{ statusList[Number(row.status)] }}</span>
            </template>
            <template #isValid="{ row }">
                <span>{{ row.isValid == 1 ? '是' : '否' }}</span>
            </template>
            <template #attachment="{ row }">
                <span class="global-blue-btn" @click="view(row)">{{ row.filename }}</span>
            </template>
            <template #operation="{ row }">
                <span
                    class="global-blue-btn"
                    :class="{ gray: !isShowDataQuery(row) }"
                    @click="dataQuery(row)"
                    >数据查询</span
                >
                <span class="divider">|</span>
                <span
                    class="global-blue-btn"
                    :class="{ gray: !isApplying(row.status) }"
                    @click="edit(row)"
                    >修改</span
                >
                <span class="divider">|</span>
                <span
                    class="global-red-btn"
                    :class="{ gray: !isApplying(row.status) }"
                    @click="del(row)"
                    >删除</span
                >
            </template>
        </dataTable>
        <!--  -->
        <addWorkOrder
            v-if="isShowAddWorkOrder"
            :visible.sync="isShowAddWorkOrder"
            :row="curRow"
            @updateTable="search"
        />
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import { fields, tableColumns, statusList } from './constants';
export default {
    name: 'dataQueryApply',
    components: {
        searchBar,
        dataTable,
        addWorkOrder: () => import('./components/addWorkOrder.vue')
    },
    data() {
        return {
            form: {
                listType: '',
                taskName: '',
                queryMsisdn: '',
                status: ''
            },
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            total: 0,
            isShowAddWorkOrder: false,
            curRow: {},
            statusList
        };
    },
    computed: {
        fields() {
            return fields;
        },
        columns() {
            return tableColumns;
        }
    },
    created() {
        this.search();
    },
    methods: {
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        async getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            this.highGetPost(
                'newComplaintToolApi',
                'getPermissionList', // 请确认实际接口方法名
                {
                    ...this.form,
                    pageNum: curPage,
                    pageSize
                },
                '权限查询列表'
            ).then(({ data }) => {
                this.tableData = data.list;
                this.total = data.totalSize;
            });
        },
        add() {
            this.curRow = {};
            this.isShowAddWorkOrder = true;
        },
        del(row) {
            this.$confirm('确定删除该条记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.highGetPost(
                    'newComplaintToolApi',
                    'delPermission',
                    {
                        id: row.id
                    },
                    '权限删除'
                ).then(() => {
                    this.$message.success('删除成功');
                    this.search();
                });
            });
        },
        edit(row) {
            this.curRow = row;
            this.isShowAddWorkOrder = true;
        },
        dataQuery(row) {
            const path = row.listType == 1 ? 'trackQuery' : 'liveEvents';
            this.$router.push({
                path,
                query: row
            });
        },
        isApplying(status) {
            return Number(status) === 0;
        },
        isAgree(status) {
            return Number(status) === 1;
        },
        isReject(status) {
            return Number(status) === 2;
        },
        isForbidden(status) {
            return Number(status) === 3;
        },
        isShowDataQuery(row) {
            const { listType, status } = row;
            if (Number(listType) === 1) {
                return Number(status) === 1;
            }
            return Number(status) === 5;
        },
        downloadFile(row, fileName) {
            const params = { type: 2, fileName: `${row.id}_${fileName}` };
            this.highGetPost(
                'newComplaintToolApi',
                'downloadFile', // 请确认实际接口方法名
                params,
                '附件下载'
            ).then((res) => {
                const blob = new Blob([res]);
                const linkNode = document.createElement('a');
                linkNode.download = `${row.id}_${fileName}`; //a标签的download属性规定下载文件的名称
                linkNode.style.display = 'none';
                linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
                document.body.appendChild(linkNode);
                linkNode.click(); //模拟在按钮上的一次鼠标单击
                URL.revokeObjectURL(linkNode.href); // 释放URL 对象
                document.body.removeChild(linkNode);
            });
        }
    }
};
</script>

<style lang="less" scoped>
.data-apply {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 16px;
    .search-form {
        padding: 16px 10px 4px;
        background-color: #fff;
        border-radius: 4px;
        .add {
            margin-left: auto;
        }
    }
    .data-table {
        margin-top: 16px;
        padding: 16px;
        flex: 1;
        height: 0;
        background-color: #fff;
        .divider {
            color: #d6dae0;
        }
        .gray {
            color: #999 !important;
            pointer-events: none;
            cursor: not-allowed !important;
        }

        .underLine-btn {
            text-decoration: underline;
            cursor: pointer;
            color: #0082f9;
        }
        /deep/ .el-table .el-table__fixed-right {
            // height: auto !important;
            // bottom: 18px; // 改为自动高度后，设置与父容器的底部距离，高度会动态改变，值可以设置比滚动条的高度稍微大一些
             height: calc(100% - 14px) !important;
        }
    }
}
</style>
