<template>
    <div class="timeTrajectory">
        <searchBar :fields="fields" :form="form">
            <el-button
                class="highlyavailable-primary-btn"
                type="primary"
                size="small"
                @click="search()"
                >查询</el-button
            >
        </searchBar>
        <div class="content">
            <gisCom class="gis" @loaded="loaded"></gisCom>
            <dataTable
                class="dataTable"
                :columns="columns"
                :data="tableData"
                :pagination="pagination"
                :total="total"
                stripe
                :updateTable="getTableData"
            >
            </dataTable>
        </div>
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import { fields, tableColumns } from '../constatnt/timeTrajectory.js';
import dataTable from '_com/tables/dataTableLast.vue';
import gisCom from '_com/gisMap/index.vue';
import linePoint from '_com/gisMap/layers/linePoint.js';
export default {
    name: 'timeTrajectory',
    components: {
        searchBar,
        dataTable,
        gisCom
    },
    data() {
        return {
            fields: fields(),
            form: {
                complaintType: 0,
                phoneNumber: '',
                startTime: '',
                endTime: '',
                stayDuration: 10
            },
            columns: tableColumns,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            total: 0
        };
    },
    created() {
        this.getWhitePhone();
    },
    methods: {
        getWhitePhone() {
            this.highGetPost('realTimeToolSeviceApi', 'getWhitePhone', {}, '获取白名单号码').then(
                ({ data }) => {
                    this.fields = fields({
                        msisdn: data
                    });
                }
            );
        },
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            this.highGetPost(
                'realTimeToolSeviceApi',
                'queryPageDetails',
                {
                    ...this.form,
                    isPagination: true,
                    pageNum: curPage,
                    pageSize
                },
                '投诉用户指定时间轨迹呈现列表查询'
            ).then(({ data }) => {
                this.tableData = data.list;
                this.total = data.totalSize;
                if (data.list.length) {
                    this.queryTrajectoryDetails();
                }
            });
        },
        queryTrajectoryDetails() {
            this.highGetPost(
                'realTimeToolSeviceApi',
                'queryTrajectoryDetails',
                this.form,
                '投诉用户指定时间轨迹详情查询'
            ).then(({ data }) => {
                const { list, rectangleRange } = data;
                this.linePointLayer.createLinePoint(
                    [
                        {
                            points: list.map((item) => {
                                return {
                                    lat: item.lat,
                                    lng: item.lng,
                                    ht: 0
                                };
                            }),
                            color: 0x1664ff
                        }
                    ],
                    list.map((item) => {
                        return {
                            lat: item.lat,
                            lng: item.lng,
                            ht: 0.1,
                            width: 20,
                            color: 0x1664ff
                        };
                    }),
                    rectangleRange,
                    2
                );
            });
            // const list = [
            //     { lat: 39.90731558278251, lng: 116.39030839333487 },
            //     { lat: 39.90831558278251, lng: 116.39140839333487 },
            //     { lat: 39.90711558278251, lng: 116.39190839333487 },
            //     { lat: 39.90700558278251, lng: 116.39130839333487 }
            // ];
            // const rectangleRange = {
            //     lTopLng: 116.39030839333487,
            //     lTopLat: 39.90700558278251,
            //     rBtmLng: 116.39190839333487,
            //     rBtmLat: 39.90831558278251
            // };
        },
        loaded(g) {
            this.linePointLayer = linePoint(g, {
                name: '点连线',
                material: g.meshList.img.getMaterial({
                    url: require('../../../../img/gis/circle-point.png'),
                    opacity: 1
                })
            });
            this.search();
        }
    }
};
</script>

<style lang="less" scoped>
.timeTrajectory {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 1.33rem;
}
.content {
    width: 100%;
    flex: 1;
    display: flex;
    .gis {
        width: calc(60% - 10px);
        height: 100%;
        margin-right: 10px;
    }
    .dataTable {
        width: 40%;
        height: 100%;
    }
}
</style>
