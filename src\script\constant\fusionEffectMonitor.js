const fields = (mapOpts = {}) => {
    return [
        {
            prop: 'chName',
            label: '省份:',
            labelWidth: '50px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: false, // 省份不可清空,必选
            },
            slot: {
                element: 'el-option',
                enums: mapOpts.chName.map(i => ({ label: i.label, value: i.label })),
            },

            span: 3,
        },
        {
            prop: 'createTime',
            label: '创建时间:',
            labelWidth: '80px',
            element: 'el-date-picker',
            bind: {
                clearable: true,
                type: 'datetimerange',
                'range-separator': '-',
                'start-placeholder': '开始时间',
                'end-placeholder': '结束时间',
                format: 'yyyy-MM-dd HH:00:00',
                'value-format': 'yyyyMMddHH',
            },
            span: 8,
        },
        {
            span: 13
        }
    ];
};

// 未传值时，返回默认值
const getChartData = (source, defaultYAxisType) => {
    if (!source) {
        return { yAxisType: defaultYAxisType, data: undefined };
    }
    return {
        yAxisType: source.yAxisType || defaultYAxisType,
        data: source.data
    };
};

const chartList = (dataMap = {}) => {
    const { timeCoverage, matchRate, abnormalLatLong, abnormalCell, density } = dataMap;

    return [
        {
            title: '融合后时间覆盖率',
            color: '#21CCFF',
            ...getChartData(timeCoverage, 'percent')
        },
        {
            title: '工参匹配率',
            color: '#8577D3',
            ...getChartData(matchRate, 'percent')
        },
        {
            title: '异常经纬度用户比例',
            color: '#249EFF',
            ...getChartData(abnormalLatLong, 'percent')
        },
        {
            title: '异常小区数',
            color: '#D377C0',
            ...getChartData(abnormalCell, 'value')
        },
        {
            title: '位置点密度',
            color: '#47CC72',
            ...getChartData(density, 'value')
        }
    ];
};

export {
    fields,
    chartList,
};