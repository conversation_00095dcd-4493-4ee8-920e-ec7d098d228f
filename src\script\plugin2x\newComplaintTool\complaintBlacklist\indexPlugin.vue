<template>
    <div class="complaint-blacklist">
        <div class="search-form">
            <searchBar :form="form" :fields="fields">
                <el-button type="primary" @click="search">查询</el-button>
                <el-button class="add" type="primary" icon="el-icon-plus" @click="add"
                    >新增</el-button
                >
            </searchBar>
        </div>
        <dataTable
            class="data-table"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :total="total"
            :updateTable="getTableData"
            isHideUpLine
        >
            <template #isValid="{ row }">
                <span>{{ row.isValid == 1 ? '是' : '否' }}</span>
            </template>
            <template #operation="{ row }">
                <span class="global-blue-btn" @click="edit(row)">修改</span>
                <span class="divider">|</span>
                <span class="global-red-btn" @click="del(row)">删除</span>
            </template>
        </dataTable>
        <!--  -->
        <addUser
            v-if="isShowAddUser"
            :visible.sync="isShowAddUser"
            :row="curRow"
            @updateTable="search"
        />
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import { fields, tableColumns } from './constants';
export default {
    name: 'ComplaintBlacklist',
    components: {
        searchBar,
        dataTable,
        addUser: () => import('./components/addUser.vue')
    },
    data() {
        return {
            form: {
                msisdn: '',
                startTime: '',
                endTime: ''
            },
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            total: 0,
            isShowAddUser: false,
            curRow: {}
        };
    },
    computed: {
        fields() {
            return fields;
        },
        columns() {
            return tableColumns;
        }
    },
    created() {
        this.search();
    },
    methods: {
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        async getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            this.highGetPost(
                'newComplaintToolApi',
                'getblackWhiteRosterList', // 请确认实际接口方法名
                {
                    ...this.form,
                    pageNum: curPage,
                    pageSize
                },
                '投诉黑名单查询'
            ).then(({ data }) => {
                this.tableData = data.list;
                this.total = data.totalSize;
            });
        },
        add() {
            this.curRow = {};
            this.isShowAddUser = true;
        },
        del(row) {
            this.$confirm('确定删除该条记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.highGetPost(
                    'newComplaintToolApi',
                    'delBlackWhiteRoster',
                    {
                        id: row.id
                    },
                    '删除投诉黑名单'
                ).then(() => {
                    this.$message.success('删除成功');
                    this.search();
                });
            });
        },
        edit(row) {
            this.curRow = row;
            this.isShowAddUser = true;
        }
    }
};
</script>

<style lang="less" scoped>
.complaint-blacklist {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 16px;
    .search-form {
        padding: 16px 10px 4px;
        background-color: #fff;
        border-radius: 4px;
        .add {
            margin-left: auto;
        }
    }
    .data-table {
        margin-top: 16px;
        padding: 16px;
        flex: 1;
        height: 0;
        background-color: #fff;
        .divider {
            color: #d6dae0;
        }
    }
}
</style>
