<template>
	<div class="personFlowAbnormal">
		<searchBar
			:fields="fields"
			:form="form"
		>
			<el-button
				class="highlyavailable-primary-btn"
				type="primary"
				size="small"
				@click="search()"
			>查询</el-button>
		</searchBar>
		<dataTable
			class="dataTable"
			:columns="columns"
			:data="tableData"
			:pagination="pagination"
			:total="total"
			stripe
			:updateTable="getTableData"
		>
			<template #time="{ row }">
				{{ timeFormat(row.time) }}
			</template>
			<template #operation="{ row }">
				<div class="table-btn">
					<el-button
						class="table-btn-item"
						type="text"
						size="mini"
						@click="handleDetail(row)"
					>
						详情
					</el-button>
				</div>
			</template>
		</dataTable>
	</div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import { getAreasToCity, timeFormat } from '@/script/utils/method.js';
import {
	fields,
	tableColumns,
} from '@/script/constant/personFlowAbnormal.js';
import dataTable from '_com/tables/dataTableLast.vue';

export default {
	name: 'personFlowAbnormal',
	components: {
		searchBar,
		dataTable,
	},
	data() {
		return {
			form: {
				createTime: null,
				chName: '',
			},
			columns: tableColumns,
			tableData: [],
			pagination: {
				curPage: 1,
				pageSize: 15,
			},
			total: 0,
		};
	},
	computed: {
		fields() {
			const { positionDistricts } = this.$store.getters;
			return fields(
				{
					chName: getAreasToCity(positionDistricts, true),
				}
			);
		},
	},
	mounted() {
		this.search();
	},
	methods: {
		timeFormat,
		handleDetail(row) {
			const { time, s2Id } = row;
			this.$router.push({
				path: 'detail',
				query: {
					time,
					s2Id,
				},
			});
		},
		search() {
			this.pagination.curPage = 1;
			this.getTableData();
		},
		getTableData(pagination = {}) {
			const { curPage = 1, pageSize = 15 } = pagination;
			const { chName, createTime } = this.form;
			const [startTime, endTime] = createTime || [];
			this.highGetPost(
				'positionServiceApi',
				'getAlarmList',
				{
					startTime: startTime,
					endTime: endTime,
					province: chName || undefined,
					pageNumber: curPage,
					pageSize
				},
				'栅格人流异常告警服务列表查询'
			).then(({ data }) => {
				this.tableData = data.data.map((item) => {
					return {
						time: item.time,
						s2Id: item.s2Id,
						number: item.userCount,
						s2IdBase: item.baselineValue,
						numberRate: item.fluctuationRate,
					};
				});
				this.total = data.total;
			});
		},
	},
};
</script>

<style lang="less" scoped>
.personFlowAbnormal {
	width: 100%;
	height: 100%;
	padding: 1.33rem;
	display: flex;
	flex-direction: column;
}

.dataTable {
	width: 100%;
	flex: 1;
	height: 0;
}

a {
	color: #1664ff;
	text-decoration: underline;
	cursor: pointer;
}

.table-btn {
	display: flex;

	&-item {
		position: relative;
		font-size: 14px;

		&.redText {
			color: #ff4d4f;
		}

		&::after {
			position: absolute;
			right: -13px;
			top: 8px;
			width: 1px;
			background: #ebeef5;
			height: 11px;
			content: "";
		}

		&:last-child {
			&::after {
				position: absolute;
				right: -13px;
				top: 8px;
				width: 1px;
				background: #ebeef5;
				height: 0px;
				content: "";
			}
		}
	}
}
</style>
