const fields = [
    // {
    //     prop: 'displayIndicators',
    //     label: '展示指标：',
    //     labelWidth: '70',
    //     element: 'el-select',
    //     bind: {
    //         placeholder: '请选择',
    //         clearable: true,
    //     },
    //     slot: {
    //         element: 'el-option',
    //         enums: [],
    //     },
    // },
    {
        prop: 'time',
        label: '数据时间：',
        labelWidth: '40',
        element: 'el-date-picker',
        bind: {
            type: 'datetimerange',
            'range-separator': '-',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            format: 'yyyy-MM-dd HH:mm:00',
            'value-format': 'yyyy-MM-dd HH:mm:00',
        },
        span: 7,
    },
    {}
];
const tableColumns = [
    {
        prop: 'time',
        label: '数据时间',
    },
    {
        label: '人群画像-年龄（18-24岁）',
        child: [
            {
                prop: 'age1Main',
                label: '主库群',
            },
            {
                prop: 'age1Backup',
                label: '备用库群',
            }
        ]
    },
    {
        label: '人群画像-年龄（25-34岁）',
        child: [
            {
                prop: 'age2Main',
                label: '主库群',
            },
            {
                prop: 'age2Backup',
                label: '备用库群',
            }
        ]
    },
    {
        label: '人群画像-年龄（35-44岁）',
        child: [
            {
                prop: 'age3Main',
                label: '主库群',
            },
            {
                prop: 'age3Backup',
                label: '备用库群',
            }
        ]
    },
    {
        label: '人群画像-年龄（45-54岁）',
        child: [
            {
                prop: 'age4Main',
                label: '主库群',
            },
            {
                prop: 'age4Backup',
                label: '备用库群',
            }
        ]
    },
    {
        label: '人群画像-年龄（55岁以上）',
        child: [
            {
                prop: 'age5Main',
                label: '主库群',
            },
            {
                prop: 'age5Backup',
                label: '备用库群',
            }
        ]
    },
    {
        label: '性别-男',
        child: [
            {
                prop: 'manMain',
                label: '主库群',
            },
            {
                prop: 'manBackup',
                label: '备用库群',
            }
        ]
    },
    {
        label: '性别-女',
        child: [
            {
                prop: 'womanMain',
                label: '主库群',
            },
            {
                prop: 'womanBackup',
                label: '备用库群',
            }
        ]
    },
];


export {
    fields,
    tableColumns,
};