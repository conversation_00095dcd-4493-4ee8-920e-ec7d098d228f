<template>
	<div class="tab-wrapper">
		<div
			class="tab-wrapper-item"
			:class="[tabActive === item.key?'tab-wrapper-item-active':'', tabFit?'tab-wrapper-item-fit':'']"
			v-for="(item,index) in tabList"
			:key="index"
			@click="changeTab(item)"
		>
			{{item.name}}
		</div>
	</div>
</template>

<script>
export default {
	name: 'tab',
	props: {
		tabActive: {
			type: String,
			default: '1'
		},
		tabList: {
			type: Array,
			default: () => [],
		},
		tabFit: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		changeTab(item) {
			this.$emit('update:tabActive', item.key);
		}
	}

};
</script>

<style lang="less" scoped>
.tab-wrapper {
	width: 100%;
	padding-top: 32px;
	height: 80px;
	display: flex;
	&-item {
		width: 160px;
		height: 48px;
		padding: 0 6px;
		background: #ffffff;
		border-radius: 4px 4px 0px 0px;
		border: 1px solid #dcdcdc;
		text-align: center;
		line-height: 48px;
		font-size: 16px;
		color: #2c2c2c;
		font-weight: 600;
		cursor: pointer;
		&-active {
			background: #f6f8fa;
			box-shadow: inset 0px 4px 0px 0px #1664ff;
			border-radius: 4px 4px 0px 0px;
			border: 1px solid #dcdcdc;
			color: #1664ff;
		}
		&-fit {
			width: auto;
		}
	}
	&-item + &-item {
		margin-left: 10px;
	}
}
</style>