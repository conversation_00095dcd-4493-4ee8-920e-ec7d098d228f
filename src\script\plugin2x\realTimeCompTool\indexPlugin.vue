<template>
    <div id="realTimeCompTool">
        <div class="main-content">
            <router-view class="page-route"></router-view>
        </div>
    </div>
</template>
<script>
export default {
    components: {},
    data() {
        return {};
    },
    mounted() {
        this.onload();
    },
    methods: {
        jumpRouter(value) {
            this.openSubPath(value, '', {});
        },
        onload() {
            const origin = window.location.href;
            const lastItem = origin.substring(origin.lastIndexOf('/') + 1);
            const isSub = lastItem.split('-');
            if (isSub.length !== 1) {
                this.jumpRouter('home');
            }
        }
    }
};
</script>
<style lang="less" scoped>
#realTimeCompTool {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    background: #fff;
}

.main-content {
    width: 100%;
    height: 100%;
}
</style>
