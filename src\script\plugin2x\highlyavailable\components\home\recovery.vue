<template>
	<div class="recovery">
		<overview
			:resList="resList"
			:span="24 / resList.length"
			:styleType="2"
		/>
		<searchBar
			:fields="formCols"
			:form="form"
		>
			<el-button
				class="highlyavailable-primary-btn"
				type="primary"
				size="small"
				@click="search()"
			>查询</el-button>
		</searchBar>
		<dataTable
			class="dataTable"
			:columns="columns"
			:data="tableData"
			:pagination="pagination"
			:total="total"
			stripe
			:updateTable="getTableData"
		>
		</dataTable>
	</div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import overview from '@/script/components/overView/overview.vue';
import {
	fields,
	tableColumns,
	getOverview
} from '@/script/constant/recovery.js';
import dayjs from 'dayjs';

export default {
	name: 'recovery',
	components: {
		searchBar,
		dataTable,
		overview,
	},
	data() {
		return {
			resList: getOverview(),
			form: {
				time: this.getDefaultTimeRange(),
			},
			columns: tableColumns,
			tableData: [],
			pagination: {
				curPage: 1,
				pageSize: 15,
			},
			total: 0,
		};
	},
	computed: {
		formCols() {
			return fields();
		},
	},
	created() {
		this.search();
	},
	methods: {
		getDefaultTimeRange() {
			const endTime = dayjs();
			const startTime = dayjs().subtract(1, 'month');
			return [
				startTime.format('YYYY-MM-DD HH:mm:ss'),
				endTime.format('YYYY-MM-DD HH:mm:ss')
			];
		},
		search() {
			this.pagination.curPage = 1;
			this.getTableData();
		},
		getTableData(pagination = {}) {
			const { curPage = 1, pageSize = 15 } = pagination;
			const { time } = this.form;
			const [startTime, endTime] = time || [];
			this.highGetPost(
				'monitorApi',
				'recovery', // 请确认实际接口方法名
				{
					// statisticsType: 0,
					startTime: startTime,
					endTime: endTime,
					pageNum: curPage,
					pageSize: pageSize
				},
				'列表'
			).then(({ data }) => {
				this.resList = getOverview({
					countOfRecovery: data.countOfRecovery,
					countOfRecoveryByButton: data.countOfRecoveryByButton,
				});
				this.tableData = data.switchCountStatisticsList.map((item) => {
					return {
						...item,
						time: item.date,
						totalUserModel: item.switchCountDetails.regionCount,
						flowModel: item.switchCountDetails.crowdCount,
						portraitModel: item.switchCountDetails.profileCount,
						heatModel: item.switchCountDetails.heatCount,
						visitsModel: item.switchCountDetails.cumulativeCount,
						residentModel: item.switchCountDetails.constantModelCount,
						stayDurationCount: item.switchCountDetails.stayDurationCount
					};
				});
				this.total = data.totalCount;
			});
		},
	},
};
</script>

<style lang="less" scoped>
.recovery {
	width: 100%;
	height: 100%;
	padding: 1.33rem;
	display: flex;
	flex-direction: column;
}
.dataTable {
	width: 100%;
	flex: 1;
	height: 0;
}
</style>
