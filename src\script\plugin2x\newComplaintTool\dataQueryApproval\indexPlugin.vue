<template>
    <div class="data-apply">
        <div class="search-form">
            <searchBar :form="form" :fields="fields">
                <el-button type="primary" @click="search">查询</el-button>
            </searchBar>
        </div>
        <dataTable
            class="data-table"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :total="total"
            :updateTable="getTableData"
            isHideUpLine
        >
            <template #listType="{ row }">
                <span>{{ row.listType == 1 ? '用户轨迹稽核' : '实时事件稽核' }}</span>
            </template>
            <template #filePath="{ row }">
                <template v-if="row.filePath && row.filePath !== ''">
                    <div
                        class="underLine-btn"
                        v-for="(item, index) in row.filePath.split(',')"
                        :key="index"
                        @click="downloadFile(row, item)"
                    >
                        {{ item }}
                    </div>
                </template>
            </template>
            <template #status="{ row }">
                <span>{{ statusList[Number(row.status)] }}</span>
            </template>
            <template #isValid="{ row }">
                <span>{{ row.isValid == 1 ? '是' : '否' }}</span>
            </template>
            <template #attachment="{ row }">
                <span class="global-blue-btn" @click="view(row)">{{ row.filename }}</span>
            </template>
            <template #operation="{ row }">
                <span class="global-blue-btn" @click="applyOrder(row)">审批</span>
                <span class="divider">|</span>
                <span
                    :class="{ gray: !isShowDataQuery(row) }"
                    class="global-blue-btn"
                    @click="dataQuery(row)"
                    >数据查询</span
                >
            </template>
        </dataTable>
        <approvalWorkOrder
            v-if="isShowApprovalWorkOrder"
            :visible.sync="isShowApprovalWorkOrder"
            :row="curRow"
            @updateTable="search"
        />
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import { fields, tableColumns, statusList } from './constants';
export default {
    name: 'dataQueryApply',
    components: {
        searchBar,
        dataTable,
        approvalWorkOrder: () => import('./components/approvalWorkOrder.vue')
    },
    data() {
        return {
            form: {
                listType: '',
                taskName: '',
                queryMsisdn: '',
                status: ''
            },
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            total: 0,
            curRow: {},
            isShowApprovalWorkOrder: false,
            statusList
        };
    },
    computed: {
        fields() {
            return fields;
        },
        columns() {
            return tableColumns;
        }
    },
    created() {
        this.search();
    },
    methods: {
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        async getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            this.highGetPost(
                'newComplaintToolApi',
                'getPermissionList', // 请确认实际接口方法名
                {
                    ...this.form,
                    pageNum: curPage,
                    pageSize
                },
                '权限查询列表'
            ).then(({ data }) => {
                this.tableData = data.list;
                this.total = data.totalSize;
            });
        },
        applyOrder(row) {
            this.curRow = row;
            this.isShowApprovalWorkOrder = true;
        },
        downloadFile(row, fileName) {
            const params = { type: 2, fileName: `${row.id}_${fileName}` };
            this.highGetPost(
                'newComplaintToolApi',
                'downloadFile', // 请确认实际接口方法名
                params,
                '附件下载'
            ).then((res) => {
                const blob = new Blob([res]);
                const linkNode = document.createElement('a');
                linkNode.download = `${row.id}_${fileName}`; //a标签的download属性规定下载文件的名称
                linkNode.style.display = 'none';
                linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
                document.body.appendChild(linkNode);
                linkNode.click(); //模拟在按钮上的一次鼠标单击
                URL.revokeObjectURL(linkNode.href); // 释放URL 对象
                document.body.removeChild(linkNode);
            });
        },
        isShowDataQuery(row) {
            const { listType, status } = row;
            if (Number(listType) === 1) {
                return true;
            }
            return Number(status) === 5;
        },
        dataQuery(row) {
            const path = row.listType == 1 ? 'trackQuery' : 'liveEvents';
            this.$router.push({
                path,
                query: row
            });
        }
    }
};
</script>

<style lang="less" scoped>
.data-apply {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 16px;
    .search-form {
        padding: 16px 10px 4px;
        background-color: #fff;
        border-radius: 4px;
    }
    .data-table {
        margin-top: 16px;
        padding: 16px;
        flex: 1;
        height: 0;
        background-color: #fff;
        .divider {
            color: #d6dae0;
        }
        .gray {
            color: #999 !important;
            pointer-events: none;
            cursor: not-allowed !important;
        }
        .underLine-btn {
            text-decoration: underline;
            cursor: pointer;
            color: #0082f9;
        }
    }
}
</style>
