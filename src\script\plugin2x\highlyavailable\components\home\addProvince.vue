<template>
  <el-dialog
    class="add-version"
    :title="type=='add'?'省份新增':'数据源切换'"
    top="80px"
    width="40%"
    :visible="visible"
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 主体 -->
    <div class="add-region__main">
      <searchBar :fields="fields" :form="form">
      </searchBar>
    </div>
    <!-- 底部 -->
    <div slot="footer" class="add-region__footer">
      <el-button size="small" @click="handleClose">取消</el-button>
      <el-button type="primary" size="small" @click="sure">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import { getAreasToCity } from '@/script/utils/method.js';
export default {
  name: "addProvince",
  components: { searchBar },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
            type: String,
            default: 'add',
        },
    rowData:{
        type: Object,
        default: null,

    }
  },
  data() {
    return {
      form:{
       provinceCodeList:[],
       primaryDatasource:'',
       standbyDatasource:'',
      },
      fields: [
           {
            prop: 'provinceCodeList',
            label: '省份:',
            labelWidth: '90px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
                multiple: true,
                'collapse-tags': true,
            },
            slot: {
                element: 'el-option',
                enums: getAreasToCity(this.$store.getters.highDistricts, true),
            },

            span: 24,
        },
          {
            prop: 'primaryDatasource',
            label: '主数据源:',
            labelWidth: '90px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: 'ck1', value: 1 },
                    { label: 'ck2', value: 2 },
                    { label: 'ck3', value: 3 },
                ],
            },
            span: 24,
        },
        {
            prop: 'standbyDatasource',
            label: '备用数据源:',
            labelWidth: '90px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: 'ck1', value: 1 },
                    { label: 'ck2', value: 2 },
                    { label: 'ck3', value: 3 },
                ],
            },
            span: 24,
        },
      ],
    };
  },
  methods: {
    sure() {
        if(this.type == 'add'){
            this.add();
        }else{
            this.change();
        }
      
    },
    add(){
        return this.highGetPost('monitorApi', 'ckChangeAdd', this.form, 'CK省份新增').then(
                (res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.$message.success(res.returnMsg);
                        this.$emit("update:visible", false);
                        this.$emit('updateTable');
                    } else {
                        this.$message.error(res.returnMsg);
                    }
                    return res;
                }
            );
    },
    change(){
         console.log('表格数据',this.form);
        return this.highGetPost('monitorApi', 'ckChangeUpdate', this.form, 'CK数据源切换').then(
                (res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.$message.success(res.returnMsg);
                        this.$emit("update:visible", false);
                        this.$emit('updateTable');
                    } else {
                        this.$message.error(res.returnMsg);
                    }
                    return res;
                }
            );
    },
    handleClose() {
      this.$emit("update:visible", false);
    },
  },
  watch:{
    type:{
        handler(val,oldVal){
            if(val == 'change'){
                this.fields = this.fields.filter(item=>{
                    return item.prop != 'provinceCodeList'
                })
                this.form.provinceCodeList = [this.rowData.provinceCode];
                this.form.primaryDatasource = this.rowData.primaryDatasource;
            }
        },
        immediate:true,
        deep:true,
    }
  }
};
</script>

<style lang="less" scoped>
.add-version {
  .grayscale-value-item{
    display: flex;
    gap: 10px;
    align-items: center;
    margin-top: 10px;
    .item-input-text{
        width: 80%;
    }
    .item-text{
        font-size: 16px;
        cursor: pointer;
        color: #1664FF;
    }
  }
  &__main {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  &__footer {
    text-align: center;
  }
  /deep/ .el-dialog {
    display: flex;
    flex-direction: column;
    border-radius: 10px;
  }
  /deep/ .el-dialog__header {
    position: relative;
    padding: 20px;
    border-bottom: 1px solid #ccc;
    .el-dialog__title {
      font-size: 18px;
      font-weight: bold;
    }
    .el-dialog__headerbtn {
      top: 19px;
      font-size: 18px;
    }
  }
  /deep/ .el-dialog__body {
    padding: 20px;
    flex: 1;
    height: 0;
  }
  /deep/ .el-dialog__footer {
    padding: 20px;
    border-top: 1px solid #ccc;
  }
}
</style>
