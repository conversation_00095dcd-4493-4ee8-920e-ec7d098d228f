<template>
    <div class="blackAndWhiteList">
        <commonComp
            ref="commonComp"
            :fieldsList="fields"
            :formList="form"
            :tableColumns="tableColumns"
            :dialogFieldsList="dialogFields"
            :dialogFormList="dialogForm"
            @getTableData="getTableData"
            @handleDelete="handleDelete"
            @addOrEdit="addOrEdit"
            @restore="restore"
        ></commonComp>
    </div>
</template>

<script>
import commonComp from './common.vue';
import { fields, tableColumns, dialogFields } from '../constatnt/blackAndWhiteList.js';
export default {
    name: 'blackAndWhiteList',
    components: {
        commonComp
    },
    data() {
        return {
            fields: fields(),
            form: {
                listType: 1,
                complaintType: 1,
                startTime: '',
                endTime: '',
                msisdn: '',
                authStime: '',
                authEtime: ''
            },
            tableColumns,
            dialogFields: dialogFields(),
            dialogForm: {
                listType: 1,
                complaintType: 1,
                isValid: 0,
                msisdn: '',
                authStime: '',
                authEtime: ''
            }
        };
    },
    created() {
        this.getWhitePhone();
    },
    methods: {
        getWhitePhone() {
            this.highGetPost('realTimeToolSeviceApi', 'getWhitePhone', {}, '获取白名单号码').then(
                ({ data }) => {
                    this.fields = fields({
                        msisdn: data
                    });
                    this.dialogFields = dialogFields({
                        msisdn: data
                    });
                }
            );
        },
        getTableData(pagination = {}, form, cb) {
            const { curPage = 1, pageSize = 15 } = pagination;
            this.highGetPost(
                'realTimeToolSeviceApi',
                'getBlackWhite',
                {
                    ...form,
                    pageNum: curPage,
                    pageSize
                },
                '获取白名单列表'
            ).then(({ data }) => {
                cb && cb(data);
            });
        },
        handleAdd() {
            this.$refs.commonComp.handleAdd();
        },
        handleDelete(row, cb) {
            this.highGetPost(
                'realTimeToolSeviceApi',
                'removeBlackWhiteRoster',
                {
                    id: row.id
                },
                '删除白名单'
            ).then((res) => {
                cb && cb(res);
            });
        },
        addOrEdit(form, type, row, cb) {
            if (type === 'add') {
                this.highGetPost(
                    'realTimeToolSeviceApi',
                    'addBlackWhiteRoster',
                    {
                        ...form
                    },
                    '新增白名单'
                ).then((res) => {
                    cb && cb(res);
                });
            } else {
                this.highGetPost(
                    'realTimeToolSeviceApi',
                    'modifyBlackWhiteRoster',
                    {
                        id: row.id,
                        ...form
                    },
                    '修改白名单'
                ).then((res) => {
                    cb && cb(res);
                });
            }
        },
        restore(row, { id }, cb) {
            this.highGetPost(
                'realTimeToolSeviceApi',
                'modifyBlackWhiteRoster',
                {
                    id,
                    ...row,
                    isValid: 1
                },
                '恢复白名单'
            ).then((res) => {
                cb && cb(res);
            });
        }
    }
};
</script>

<style lang="less" scoped>
.blackAndWhiteList {
    width: 100%;
    height: 100%;
}
</style>
