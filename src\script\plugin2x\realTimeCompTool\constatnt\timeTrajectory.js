const fields = (Opts = { msisdn: [] }) => {
    return [
        {
            prop: 'complaintType',
            label: '投诉类型：',
            labelWidth: '70',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '实时', value: 0 },
                    { label: '离线', value: 1 }
                ]
            },
            span: 3
        },
        {
            prop: 'phoneNumber',
            label: '号码：',
            labelWidth: '70',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: Opts.msisdn.map((i) => ({ label: i.msisdn, value: i.msisdn }))
            },
            span: 3
        },
        {
            prop: 'startTime',
            label: '开始时间：',
            labelWidth: '70',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss'
            },
            span: 4
        },
        {
            prop: 'endTime',
            label: '结束时间：',
            labelWidth: '70',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss'
            },
            span: 4
        },
        {
            prop: 'stayDuration',
            label: '驻留时长：',
            labelWidth: '70',
            element: 'el-input-number',
            bind: {
                placeholder: '请输入',
                clearable: true,
                'controls-position': 'right',
                min: 1
            },
            span: 3
        },
        {
            span: 2
        }
    ];
};
const tableColumns = [
    {
        prop: 'time',
        label: '信令时间'
    },
    {
        prop: 'baseStationId',
        label: '基站标识'
    },
    {
        prop: 'lng',
        label: '基站经度'
    },
    {
        prop: 'lat',
        label: '基站纬度'
    },
    {
        prop: 'isStayPoint',
        label: '是否驻留点'
    },
    {
        prop: 'stayTime',
        label: '驻留时间（分钟）'
    }
];

export { fields, tableColumns };
