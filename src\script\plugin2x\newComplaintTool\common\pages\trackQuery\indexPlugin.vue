<template>
    <div class="track-query">
        <div class="top">
            <headTitle :title="title" @back="back" />
            <baseInfo :infoItems="infoItems" />
            <div class="search-form">
                <searchBar :form="form" :fields="fields">
                    <el-button type="primary" @click="search">查询</el-button>
                </searchBar>
            </div>
        </div>
        <div class="bottom">
            <gisMap @loaded="loaded" />
            <orderDetail
                ref="orderDetail"
                :defTableData="defTableData"
                :defTotal="defTotal"
                :form="form"
                @rowClick="rowClick"
            />
        </div>
    </div>
</template>

<script>
import headTitle from '../../components/headTitle.vue';
import baseInfo from '../../components/baseInfo.vue';
import searchBar from '_com/searchForm/index.vue';
import gisMap from '_com/gisMap/index.vue';
import { createLine, createPoint, createPlane } from '../../components/layers';
import { fields } from './constants.js';
import blueImg from '@/img/gis/innerBaseStation.png';
export default {
    name: 'trackQuery',
    components: {
        headTitle,
        searchBar,
        baseInfo,
        gisMap,
        orderDetail: () => import('./components/details.vue')
    },
    data() {
        return {
            title: '用户轨迹查询',
            infoItems: [
                {
                    label: '投诉工单ID',
                    prop: 'id',
                    value: '-'
                },
                {
                    label: '用户号码',
                    prop: 'queryMsisdn',
                    value: '-'
                }
            ],
            form: {
                dataStime: '',
                dataEtime: ''
            },
            defTableData: [],
            defTotal: 0,
            curRow: null
        };
    },
    computed: {
        fields() {
            return fields;
        },
        queryData() {
            return this.$route.query;
        }
    },
    created() {
        this.initData();
    },
    methods: {
        loaded(g) {
            this.Line = createLine(g);
            this.Point = createPoint(g, {
                img: blueImg
            });
            this.Plane = createPlane(g);
            // 地图加载完成
            this.search();
        },
        initData() {
            // baseInfo
            for (const item of this.infoItems) {
                const prop = item.prop;
                if (this.queryData[prop]) {
                    item.value = this.queryData[prop];
                }
            }
            // date
            const { dataStime, dataEtime } = this.queryData;
            Object.assign(this.form, { dataStime, dataEtime });
        },
        search() {
            this.getBaseStations();
            this.$refs.orderDetail.pagination.curPage = 1;
        },
        getBaseStations() {
            const { queryMsisdn } = this.queryData;
            const { dataStime, dataEtime } = this.form;
            const params = {
                msisdn: queryMsisdn,
                startTime: dataStime,
                endTime: dataEtime,
                status: 1,
                pageNum: 1,
                pageSize: 9999999
            };
            this.highGetPost('newComplaintToolApi', 'getBaseStations', params, '获取基站数据').then(
                ({ data }) => {
                    console.log(data);
                    const { stayList = [], totalCount } = data || [];
                    const points = stayList.map((item) => ({
                        latLng: { lat: item.cgiLat, lng: item.cgiLng },
                        data: item
                    }));
                    this.Point.draw(points);
                    this.Line.draw([
                        {
                            points: points.map((item) => item.latLng),
                            color: 0xf74041
                        }
                    ]);
                    this.defTableData = stayList;
                    this.defTotal = totalCount;
                }
            );
        },
        rowClick(row) {
            if (this.prePoint) {
                const { mesh } = this.prePoint;
                this.Point.setPointSize(mesh, 0, 50);
            }
            const graphs = this.Point.getGraphs();
            const curPoint = graphs.find((item) => item.data.lacCell === row.lacCell);
            if (curPoint) {
                const { mesh, latLng } = curPoint;
                this.Point.setPointSize(mesh, 0, 90);
                this.Point.toMove([latLng]);
                this.prePoint = curPoint;
            } else {
                console.warn('No point found with lacCell:', row.lacCell);
            }

            this.curRow = row;
        },
        back() {
            this.$router.back();
        }
    }
};
</script>

<style lang="less" scoped>
.track-query {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .top {
        .search-form {
            padding: 16px 10px 4px;
            background-color: #fff;
            box-shadow: 0px 4px 8px 0px rgba(0, 47, 136, 0.2);
        }
    }
    .bottom {
        position: relative;
        height: calc(100% - 176px);
        background-color: #fff;
    }
}
</style>
