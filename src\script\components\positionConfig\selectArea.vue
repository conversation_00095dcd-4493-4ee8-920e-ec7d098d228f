<template>
    <div class="select-area" :class="{ 'w-260': isMultiple && selectVal === 1 }" :style="boxH">
        <div class="select-area__head">请选择{{ title || selectAreaType }}</div>
        <div class="select-area__body">
            <el-form class="select-area__search" size="mini" @submit.native.prevent>
                <el-select
                    v-if="selectOps.length"
                    v-model="selectVal"
                    class="selectType"
                    @change="handleSelect"
                    placeholder="请选择"
                >
                    <el-option
                        v-for="opt in selectOps"
                        :key="opt.label"
                        :value="opt.value"
                        :label="opt.label"
                    />
                </el-select>
                <!-- 远程搜索 -->
                <div class="remote-search">
                    <el-select
                        v-model="checkedList"
                        style="width: 100%"
                        multiple
                        filterable
                        remote
                        collapse-tags
                        reserve-keyword
                        placeholder=""
                        :remote-method="remoteMethod"
                        @focus="isShowTip = false"
                        @visible-change="handleVisible"
                        @change="handleRemoteChange"
                    >
                        <el-option
                            v-for="opt in searchOps"
                            :key="opt.label"
                            :value="opt.value"
                            :label="opt.label"
                        />
                    </el-select>
                    <span v-if="isShowTip" class="custom-placeHolder">{{
                        `搜索${selectLabel || selectAreaType}`
                    }}</span>
                </div>
            </el-form>
            <!-- 主体 -->
            <div class="select-area__list">
                <template v-for="(list, index) in lists">
                    <!-- 单项 -->
                    <ul v-if="index !== levels" class="list" :key="index">
                        <template v-for="(item, inx) in list">
                            <li
                                :class="{
                                    isActive:
                                        curItemArr[index] && curItemArr[index].label === item.label,
                                }"
                                :key="`${item.label}${inx}`"
                                :id="item.value"
                                :title="item.label"
                                @click="handleClkItem(item, index)"
                            >
                                <span class="text-ellipsis">{{ item.label }}</span>
                                <i class="el-icon-arrow-right"></i>
                            </li>
                        </template>
                    </ul>
                    <!-- 复选框 -->
                    <div v-else :key="`${index}-checkbox`" class="custom-checkbox">
                        <el-checkbox
                            class="all-checkbox"
                            v-model="checkedAll"
                            :disabled="isDisabled"
                            @change="selectAll(arguments[0], list, index)"
                            ><a @click="isSelectAll = true">全选</a>/<a @click="isSelectAll = false"
                                >反选</a
                            ></el-checkbox
                        >
                        <el-checkbox-group
                            v-model="checkedList"
                            class="checklist"
                            @change="handleCheckAllChange(arguments[0], list)"
                        >
                            <el-checkbox
                                v-for="(item, inx) in list"
                                :class="{
                                    isActive:
                                        curItemArr[index] && curItemArr[index].label === item.label,
                                    labelWidth: levels > 0,
                                }"
                                :label="formatLabel(item.value, index)"
                                :title="item.label"
                                :id="item.value"
                                :key="`${item.label}${inx}`"
                                :disabled="isDisabled"
                                @change="() => handleChangeItem(item, index)"
                            >
                                <span>{{ item.label }}</span>
                            </el-checkbox>
                        </el-checkbox-group>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
export default {
    name: 'select-area',
    props: {
        value: {
            type: Array,
            default: () => [],
            required: true,
        },
        title: {
            type: String,
            default: '',
        },
        selectOps: {
            type: Array,
            default: () => [],
        },
        treeData: {
            type: Array,
            default: () => [],
        },
        panelLevels: {
            type: Number,
            default: 3,
        },
        tags: {
            type: Array,
            default: () => [],
        },
        prefix: {
            type: String,
            default: '',
        },
        isMultiple: {
            type: Boolean,
            default: false,
        },
    },
    model: {
        prop: 'value',
        event: 'change',
    },
    data() {
        return {
            searchVal: [],
            searchOps: [],
            selectVal: 1,
            curCheckedLabels: '',
            lists: [],
            curItemArr: [],
            mapCheckedList: {},
            mapAreaNames: ['省份', '地市', '区县'],
            isShowTip: true,
            // specialCities: ['北京市', '天津市', '重庆市', '上海市'],
            provinceSet: [], // 选中省份集合
            checkedAll: false,
            preCheckedList: [], //记录前次选中集合
            isSelectAll: true,
        };
    },
    computed: {
        resourcesList() {
            return window.baseCompExt2.userInfor.resourcesList;
        },
        winH() {
            return window.screen.height;
        },
        boxH() {
            let style = {};
            style.height = '500px';
            return style;
            // if (window.innerHeight >= 963) {
            //   style.height = '420px';
            // } else if (window.innerHeight >= 931) {
            //   style.height = '390px';
            // }
            // return style;
        },
        checkedList: {
            get() {
                this.setProvinceSet(this.value);
                return this.value;
            },
            set(val) {
                this.$nextTick(() => {
                    this.$emit(
                        'update:tags',
                        val.map((value) => {
                            return {
                                value,
                                label: this.mapCheckedList[value] || this.formatRegionLabel(value),
                            };
                        })
                    );
                });
                this.$emit('change', val);
                //
                this.setProvinceSet(val);
            },
        },
        levels() {
            return this.isAllowSelect ? this.selectVal : this.panelLevels - 1;
        },
        selectAreaType() {
            return this.mapAreaNames[this.levels];
        },
        selectLabel() {
            const curSelectItem =
                this.selectOps.length &&
                this.selectOps.find((item) => item.value === this.selectVal);
            if (!curSelectItem) {
                return '';
            }
            return curSelectItem.label;
        },
        isAllowSelect() {
            return Boolean(this.selectOps.length);
        },
        labelInx() {
            if (this.isAllowSelect) {
                return this.levels;
            }
            return this.levels ? this.levels - 1 : this.levels;
        },
        regionNames() {
            return this.$store.state.highAvailable.regionNames;
        },
        regionData() {
            return this.$store.state.highAvailable.regionData;
        },
        cityData() {
            return this.$store.state.highAvailable.cityData;
        },
        isDisabled() {
            if (this.levels === 2) return false;
            let provinceCnt = 3;
            if (
                (this.levels === 0 && this.provinceSet.length >= provinceCnt) ||
                (this.provinceSet.length >= provinceCnt &&
                    !this.provinceSet.includes(this.curItemArr[0].value))
            ) {
                this.$message({
                    type: 'warning',
                    duration: 6000,
                    offset: 50,
                    showClose: true,
                    message: `省份选择不能超过${provinceCnt}个，若要选择其他省份，请在右边已选省份中删除并重新选择。`,
                });
                return true;
            }
            return false;
        },
        isCityCountyHandle() {
            return this.levels === 2 || (this.levels === 1 && !this.isMultiple);
        },
    },
    watch: {
        levels(newLevels) {
            this.lists = [this.treeData, [], []].slice(0, newLevels + 1);
            this.curItemArr.length = newLevels + 1;
        },
        curItemArr: {
            handler(newCurItems) {
                if (!Array.isArray(newCurItems)) return;
                const prefixes = [...new Set(newCurItems.map((item) => item.label))];
                this.$emit('update:prefix', prefixes.join(''));
            },
            deep: true,
            immediate: true,
        },
        treeData(val) {
            this.lists = [this.treeData, [], []].slice(0, this.levels + 1);
        },
    },
    mounted() {
        //
        this.lists = [this.treeData, [], []].slice(0, this.levels + 1);
    },
    methods: {
        handleVisible(value) {
            if (!value) {
                this.isShowTip = true;
            }
        },
        initData(defCheckVal, mapCheckedData) {
            const defCheckValues = defCheckVal.split('/');
            defCheckValues.forEach((val, inx) => {
                const curItem = this.lists[inx].find((item) => item.value === val);
                if (defCheckValues.length - 1 !== inx) {
                    this.handleClkItem(curItem, inx);
                }
            });
            if (mapCheckedData) {
                this.mapCheckedList = mapCheckedData;
            }
        },
        handleClkItem(item, index) {
            this.lists = [...this.lists.slice(0, index + 1), item.children];

            this.$set(this.lists, index + 1, item.children);
            if (index === 0 && this.levels === 2) {
                this.$set(this.lists, index + 2, []);
            }
            this.$set(this.curItemArr, index, item);
            if (
                this.preCheckedList[this.getPrefix(this.levels)] &&
                this.preCheckedList[this.getPrefix(this.levels)].length
            ) {
                this.checkedAll = true;
            } else {
                this.checkedAll = false;
            }
        },
        handleChangeItem(item, index) {
            this.$set(this.curItemArr, index, item);
            const { labels, values } = this.getCurItemData(this.curItemArr);
            this.mapCheckedList[values.join('/')] = [...new Set(labels)].join('');
            // 清空之前的项
            let isChangeCheckedType = false;
            const curPrefix = labels.slice(0, this.labelInx).join('/');
            if (this.curCheckedLabels && this.curCheckedLabels !== curPrefix) {
                this.checkedList = [values.join('/')];
                isChangeCheckedType = true;
            }
            this.curCheckedLabels = curPrefix;
            this.$emit('checkedArea', item, this.curItemArr, isChangeCheckedType);
            this.preCheckedList[this.getPrefix(index)] = [];
        },
        // 滚动条滚动到锚点位置
        goAnchor(selector) {
            selector.forEach((item, index) => {
                if (this.levels === 0) {
                    document.getElementsByClassName('checklist')[0].scrollTo({
                        top: document.getElementById(item).offsetTop - 80,
                        behavior: 'smooth',
                    });
                } else if (index === 0) {
                    document.getElementsByClassName('list')[0].scrollTo({
                        top: document.getElementById(item).offsetTop - 80,
                        behavior: 'smooth',
                    });
                } else {
                    this.$nextTick(() => {
                        document.getElementsByClassName('checklist')[0].scrollTo({
                            top: document.getElementById(item).offsetTop - 105,
                            behavior: 'smooth',
                        });
                    });
                }
            });
        },
        handleCheckAllChange(val, list) {
            // 判断是否全选
            const checkedCount = val.length;
            this.checkedAll = checkedCount === list.length;
        },
        getCurItemData(curItemArr) {
            const labels = [],
                values = [];
            curItemArr.forEach((item) => {
                labels.push(item.label);
                values.push(item.value);
            });
            return { labels, values };
        },
        formatLabel(curVal, inx) {
            return this.curItemArr
                .slice(0, inx)
                .map((item) => item.value)
                .concat(curVal)
                .join('/');
        },
        handleSelect(value) {
            this.$emit('selectArea', value, this.selectLabel);
        },
        // 远程搜索功能
        remoteMethod(queryVal) {
            if (!queryVal) {
                this.searchOps = [];
            } else if (this.levels === 1) {
                // 市
                if (this.isMultiple) {
                    this.searchOps = _.uniqWith(
                        this.regionData
                            .filter((item) => item.cityName.includes(queryVal))
                            .map(({ cityName, cityCode, provinceCode, provinceName }) => {
                                return {
                                    label: `${provinceName}/${cityName}`,
                                    value: `${provinceCode}/${cityCode}`,
                                };
                            }),
                        _.isEqual
                    );
                } else {
                    this.searchOps = this.cityData
                        .filter((item) => item.cityName.includes(queryVal))
                        .map(({ cityName, cityCode, provinceCode, provinceName }) => {
                            return {
                                label: `${provinceName}/${cityName}`,
                                value: `${provinceCode}/${cityCode}`,
                            };
                        });
                }
            } else if (this.levels === 2) {
                // 县
                this.searchOps = this.regionData
                    .filter((item) => item.districtName.includes(queryVal))
                    .map(
                        ({
                            cityName,
                            cityCode,
                            provinceCode,
                            provinceName,
                            districtName,
                            districtCode,
                        }) => {
                            return {
                                label: `${provinceName}/${cityName}/${districtName}`,
                                value: `${provinceCode}/${cityCode}/${districtCode}`,
                            };
                        }
                    );
            } else {
                // 省
                this.searchOps = this.treeData.filter((item) => item.label.includes(queryVal));
            }
        },
        formatRegionLabel(regionIds) {
            const regionsArr = regionIds.split('/');
            const lastId = regionsArr[regionsArr.length - 1];
            const regionNames = [...new Set(this.regionNames[lastId].split('/'))];
            return regionNames.join('');
        },
        handleRemoteChange(curCheckList) {
            if (!curCheckList.length) return;
            const lastItem = curCheckList[curCheckList.length - 1];
            this.initData(lastItem);
            if (lastItem) {
                const values = lastItem.split('/');
                const lastValue = values[values.length - 1];
                const names = this.regionNames[lastValue].split('/');
                const lastName = names[names.length - 1];
                this.handleChangeItem({ label: lastName, value: lastValue }, values.length - 1);
                this.goAnchor(values);
            }
        },
        setProvinceSet(list) {
            const provinceSet = [
                ...new Set(
                    list.map((value) => {
                        const values = value.split('/');
                        return values[0];
                    })
                ),
            ];
            if (provinceSet.length <= 10) {
                this.provinceSet = provinceSet;
            }
        },
        // 全选/反选数据处理
        selectAll(value, list, inx) {
            if (!value) {
                this.preCheckedList[this.getPrefix(inx)] = [];
                let preCheckedList = [];
                Object.values(this.preCheckedList).forEach((item) => {
                    preCheckedList = preCheckedList.concat(item);
                });
                this.checkedList = [...preCheckedList];
                return;
            }
            const data = list.map((item) => {
                return this.formatLabel(item.value, inx);
            });
            const { labels } = this.getCurItemData(this.curItemArr);
            const curPrefix = labels.slice(0, this.labelInx).join('/');
            if (
                this.isCityCountyHandle &&
                (!this.curCheckedLabels || this.curCheckedLabels === curPrefix)
            ) {
                let checkedList = [...this.checkedList, ...data];
                let preList = data;
                if (!this.isSelectAll) {
                    const intersectionlist = this.handlerIntersection(data, this.checkedList);
                    const diff = this.handlerDifference(data, intersectionlist);
                    const diffCheck = this.handlerDifference(this.checkedList, intersectionlist);
                    checkedList = [...diffCheck, ...diff];
                    preList = diff;
                }
                this.checkedList = [...new Set(checkedList)];
                this.preCheckedList[this.getPrefix(inx)] = preList;
            } else {
                const checkedList = this.isSelectAll
                    ? data
                    : this.handlerDifference(data, this.checkedList);
                this.checkedList = checkedList;
                this.preCheckedList = {};
                this.preCheckedList[this.getPrefix(inx)] = checkedList;
            }
            this.curCheckedLabels = curPrefix;
            this.$emit('checkedArea', '', '', true);
        },
        // 求差集
        handlerDifference(arr1, arr2) {
            const arr = arr1.filter((x) => !arr2.some((y) => y === x));
            return arr;
        },
        // 求交集
        handlerIntersection(arr1, arr2) {
            const arr = arr1.filter((x) => arr2.some((y) => y === x));
            return arr;
        },
        // 获取前缀
        getPrefix(inx) {
            return this.curItemArr
                .slice(0, inx)
                .map((item) => item.value)
                .join('/');
        },
    },
};
</script>
<style lang="less" scoped>
.select-area {
    margin-right: 8px;
    display: inline-block;
    height: 280px;
    background-color: #ffffff;
    border-radius: 2px;
    border: 1px solid #d9d9d9;
    &__head {
        height: 32px;
        background-color: #f6f7fa;
        padding-left: 14px;
    }

    &__body {
        height: calc(100% - 32px);
    }

    &__search {
        display: flex;
        width: 100%;
        height: 40px;
        padding: 8px 8px 0;

        .selectType {
            flex-basis: 80px;
        }
        .remote-search {
            display: inline-block;
            position: relative;
            flex: 1;
            /deep/ .el-select__tags {
                & > span {
                    display: none;
                }
            }
        }
        .custom-placeHolder {
            position: absolute;
            left: 14px;
            font-size: 12px;
            color: #999;
        }
    }

    &__list {
        display: flex;
        height: calc(100% - 40px);
        padding: 8px 8px 0px 8px;
        .list {
            margin: 0;
            padding-left: 0;
            height: 100%;
            flex: 1;
            min-width: 120px;
            list-style: none;
            overflow: auto;
            border-right: 1px solid #ccc;

            & > li {
                margin: 0 4px;
                padding: 0 8px;
                display: flex;
                align-items: center;
                justify-content: space-around;
                font-size: 13px;

                &:hover {
                    cursor: pointer;
                    background-color: #f5f9ff;
                }

                &.isActive {
                    background-color: #edf3ff;
                }
            }

            &::-webkit-scrollbar {
                width: 10px;
                height: 1px;
            }

            &::-webkit-scrollbar-thumb {
                border-radius: 10px;
                box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                background: #f6f7fa;
            }

            &::-webkit-scrollbar-track {
                /* 滚动条里面轨道 */
                box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                border-radius: 10px;
                background: transparent;
            }
        }
        .custom-checkbox {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .all-checkbox {
            padding-left: 12px;
            margin-bottom: 0px;
            height: 28px;
        }
        .checklist {
            display: flex;
            flex-direction: column;
            margin: 0;
            padding-left: 0;
            height: 100%;
            flex: 1;
            overflow: auto;
            min-width: 120px;

            & > .el-checkbox {
                margin: 0 4px;
                height: 32px;
                padding-left: 8px;
                font-size: 13px;
                /deep/ .el-checkbox__label {
                    padding-left: 7px;
                    // width: 84px;
                    vertical-align: middle;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                &:hover {
                    cursor: pointer;
                    background-color: #f5f9ff;
                }

                &.isActive {
                    background-color: #edf3ff;
                }
            }

            &::-webkit-scrollbar {
                width: 10px;
                height: 1px;
            }

            &::-webkit-scrollbar-thumb {
                border-radius: 10px;
                box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                background: #f6f7fa;
            }

            &::-webkit-scrollbar-track {
                /* 滚动条里面轨道 */
                box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                border-radius: 10px;
                background: transparent;
            }
        }
    }
}
.text-ellipsis {
    width: 75px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.labelWidth {
    /deep/ .el-checkbox__label {
        width: 84px;
    }
}
.w-260 {
    width: 260px;
}
</style>
