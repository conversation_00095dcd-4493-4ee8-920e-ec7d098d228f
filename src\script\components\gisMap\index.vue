<template>
    <div v-loading="isLoading" class="gis-com">
        <mtv-gis
            ref="gisComRef"
            class="gis"
            :totaloptions="gisOptions"
            :autoActive="false"
            @onLoad="gisOnLoad"
        />
    </div>
</template>
<script>
import gisOptions from './static/gis.js';
import { throttle } from '@/script/utils/method.js';
export default {
    name: 'gis-com',
    props: {},
    data() {
        return {
            isLoading: false,
        };
    },
    watch: {},
    created() {
        this.gisOptions = gisOptions(this.getMayType());
        this.isLoading = true;
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resizeGis);
    },
    deactivated() {
        window.removeEventListener('resize', this.resizeGis);
    },
    methods: {
        getMayType() {
            let hostName = window.location.hostname;
            if (
                hostName.indexOf('localhost') >= 0 ||
                hostName.indexOf('192.168') >= 0 ||
                hostName.indexOf('127.0') >= 0
            ) {
                return 'tx';
            }
            return 'default';
        },

        gisOnLoad() {
            this.g = this.$refs.gisComRef.getEntity();
            if (this.getMayType() === 'default') {
                this.g.tileLayerList['底图图层'].visible = false;
                this.g.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/wzlocMapUrl/grid/VectorMapAbility/HMapServer/v2.0/wmts?LAYER=ChinaMap2020Q1&SERVICE=WMTS&REQUEST=GetTile&TILEMATRIX=${z}&TILECOL=${x}&TILEROW=${y}&Version=1.0.0&FORMAT=image/png&style=&tilematrixset=EPSG:3857_base&key=ad6609c5afe3741b`;
                };
            } else {
                this.g.tileLayerList['高德底图'] &&
                    (this.g.tileLayerList['高德底图'].visible = false);
            }

            this.initGisOps();
            this.$emit('loaded', this.g);
            this.isLoading = false;
            window.addEventListener('resize', this.resizeGis);
        },
        initGisOps() {
            const g = this.g;
            g.layerList.boxSelectLayer.autoButton = false;
            g.layerList['圈选'].autoClear = false;
            g.layerList.圈选.Group.position.y = 0.005;
            g.layerList.圈选.unitMeter = true;
            g.tool.proportional.visible = true;
        },
        resizeGis: throttle(function () {
            this.g && this.g.gis.reSize();
        }, 200),
    },
};
</script>
<style lang="less" scoped>
.gis-com {
    height: 100%;
    .gis {
        height: 100%;
    }
}
</style>
