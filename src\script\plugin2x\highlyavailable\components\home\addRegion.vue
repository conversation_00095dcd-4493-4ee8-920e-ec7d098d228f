<template>
    <el-dialog
        class="add-region"
        title="区域新增"
        top="80px"
        width="80%"
        :visible="visible"
        append-to-body
        destroy-on-close
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <!-- 主体 -->
        <div class="add-region__main">
            <positionConfig ref="positionConfigRef" />
        </div>
        <!-- 底部 -->
        <div slot="footer" class="add-region__footer">
            <el-button size="small" @click="handleClose">取消</el-button>
            <el-button type="primary" size="small" @click="sure">确定</el-button>
        </div>
    </el-dialog>
</template>

<script>
import positionConfig from '@/script/components/positionConfig/index.vue';
export default {
    name: 'addRegion',
    components: {
        positionConfig,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        primaryDatasource() {
            return this.$store.getters.highPriDataSources;
        },
    },
    methods: {
        sure() {
            const { regionList, regionIds, type } = this.$refs.positionConfigRef.getRegionData();

            if (!regionIds || !regionIds.length) {
                return this.$message.warning('区域不能为空！');
            }

            const ck2 = [];
            const ck3 = [];
            let areaIds = [];
            const promises = [];
            const isArea = type === 1;
            let regionIdList = regionIds;
            if (isArea) {
                regionIdList = regionList.map((item) => item.province);
                areaIds = regionList.map((item) => item.regionId);
            }
            regionIdList.forEach((code, inx) => {
                const prefix = code.substring(0, 2);
                const tarItem =
                    this.primaryDatasource.find(
                        (item) => String(item.provinceCode) === `${prefix}0000`
                    ) || {};
                if (tarItem.primaryDatasource === 2) {
                    ck2.push(isArea ? areaIds[inx] : code);
                } else if (tarItem.primaryDatasource === 3) {
                    ck3.push(isArea ? areaIds[inx] : code);
                }
            });
            if (ck2.length) {
                promises.push(this.createRegion(ck2, type, 2));
            }
            if (ck3.length) {
                promises.push(this.createRegion(ck3, type, 3));
            }

            Promise.all(promises)
                .then((res) => {
                    const item = (res && res[0]) || {};
                    if (item.serviceFlag === 'TRUE') {
                        this.$message.success(item.returnMsg);
                        this.handleClose();
                        this.$emit('updateTable');
                    } else {
                        this.$message.error(item.returnMsg || '新增失败！');
                    }
                })
                .catch((error) => {
                    console.error('区域新增失败:', error);
                    this.$message.error('区域新增失败，请稍后重试！');
                });
        },
        createRegion(regionIds, type, primaryDatasource) {
            return this.highGetPost(
                'monitorApi',
                'insert',
                {
                    regionCodeList: regionIds,
                    regionType: type, // 区域类型 1：自定义区域，2：省，3：市，4：区县
                    primaryDatasource,
                    nowDatasource: primaryDatasource,
                    standbyDatasource: 1,
                },
                '区域编码新增'
            );
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
    },
};
</script>

<style lang="less" scoped>
.add-region {
    &__main {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
    &__footer {
        text-align: center;
    }
    /deep/ .el-dialog {
        display: flex;
        flex-direction: column;
        border-radius: 10px;
    }
    /deep/ .el-dialog__header {
        position: relative;
        padding: 20px;
        border-bottom: 1px solid #ccc;
        .el-dialog__title {
            font-size: 18px;
            font-weight: bold;
        }
        .el-dialog__headerbtn {
            top: 19px;
            font-size: 18px;
        }
    }
    /deep/ .el-dialog__body {
        padding: 20px;
        flex: 1;
        height: 0;
    }
    /deep/ .el-dialog__footer {
        padding: 20px;
        border-top: 1px solid #ccc;
    }
}
</style>
