<template>
    <div class="selected-tags" :style="boxH">
        <div class="selected-tags__head">
            已选{{ title || mapAreaNames[panelLevels - 1] }}{{ getTagsNumber(tags) }}
            <span v-show="tags.length" class="clear" @click="clear">清空</span>
        </div>
        <div class="selected-tags__body">
            <el-tag
                v-for="tag in tags"
                :key="tag.value"
                type="info"
                size="small"
                closable
                @close="handleClose(tag)"
                >{{ tag.label }}</el-tag
            >
        </div>
    </div>
</template>

<script>
export default {
    name: 'selected-tags',
    props: {
        tags: {
            type: Array,
            default: () => [],
        },
        title: {
            type: String,
            default: '',
        },
        panelLevels: {
            type: Number,
            default: 3,
        },
        total: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            mapAreaNames: ['省份', '地市', '区县'],
        };
    },
    computed: {
        winH() {
            return window.screen.height;
        },
        boxH() {
            let style = {};
            style.height = '500px';
            return style;
            // if (window.innerHeight >= 963) {
            //   style.height = '420px';
            // } else if (window.innerHeight >= 931) {
            //   style.height = '390px';
            // }
            // return style;
        },
    },
    methods: {
        handleClose(tag) {
            this.$emit('delTags', tag);
        },
        getTagsNumber(tags) {
            if (tags.length === 1 && tags[0].value.includes('-all')) {
                return this.total ? `（${this.total}个）` : '';
            }
            return tags.length ? `（${tags.length}个）` : '';
        },
        clear() {
            this.$emit('delTags', [], true);
        },
    },
};
</script>
<style lang="less" scoped>
.selected-tags {
    display: inline-block;
    width: 100%;
    height: 280px;
    background-color: #ffffff;
    border-radius: 2px;
    border: 1px solid #d9d9d9;
    &__head {
        height: 32px;
        background-color: #f6f7fa;
        padding-left: 14px;
        position: relative;
        .clear {
            position: absolute;
            right: 10px;
            line-height: 32px;
            color: #409eff;
            cursor: pointer;
        }
    }
    &__body {
        height: calc(100% - 32px);
        overflow: auto;
        .el-tag {
            margin: 6px;
            height: auto;
            word-break: break-word;
            white-space: pre-line;
        }
        &::-webkit-scrollbar {
            width: 10px;
            height: 1px;
        }

        &::-webkit-scrollbar-thumb {
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            background: #f6f7fa;
        }

        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            background: transparent;
        }
    }
}
</style>
