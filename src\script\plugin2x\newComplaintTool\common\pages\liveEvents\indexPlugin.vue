<template>
    <div class="live-events">
        <div class="top">
            <headTitle :title="title" @back="back" />
            <baseInfo :infoItems="infoItems" />
            <div class="search-form">
                <searchBar :form="form" :fields="fields">
                    <el-checkbox class="base-station" v-model="isShowBaseStation"
                        >GIS显示基站</el-checkbox
                    >
                    <el-button type="primary" @click="search">查询</el-button>
                </searchBar>
            </div>
        </div>
        <div class="bottom">
            <gisMap @loaded="loaded" />
            <orderDetail
                ref="orderDetail"
                :getBaseStations="getBaseStations"
                :tableData="tableData"
                :total="total"
                :overviewList="overviewList"
                :permissionTabs="permissionTabs"
                @changeTab="changeTab"
            />
            <customLegend v-if="isShowBaseStation" />
        </div>
    </div>
</template>

<script>
import headTitle from '../../components/headTitle.vue';
import baseInfo from '../../components/baseInfo.vue';
import searchBar from '_com/searchForm/index.vue';
import gisMap from '_com/gisMap/index.vue';
import { createLine, createPoint, createPlane } from '../../components/layers';
import { fields } from './constants.js';
import blueImg from '@/img/gis/innerBaseStation.png';
import yellowImg from '@/img/gis/outBaseStation.png';
import { enterLeaveOverviewList, residentOverviewList } from './constants.js';
export default {
    name: 'liveEvents',
    components: {
        headTitle,
        searchBar,
        baseInfo,
        gisMap,
        orderDetail: () => import('./components/details.vue'),
        customLegend: () => import('./components/legend.vue')
    },
    data() {
        return {
            title: '用户实时事件查询',
            infoItems: [
                {
                    label: '投诉工单ID',
                    prop: 'id',
                    value: '-'
                },
                {
                    label: '用户号码',
                    prop: 'queryMsisdn',
                    value: '-'
                },
                {
                    label: '申请任务',
                    prop: 'taskName',
                    value: '-'
                },
                {
                    label: '申请任务ID',
                    prop: 'taskId',
                    value: '-'
                }
            ],
            form: {
                regionId: '',
                dataStime: '',
                dataEtime: '',
                stayDuration: null
            },
            tableData: [],
            total: 0,
            isShowBaseStation: false,
            showOrderDetail: true,
            areaOpts: [],
            trajectoryList: [],
            overviewList: [],
            notEnteredList: [],
            enteredListMap: {},
            stayListMap: {},
            trackPoints: [],
            inAreaPoints: [],
            permissionTabs: []
        };
    },
    computed: {
        fields() {
            return fields(this.areaOpts);
        },
        queryData() {
            return this.$route.query;
        }
    },
    watch: {
        isShowBaseStation: {
            handler(val) {
                if (val) {
                    this.drawTrackStations();
                } else {
                    this.Point.remove();
                    this.Line.remove();
                    this.InPoint.remove();
                }
            }
        }
    },
    created() {
        this.initData();
    },
    methods: {
        async loaded(g) {
            this.Line = createLine(g);
            this.Point = createPoint(g, {
                img: blueImg
            });
            this.InPoint = createPoint(g, {
                img: yellowImg
            });
            this.Plane = createPlane(g);
            //
            await this.getAreaOptions();

            this.search();
        },
        initData() {
            // baseInfo
            for (const item of this.infoItems) {
                const prop = item.prop;
                if (this.queryData[prop]) {
                    item.value = this.queryData[prop];
                }
            }
            // date
            const { dataStime, dataEtime } = this.queryData;
            Object.assign(this.form, { dataStime, dataEtime });
        },
        search() {
            this.$refs.orderDetail.pagination.curPage = 1;
            this.getBaseStations();
            this.getInAreaStations();
        },
        getBaseStations(pagination) {
            const { queryMsisdn, taskId } = this.queryData;
            const { pageNum = 1, pageSize = 10 } = pagination || {};
            const { regionId, dataStime, dataEtime, stayDuration } = this.form;
            const params = {
                iopTaskId: taskId,
                msisdn: queryMsisdn,
                regionId,
                startTime: dataStime,
                endTime: dataEtime,
                stayTime: stayDuration,
                status: null,
                pageNum,
                pageSize
            };
            this.highGetPost('newComplaintToolApi', 'getBaseStations', params, '获取基站数据').then(
                ({ data }) => {
                    console.log(data);
                    const {
                        trajectoryList = [],
                        notEnteredList = [],
                        enteredListMap = {},
                        stayListMap = {},
                        totalCount = 0
                    } = data || [];

                    this.trackPoints = trajectoryList.map((item) => ({
                        latLng: { lat: item.cgiLat, lng: item.cgiLng },
                        data: item
                    }));

                    this.trajectoryList = trajectoryList;
                    this.notEnteredList = notEnteredList;
                    this.enteredListMap = enteredListMap;
                    this.stayListMap = stayListMap;

                    this.tableData = trajectoryList;
                    this.total = totalCount;
                    if (this.isShowBaseStation) {
                        this.drawTrackStations(Boolean(pagination));
                    }
                }
            );
        },
        getInAreaStations() {
            const { regionId } = this.form;
            const { queryMsisdn } = this.queryData;
            const params = {
                regionId,
                msisdn: queryMsisdn,
                pageNum: 1,
                pageSize: 100
            };
            this.highGetPost(
                'newComplaintToolApi',
                'getInAreaStations',
                params,
                '获取区域内基站数据'
            ).then(({ data }) => {
                const { cellList = [] } = data || {};
                this.inAreaPoints = cellList.map((item) => ({
                    latLng: { lat: item.cgiLat, lng: item.cgiLng },
                    data: item
                }));
            });
        },
        changeTab(tab) {
            if (tab === 'totalTrack') {
                this.tableData = this.trajectoryList;
                this.overviewList = [];
            } else if (tab === 'enterLeaveList') {
                const { enterCnt = 0, outCnt = 0, enteredList = [] } = this.enteredListMap;
                this.tableData = enteredList;
                this.overviewList = enterLeaveOverviewList(enterCnt, outCnt);
            } else if (tab === 'notInList') {
                this.tableData = this.notEnteredList;
                this.overviewList = [];
            } else if (tab === 'residentList') {
                const { stayTimeCount = 0, stayList = [] } = this.stayListMap;
                this.tableData = stayList;
                this.overviewList = residentOverviewList(stayTimeCount);
            }
        },
        drawTrackStations(isOnlyShowTrackPoint = false) {
            this.Point.draw(this.trackPoints);
            this.Line.draw([
                {
                    points: this.trackPoints.map((item) => item.latLng),
                    color: 0xf74041
                }
            ]);

            if (!isOnlyShowTrackPoint) {
                this.InPoint.draw(this.inAreaPoints);
            }
        },
        getAreaOptions() {
            this.highGetPost(
                'newComplaintToolApi',
                'getAreaOptions', // 请确认实际接口方法名
                {
                    taskId: this.queryData.taskId
                },
                '区域选择下拉项查询'
            ).then(({ data }) => {
                const { regionIds = [], events = [] } = data || {};
                this.areaOpts = regionIds.map((item) => ({
                    label: item.regionName,
                    value: item.regionId
                }));
                //
                events.forEach((item) => {
                    const { eventType, eventCondition } = item;
                    if ([1, 2].includes(eventType)) {
                        this.permissionTabs.push('enterLeaveList');
                    }
                    if (eventType === 3) {
                        this.permissionTabs.push('residentList');
                    }
                    if (eventCondition && eventCondition.minTime) {
                        this.form.stayDuration = eventCondition.minTime;
                    }
                });
            });
        },
        back() {
            this.$router.back();
        }
    }
};
</script>

<style lang="less" scoped>
.live-events {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .top {
        .search-form {
            padding: 16px 10px 4px;
            background-color: #fff;
            box-shadow: 0px 4px 8px 0px rgba(0, 47, 136, 0.2);
            .base-station {
                margin: 0 20px;
            }
        }
    }
    .bottom {
        position: relative;
        height: calc(100% - 176px);
        background-color: #fff;
    }
}
</style>
