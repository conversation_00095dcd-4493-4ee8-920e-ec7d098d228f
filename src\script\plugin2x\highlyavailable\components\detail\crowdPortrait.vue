<!--
 * @Date: 2025-02-11 11:34:01
 * @LastEditors: liurong <EMAIL>
 * @LastEditTime: 2025-02-27 13:56:31
 * @FilePath: \mtex-static-highlyavailable\src\script\plugin2x\highlyavailable\components\detail\crowdPortrait.vue
-->
<template>
    <div class="crowdPortrait-wrapper">
        <searchBar :fields="fields" :form="form" :isLayout="false">
            <el-button
                class="highlyavailable-primary-btn"
                type="primary"
                size="small"
                @click="search()"
                >查询</el-button
            >
        </searchBar>
        <dataTable
            class="dataTable"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :total="total"
            stripe
            border
            :updateTable="getTableData"
        >
        </dataTable>
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import { fields, tableColumns } from '@/script/constant/crowdPortrait.js';
import { detailMixin } from './mixin';

export default {
    name: 'crowdPortrait',
    mixins: [detailMixin],
    components: {
        searchBar,
        dataTable,
    },
    data() {
        return {
            form: {
                time: [],
                displayIndicators: '',
            },
            fields,
            columns: tableColumns,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15,
            },
            total: 0,
        };
    },
    created() {
        this.initTime();
        this.search();
    },
    methods: {
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            const { time } = this.form;
            const [startTime, endTime] = time;
            const { regionType, regionId, timeSize } = this.queryRoute;
            this.highGetPost(
                'monitorApi',
                'getCrowdHot',
                {
                    regionType,
                    timeSize,
                    regionId,
                    startTime,
                    endTime,
                    pageNum: curPage,
                    pageSize,
                    modelList: {
                        modelId: '3',
                        modelParam: {
                            sex: ['男', '女'],
                            age: ['18-24', '25-34', '35-44', '45-54', '55岁以上'],
                        },
                    },
                },
                '高可用监控查询'
            ).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    const data = res.data;
                    const { primaryResult, standbyResult, totalNum } = data;

                    // 获取所有不重复的时间点
                    const timeSpans = [...new Set(primaryResult.age.map((item) => item.timeSpan))];

                    // 处理数据
                    this.tableData = timeSpans.map((timeSpan) => {
                        // 获取主库该时间点的各年龄段数据
                        const mainAgeData = primaryResult.age.filter(
                            (item) => item.timeSpan === timeSpan
                        );
                        // 获取备用库该时间点的各年龄段数据
                        const backupAgeData = standbyResult.age.filter(
                            (item) => item.timeSpan === timeSpan
                        );

                        // 获取主库该时间点的性别数据
                        const mainSexData = primaryResult.sex.filter(
                            (item) => item.timeSpan === timeSpan
                        );
                        // 获取备用库该时间点的性别数据
                        const backupSexData = standbyResult.sex.filter(
                            (item) => item.timeSpan === timeSpan
                        );

                        // 辅助函数：安全获取 nub 值
                        const getNubValue = (arr, name) => {
                            const item = arr.find((item) => item.name === name);
                            return item ? item.nub : 0;
                        };

                        // 构建返回数据
                        return {
                            time: timeSpan,
                            // 18-24岁
                            age1Main: getNubValue(mainAgeData, '18-24'),
                            age1Backup: getNubValue(backupAgeData, '18-24'),
                            // 25-34岁
                            age2Main: getNubValue(mainAgeData, '25-34'),
                            age2Backup: getNubValue(backupAgeData, '25-34'),
                            // 35-44岁
                            age3Main: getNubValue(mainAgeData, '35-44'),
                            age3Backup: getNubValue(backupAgeData, '35-44'),
                            // 45-54岁
                            age4Main: getNubValue(mainAgeData, '45-54'),
                            age4Backup: getNubValue(backupAgeData, '45-54'),
                            // 55岁以上
                            age5Main: getNubValue(mainAgeData, '55以上'),
                            age5Backup: getNubValue(backupAgeData, '55以上'),
                            // 性别-男
                            manMain: getNubValue(mainSexData, '男'),
                            manBackup: getNubValue(backupSexData, '男'),
                            // 性别-女
                            womanMain: getNubValue(mainSexData, '女'),
                            womanBackup: getNubValue(backupSexData, '女'),
                        };
                    });

                    this.total = totalNum;
                } else {
                    this.$message.warning(res.returnMsg);
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
.crowdPortrait-wrapper {
    width: 100%;
    height: 100%;
    padding: 1.33rem;
    display: flex;
    flex-direction: column;
}
.dataTable {
    width: 100%;
    flex: 1;
    height: 0;
}
</style>
