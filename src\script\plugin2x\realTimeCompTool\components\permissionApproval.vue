<template>
    <div class="permissionApproval">
        <searchBar :fields="fields" :form="form">
            <el-button
                class="highlyavailable-primary-btn"
                type="primary"
                size="small"
                @click="search()"
                >查询</el-button
            >
        </searchBar>
        <dataTable
            class="dataTable"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :total="total"
            stripe
            :updateTable="getTableData"
        >
            <template #isValid="{ row }">
                <span>{{ isValid[row.isValid] }}</span>
            </template>
            <template #status="{ row }">
                <span>{{ status[row.status] }}</span>
            </template>
            <template #isTransfer="{ row }">
                <span>{{ isValid[row.isTransfer] }}</span>
            </template>
            <template #operate="{ row }">
                <div class="table-btn">
                    <el-button
                        class="table-btn-item"
                        type="text"
                        size="mini"
                        @click="examineAndApprove(row)"
                    >
                        审批
                    </el-button>
                </div>
            </template>
        </dataTable>
        <!-- 审批 -->
        <myDialog title="审批" :visible.sync="dialogVisible" :btnList="dialogConfigurationBtn()">
            <searchBar :fields="dialogFields" :form="dialogForm"> </searchBar>
        </myDialog>
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import myDialog from '_com/dialog/index.vue';
import { fields, tableColumns, dialogFields } from '../constatnt/permissionApproval.js';
export default {
    name: 'permissionApproval',
    components: {
        searchBar,
        dataTable,
        myDialog
    },
    data() {
        return {
            fields: fields(),
            form: {
                queryMsisdn: '',
                dataStime: '',
                dataEtime: '',
                regionId: '',
                status: 0
            },
            columns: tableColumns,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            total: 0,
            dialogType: 'add', // add、edit
            dialogVisible: false,
            dialogForm: {
                status: 1,
                approvalOpn: '',
                isTransfer: 0,
                transferUser: ''
            },
            dialogFields,
            isValid: {
                1: '是',
                0: '否'
            },
            status: {
                0: '申请中',
                1: '同意',
                2: '驳回',
                3: '禁止'
            }
        };
    },
    created() {
        this.getWhitePhone();
    },
    mounted() {
        this.search();
    },
    methods: {
        getWhitePhone() {
            this.highGetPost('realTimeToolSeviceApi', 'getWhitePhone', {}, '获取白名单号码').then(
                ({ data }) => {
                    this.fields = fields({
                        msisdn: data
                    });
                }
            );
        },
        dialogConfigurationBtn() {
            return [
                {
                    name: '确定',
                    class: 'blue-btn',
                    listeners: {
                        click: () => {
                            this.approvePermission(this.dialogForm);
                        }
                    }
                }
            ];
        },
        examineAndApprove(row) {
            this.curRow = row;
            this.dialogVisible = true;
        },
        approvePermission(form) {
            this.highGetPost(
                'realTimeToolSeviceApi',
                'approvePermission',
                {
                    ...form,
                    id: this.curRow.id
                },
                '审批'
            ).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg);
                    this.search();
                } else {
                    this.$message.error(res.returnMsg);
                }
                this.dialogVisible = false;
            });
        },
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            this.highGetPost(
                'realTimeToolSeviceApi',
                'getPermissionList',
                {
                    queryType: 2,
                    ...this.form,
                    pageNum: curPage,
                    pageSize
                },
                '获取用户数据查询权限列表'
            ).then(({ data }) => {
                this.tableData = data.list;
                this.total = data.totalSize;
            });
        }
    }
};
</script>

<style lang="less" scoped>
.permissionApproval {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 1.33rem;
}
.dataTable {
    width: 100%;
    flex: 1;
}
.table-btn {
    display: flex;

    &-item {
        position: relative;
        font-size: 14px;

        &.redText {
            color: #ff4d4f;
        }

        &::after {
            position: absolute;
            right: -13px;
            top: 8px;
            width: 1px;
            background: #ebeef5;
            height: 11px;
            content: '';
        }

        &:last-child {
            &::after {
                position: absolute;
                right: -13px;
                top: 8px;
                width: 1px;
                background: #ebeef5;
                height: 0px;
                content: '';
            }
        }
    }
}
</style>
