/*
 * @Date: 2025-02-27 08:59:50
 * @LastEditors: liurong <EMAIL>
 * @LastEditTime: 2025-02-27 09:01:25
 * @FilePath: \mtex-static-highlyavailable\src\script\plugin2x\highlyavailable\components\detail\mixin.js
 */
/**
 * @description 高可用详情页面的通用mixin
 */
import dayjs from 'dayjs';
export const detailMixin = {
    computed: {
        queryRoute () {
            return this.$route.query;
        },
    },
    methods: {
        initTime () {
         /*    const { startTime, endTime } = this.queryRoute;
            if (startTime && endTime) {
                this.form.time = [startTime, endTime];
            } else {
            } */
            this.form.time = [dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:00'), dayjs().format('YYYY-MM-DD HH:mm:00')];
        },
    }
};
