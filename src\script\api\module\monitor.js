/*
 * @Date: 2025-02-11 11:34:01
 * @LastEditors: liurong <EMAIL>
 * @LastEditTime: 2025-02-25 19:53:07
 * @FilePath: \mtex-static-highlyavailable\src\script\api\module\monitor.js
 */
import norMalConfig from '../normal-config';
const _url = '/default-server';
const monitor = {
    monitorApi: {
        // 高可用监控查询
        selectAdditional(params) {
            return norMalConfig(
                `${_url}/task-service/ckSwitchOverImportant/selectAdditional`,
                params
            );
        },
        // 高可用监控新增
        insert(params) {
            return norMalConfig(`${_url}/task-service/ckSwitchOverImportant/insert`, params);
        },
        // 高可用监控更新
        update(params) {
            return norMalConfig(`${_url}/task-service/ckSwitchOverImportant/update`, params);
        },
        // 高可用监控删除
        delete(params) {
            return norMalConfig(`${_url}/task-service/ckSwitchOverImportant/delete`, params);
        },
        warehouseInfo(params) {
            return norMalConfig(`${_url}/task-service/ckSwitchOverLog/pageClusterInfo`, params);
        },
        getHistory(params) {
            return norMalConfig(`${_url}/task-service/ckSwitchOverImportant/select`, params);
        },
        getCrowdHot(params) {
            return norMalConfig(`${_url}/task-service/ckSwitchOverCrowd/fixRegion/crowd`, params);
        },
        getCrowHotData(params) {
            return norMalConfig(`${_url}/task-service/ckSwitchOverCrowd/fixRegion/crowd/heatRegion`, params);
        },
        geAreaOutline(params) {
            return norMalConfig(`${_url}/region-service/regionMng/searchRegion`, params);
        },
        ganaryTestInfo(params) {
            return norMalConfig(`${_url}/task-service/gray/page`, params);
        },
        grayAdd(params) {
            return norMalConfig(`${_url}/task-service/gray/add`, params);
        },
        grayUpdate(params) {
            return norMalConfig(`${_url}/task-service/gray/update`, params);
        },
        grayDelete(params) {
            return norMalConfig(`${_url}/task-service/gray/delete`, params);
        },
        ckChangeInfo(params) {
            return norMalConfig(`${_url}/task-service/ckSwitchOverSet/query`, params);
        },
        ckChangeAdd(params) {
            return norMalConfig(`${_url}/task-service/ckSwitchOverSet/insert`, params);
        },
        ckChangeUpdate(params) {
            return norMalConfig(`${_url}/task-service/ckSwitchOverSet/update`, params);
        },
        ckChangeDelete(params) {
            return norMalConfig(`${_url}/task-service/ckSwitchOverSet/delete`, params);
        },
        recovery(params) {
            return norMalConfig(
                `${_url}/task-service/ckSwitchCountStatistics/switchCountStatistics`,
                params
            );
        }
    }
};
export default monitor;
