const fields = (Opts = { msisdn: [] }) => {
    return [
        {
            prop: 'queryMsisdn',
            label: '申请查询号码：',
            labelWidth: '70',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: Opts.msisdn.map((i) => ({ label: i.msisdn, value: i.msisdn }))
            },
            span: 4
        },
        {
            prop: 'dataStime',
            label: '数据开始时间：',
            labelWidth: '70',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss'
            },
            span: 5
        },
        {
            prop: 'dataEtime',
            label: '数据结束时间：',
            labelWidth: '70',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss'
            },
            span: 5
        },
        {
            prop: 'regionId',
            label: '申请区域ID：',
            labelWidth: '70',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true
            },
            span: 4
        },
        {
            prop: 'status',
            label: '状态：',
            labelWidth: '70',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '申请中', value: 0 },
                    { label: '同意', value: 1 },
                    { label: '驳回', value: 2 },
                    { label: '禁止', value: 3 }
                ]
            },
            span: 4
        },
        {
            span: 2
        }
    ];
};
const tableColumns = [
    {
        prop: 'id',
        label: 'ID'
    },
    {
        prop: 'applicant',
        label: '申请人'
    },
    {
        prop: 'queryMsisdn',
        label: '申请查询号码'
    },
    {
        prop: 'dataStime',
        label: '数据开始时间'
    },
    {
        prop: 'dataEtime',
        label: '数据结束时间'
    },
    {
        prop: 'regionId',
        label: '申请区域'
    },
    {
        prop: 'applicationReason',
        label: '申请原因'
    },
    {
        prop: 'status',
        label: '状态'
    },
    {
        prop: 'approver',
        label: '审批人'
    },
    {
        prop: 'approvalOpn',
        label: '审批意见'
    },
    {
        prop: 'isTransfer',
        label: '是否转派'
    },
    {
        prop: 'transferUser',
        label: '转派人'
    },
    {
        prop: 'isValid',
        label: '是否有效'
    },
    {
        prop: 'createTime',
        label: '创建时间'
    },
    {
        prop: 'operate',
        label: '操作'
    }
];

const dialogFields = [
    {
        prop: 'status',
        label: '申请结果：',
        labelWidth: '108px',
        element: 'el-select',
        bind: {
            placeholder: '请选择',
            clearable: true
        },
        slot: {
            element: 'el-option',
            enums: [
                { label: '同意', value: 1 },
                { label: '驳回', value: 2 },
                { label: '禁止', value: 3 }
            ]
        },
        span: 24
    },
    {
        prop: 'approvalOpn',
        label: '审批意见：',
        labelWidth: '108px',
        element: 'el-input',
        bind: {
            placeholder: '请输入',
            clearable: true,
            type: 'textarea',
            autosize: { minRows: 2, maxRows: 4 }
        },
        span: 24
    },
    {
        prop: 'isTransfer',
        label: '是否转派：',
        labelWidth: '108px',
        element: 'el-select',
        bind: {
            placeholder: '请选择',
            clearable: true
        },
        slot: {
            element: 'el-option',
            enums: [
                { label: '是', value: 1 },
                { label: '否', value: 0 }
            ]
        },
        span: 24
    },
    {
        prop: 'transferUser',
        label: '转派人：',
        labelWidth: '108px',
        element: 'el-input',
        bind: {
            placeholder: '请输入',
            clearable: true
        },
        span: 24
    }
];

export { fields, tableColumns, dialogFields };
