<template>
    <div class="highlyavailable">
        <div class="highlyavailable-title">
            <div class="text">{{ title }}</div>
            <el-button
                v-if="hasCreate.includes(tabActive)"
                class="highlyavailable-primary-btn"
                type="primary"
                icon="el-icon-plus"
                @click="addConfig"
                >新增</el-button
            >
        </div>
        <tab :tabActive.sync="tabActive" :tabList="tabList" :tabFit="true"></tab>
        <div class="highlyavailable-content">
            <component class="box" ref="currentComp" :is="currentComp" />
        </div>
    </div>
</template>
<script>
import tab from '_com/tabs/index.vue';
import { currencyComp } from './components/index.js';
export default {
    components: {
        tab,
        ...currencyComp
    },
    data() {
        return {
            tabActive: '1',
            hasCreate: ['2', '3', '4'],
            tabList: [
                { name: '投诉用户指定时间轨迹呈现', key: '1', comp: 'timeTrajectory' },
                { name: '投诉应用权限', key: '2', comp: 'appPermissions' },
                { name: '投诉应用黑白名单', key: '3', comp: 'blackAndWhiteList' },
                { name: '用户数据查询权限申请', key: '4', comp: 'permissionApplication' },
                { name: '用户数据查询权限审批', key: '5', comp: 'permissionApproval' },
                { name: '用户数据查询权限统计', key: '6', comp: 'permissionStatistics' },
                { name: '用户电子围栏轨迹稽核', key: '7', comp: 'trajectoryAudit' }
            ]
        };
    },
    computed: {
        title() {
            return this.tabList.find((item) => item.key == this.tabActive).name;
        },
        currentComp() {
            return this.tabList.find((item) => item.key == this.tabActive).comp;
        }
    },
    mounted() {},
    methods: {
        addConfig() {
            this.$refs.currentComp.handleAdd();
        }
    }
};
</script>
<style lang="less" scoped>
.highlyavailable {
    width: 100%;
    height: 100%;
    background: url('../../../img/background.png') no-repeat center center / 100% 100%;
    padding: 1.94rem 1.78rem 1.33rem 1.78rem;
    display: flex;
    flex-direction: column;
    &-title {
        display: flex;
        justify-content: space-between;
        .text {
            font-size: @font-size-max;
            color: @black-title;
            font-weight: 500;
            line-height: 40px;
        }
    }
    &-content {
        flex: 1;
        width: 100%;
        height: 0;
        background: #ffffff;
        box-shadow: 0px 0px 0.89rem 0px rgba(65, 67, 68, 0.15);
        border-radius: 0px 0.44rem 0.44rem 0.44rem;
    }
}
</style>
<style lang="less">
html {
    font-size: calc(100vw * 18 / 1920);
}
</style>
