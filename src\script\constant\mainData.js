/*
 * @Date: 2025-02-11 11:34:01
 * @LastEditors: liurong <EMAIL>
 * @LastEditTime: 2025-02-27 10:04:23
 * @FilePath: \mtex-static-highlyavailable\src\script\constant\mainData.js
 */
const fields = [
    // {
    //     prop: 'displayIndicators',
    //     label: '展示指标：',
    //     labelWidth: '70',
    //     element: 'el-select',
    //     bind: {
    //         placeholder: '请选择',
    //         clearable: true,
    //     },
    //     slot: {
    //         element: 'el-option',
    //         enums: [],
    //     },
    //     span: 3,
    // },
    {
        prop: 'time',
        label: '数据时间：',
        labelWidth: '40',
        element: 'el-date-picker',
        bind: {
            type: 'datetimerange',
            'range-separator': '-',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            format: 'yyyy-MM-dd HH:mm:00',
            'value-format': 'yyyy-MM-dd HH:mm:00',
        },
        span: 7,
    },
    {
        span: 2
    }
];
const tableColumns = [
    {
        prop: 'time',
        label: '数据时间',
    },
    {
        label: '总人数',
        child: [
            {
                prop: 'totalPeopleMain',
                label: '主库群',
            },
            {
                prop: 'totalPeopleBackup',
                label: '备用库群',
            }
        ]
    },
    {
        label: '流入',
        child: [
            {
                prop: 'flowInMain',
                label: '主库群',
            },
            {
                prop: 'flowInBackup',
                label: '备用库群',
            }
        ]
    },
    {
        label: '流出',
        child: [
            {
                prop: 'flowOutMain',
                label: '主库群',
            },
            {
                prop: 'flowOutBackup',
                label: '备用库群',
            }
        ]
    },
    {
        label: '累计到访',
        child: [
            {
                prop: 'visitsMain',
                label: '主库群',
            },
            {
                prop: 'visitsBackup',
                label: '备用库群',
            }
        ]
    },
    {
        label: '常驻分布',
        child: [
            {
                prop: 'residentMain',
                label: '主库群',
            },
            {
                prop: 'residentBackup',
                label: '备用库群',
            }
        ]
    },
];


export {
    fields,
    tableColumns,
};