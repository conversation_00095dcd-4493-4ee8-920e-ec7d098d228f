<template>
    <div class="historyData-wrapper">
        <searchBar :fields="fields" :form="form" :isLayout="false">
            <el-button
                class="highlyavailable-primary-btn"
                type="primary"
                size="small"
                @click="search()"
                >查询</el-button
            >
        </searchBar>
        <dataTable
            class="dataTable"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :total="total"
            stripe
            border
            :updateTable="getTableData"
        >
            <template #nowDatasource="{ row }">
                <span>ck{{ row.nowDatasource }}</span>
            </template>
        </dataTable>
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import { fields, tableColumns } from '@/script/constant/historyData.js';
export default {
    name: 'historyData',
    components: {
        searchBar,
        dataTable,
    },
    data() {
        return {
            form: {
                time: [],
                nowDatasource: '',
            },
            fields,
            columns: tableColumns,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 20,
            },
            total: 0,
        };
    },
    computed: {
        queryRoute() {
            return this.$route.query;
        },
    },
    mounted() {
        this.search();
    },
    methods: {
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            const { regionCode, regionType } = this.queryRoute;
            const { time, nowDatasource } = this.form;
            const [startTimeFrom, endTimeTo] = time || [];
            this.highGetPost(
                'monitorApi',
                'getHistory',
                {
                    regionCodeList: [regionCode],
                    regionType, // 区域类型 1：自定义区域，2：省，3：市，4：区县
                    nowDatasource,
                    startTimeFrom,
                    endTimeTo,
                    pageNum: curPage,
                    pageSize,
                },
                '历史查询'
            ).then(({ data }) => {
                this.tableData = data.list;
                this.total = data.pageTotal;
            });
        },
    },
};
</script>

<style lang="less" scoped>
.historyData-wrapper {
    width: 100%;
    height: 100%;
    padding: 1.33rem;
    display: flex;
    flex-direction: column;
}
.dataTable {
    width: 100%;
    flex: 1;
    height: 0;
}
</style>
