<template>
    <transition name="fade">
        <div class="details" :style="{ width: isExpand ? '500px' : 0 }">
            <div class="title">
                <span class="text">详单</span>
            </div>
            <div class="content">
                <dataTable
                    :columns="columns"
                    :data="tableData"
                    :pagination="pagination"
                    :total="total"
                    :updateTable="getTableData"
                    layout="total, prev, pager, next"
                    :row-class-name="activeRowClassName"
                    size="mini"
                    isHideUpLine
                    @cell-click="handleCellClick"
                >
                </dataTable>
            </div>
            <img class="toggle-icon" :src="toggleIcon" alt="" @click="isExpand = !isExpand" />
        </div>
    </transition>
</template>

<script>
import dataTable from '_com/tables/dataTableLast.vue';
import arrowLeft from '@/img/icon/left.png';
import arrowRight from '@/img/icon/right.png';
import { columns } from '../constants.js';
export default {
    name: 'details',
    components: {
        dataTable
    },
    props: {
        defTableData: {
            type: Array,
            default: () => []
        },
        defTotal: {
            type: Number,
            default: 0
        },
        form: {
            type: Object,
            default: () => ({})
        },
        curRow: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            tableData: [],
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 10
            },
            isExpand: true,
            curClickRow: {}
        };
    },
    computed: {
        columns() {
            return columns;
        },
        toggleIcon() {
            return this.isExpand ? arrowRight : arrowLeft;
        },
        listenDefTable() {
            return {
                defTableData: this.defTableData,
                defTotal: this.defTotal
            };
        },
        queryData() {
            return this.$route.query;
        }
    },
    watch: {
        listenDefTable: {
            handler() {
                this.tableData = this.defTableData;
                this.total = this.defTotal;
            }
        }
    },
    methods: {
        getTableData(pagination = {}) {
            const { queryMsisdn } = this.queryData;
            const { curPage = 1, pageSize = 10 } = pagination;
            const { dataStime, dataEtime, stayDuration } = this.form;
            const params = {
                msisdn: queryMsisdn,
                startTime: dataStime,
                endTime: dataEtime,
                stayTime: stayDuration,
                status: 1,
                pageNum: curPage,
                pageSize: pageSize
            };
            this.highGetPost('newComplaintToolApi', 'getBaseStations', params, '获取基站数据').then(
                ({ data }) => {
                    console.log(data);
                    const { trajectoryList = [], totalCount } = data || [];
                    this.tableData = trajectoryList;
                    this.total = totalCount;
                }
            );
        },
        activeRowClassName({ row }) {
            if (row === this.curClickRow) {
                return 'success-row';
            }
            return '';
        },
        handleCellClick(row) {
            this.curClickRow = row;
            this.$emit('rowClick', row);
        }
    }
};
</script>

<style lang="less" scoped>
.details {
    display: flex;
    flex-direction: column;
    position: absolute;
    right: 16px;
    top: 16px;
    width: 480px;
    height: calc(100% - 32px);
    z-index: 1000;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    background-color: #fff;
    .title {
        position: relative;
        padding-left: 16px;
        line-height: 40px;
        font-weight: bold;
        border-bottom: 1px solid #f0f0f0;
        .text {
            display: flex;
            align-items: center;
            height: 32px;
            font-size: 14px;
            &::before {
                content: '';
                display: inline-block;
                margin-right: 6px;
                width: 3px;
                height: 12px;
                background: #0091ff;
            }
        }
    }
    .content {
        padding: 16px;
        flex: 1;
        height: 0;
    }
    .toggle-icon {
        position: absolute;
        left: -16px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
    }
}

/* 添加过渡效果 */
.fade-enter-active,
.fade-leave-active {
    transition: all 0.5s;
}
.fade-enter,
.fade-leave-to {
    opacity: 0;
    width: 0 !important;
}
</style>
