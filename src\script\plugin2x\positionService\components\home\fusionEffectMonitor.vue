<template>
	<div class="fusionEffectMonitor">
		<searchBar
			:fields="fields"
			:form="form"
		>
			<el-button
				class="highlyavailable-primary-btn"
				type="primary"
				size="small"
				@click="search()"
			>查询</el-button>
		</searchBar>
		<div class="chart-list">
			<div class="chart-grid">
				<chart-card
					v-for="(chart, index) in chartList"
					:key="index"
					:title="chart.title"
					:chart-data="chart.data"
					:chart-color="chart.color"
					:y-axis-type="chart.yAxisType"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import ChartCard from './ChartCard.vue';
import { getAreasToCity, timeFormat } from '@/script/utils/method.js';
import {
	fields,
	chartList
} from '@/script/constant/fusionEffectMonitor.js';

export default {
	name: 'fusionEffectMonitor',
	components: {
		searchBar,
		ChartCard
	},
	data() {
		const { positionDistricts } = this.$store.getters;
		const firstProvince = getAreasToCity(positionDistricts, true).at(0) || '';
		return {
			form: {
				createTime: null,
				// 默认第一个省份
				chName: firstProvince.label,
			},
			chartList: chartList()
		};
	},
	computed: {
		fields() {
			const { positionDistricts } = this.$store.getters;
			return fields(
				{
					chName: getAreasToCity(positionDistricts, true),
				}
			);
		},
	},
	mounted() {
		this.search();
	},
	methods: {
		search() {
			// 实际项目中，这里应该调用API获取图表数据
			this.fetchChartData();
		},
		fetchChartData() {
			const { chName, createTime } = this.form;
			const [startTime, endTime] = createTime || [];
			this.highGetPost(
				'positionServiceApi',
				'getFusionEffect',
				{
					startTime: startTime,
					endTime: endTime,
					province: chName || undefined
				},
				'用户面和控制面融合效果监控查询'
			).then(({ data }) => {
				// 处理接口返回的数据
				const processChartData = (dataArray) => {
					if (!dataArray || !dataArray.length) return { xAxis: [], series: [] };

					// 按时间排序
					dataArray.sort((a, b) => a.time.localeCompare(b.time));

					// 提取x轴和y轴数据
					const xAxis = dataArray.map(item => {
						// // 提取小时部分
						// const hour = item.time.length >= 10 ? item.time.substring(8, 10) : '00';
						// return `${hour}:00`;
						return timeFormat(item.time);
					});

					const series = dataArray.map(item => parseFloat(item.value));

					return { xAxis, series };
				};
				this.chartList = chartList({
					timeCoverage: {
						yAxisType: 'percent',
						data: processChartData(data.fusionTimeCoverages)
					},
					matchRate: {
						yAxisType: 'percent',
						data: processChartData(data.matchRatios)
					},
					abnormalLatLong: {
						yAxisType: 'percent',
						data: processChartData(data.abnormalLocationUserRatios)
					},
					abnormalCell: {
						yAxisType: 'value',
						data: processChartData(data.abnormalCellNums)
					},
					density: {
						yAxisType: 'value',
						data: processChartData(data.locationPointDensitys)
					}
				});
			});
		}
	},
};
</script>

<style lang="less" scoped>
.fusionEffectMonitor {
	width: 100%;
	height: 100%;
	padding: 1.33rem;
	display: flex;
	flex-direction: column;

	.chart-list {
		flex: 1;
		overflow: auto;
		margin-top: 16px;

		.chart-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-template-rows: repeat(3, 1fr);
			gap: 24px;
			padding: 4px;
			height: 100%;
			min-height: 700px;
		}
	}
}
</style>
