<template>
    <div class="detail-wrapper">
        <div class="top">
            <button class="btn-plain mt-btn__small" slot="btn" @click="handleReturn">
                <em class="highlyavailable-returnIcon"></em>
                <span>返回</span>
            </button>
            <span class="text">数据详情</span>
        </div>
        <div class="content">
            <tab :tabActive.sync="tabActive" :tabList="tabList"></tab>
            <div class="detail-wrapper-content">
                <mainData v-if="tabActive == '1'" />
                <crowdPortrait v-if="tabActive == '2'" />
                <thermalData v-if="tabActive == '3'" />
            </div>
        </div>
    </div>
</template>

<script>
import tab from '_com/tabs/index.vue';
import mainData from './components/detail/mainData.vue';
import thermalData from './components/detail/thermalData.vue';
import crowdPortrait from './components/detail/crowdPortrait.vue';
export default {
    name: 'detail',
    components: {
        tab,
        mainData,
        thermalData,
        crowdPortrait,
    },
    data() {
        return {
            tabActive: '1',
            tabList: [
                { name: '主要数据', key: '1' },
                { name: '人群画像', key: '2' },
                { name: '热力数据', key: '3' },
            ],
        };
    },
    computed: {
        queryRoute() {
            return this.$route.query;
        },
    },
    created() {
        if (this.queryRoute.isHot) {
            this.tabActive = '3';
        }
    },
    methods: {
        handleReturn() {
            this.$router.back();
        },
    },
};
</script>

<style lang="less" scoped>
.detail-wrapper {
    width: 100%;
    height: 100%;
    .top {
        width: 100%;
        height: 60px;
        background: #fff;
        display: flex;
        align-items: center;
        padding: 14px 32px 14px 32px;
        box-shadow: inset 0px -1px 0px 0px #dcdcdc;
    }
    .content {
        width: 100%;
        height: calc(100% - 60px);
        background: #f4f6f8;
        padding: 0 32px;
    }
    .btn-plain {
        background: #fff;
        border-radius: 4px;
        border: 1px solid #dcdcdc;
        display: flex;
        align-items: center;
        &:hover {
            opacity: 0.8;
        }
    }
    .text {
        font-size: 20px;
        color: #383838;
        line-height: 40px;
        font-weight: 600;
        padding-left: 10px;
    }
    &-content {
        width: 100%;
        height: calc(100% - 103px);
        background: #ffffff;
        box-shadow: 0px 0px 0.89rem 0px rgba(65, 67, 68, 0.15);
        border-radius: 0px 0.44rem 0.44rem 0.44rem;
    }
}
</style>
