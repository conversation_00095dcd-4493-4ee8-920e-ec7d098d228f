<template>
    <div class="select-scene" :style="boxH">
        <div class="select-scene__head">
            <span>请选择区域</span>
            <el-select
                v-model="selectAreaType"
                size="mini"
                placeholder="请选择"
                @change="toggleAreaType"
            >
                <el-option
                    v-for="opt in areaTypeOps"
                    :key="opt.label"
                    :value="opt.value"
                    :label="opt.label"
                />
            </el-select>
        </div>
        <div class="select-scene__body">
            <el-form class="select-scene__search" size="mini" @submit.native.prevent>
                <el-input v-model="searchVal" class="input-with-select" :placeholder="`搜索`">
                    <el-select
                        v-model="selectVal"
                        slot="prepend"
                        placeholder="请选择"
                        @change="handleSelect"
                    >
                        <el-option
                            v-for="opt in selectOps"
                            :key="opt.label"
                            :value="opt.value"
                            :label="opt.label"
                        />
                    </el-select>
                </el-input>
            </el-form>
            <div class="select-scene__list">
                <el-checkbox-group
                    v-model="checkedData[selectVal]"
                    ref="checkboxRef"
                    class="checklist overflow-auto"
                    @scroll.native="handleScroll"
                >
                    <el-checkbox
                        v-for="item in showOptions"
                        :class="{
                            isActive: activeItem === item.value,
                            divider: item.value.includes('all'),
                        }"
                        :label="item.value"
                        :key="item.value"
                        @change="(status) => handleChangeItem(item, status)"
                    >
                        <span :title="item.label">{{ item.label }} </span>
                    </el-checkbox>
                </el-checkbox-group>
                <!-- 提示/操作 -->
                <div class="footer">
                    <span v-if="showOptions.length === 5" class="tip"
                        >最多展示100个，请搜索查找</span
                    >
                    <div v-else-if="showOptions.length > 5" class="operate">
                        <span v-show="isShowTip" class="toggle">最多显示100条</span>
                    </div>
                    <span class="account"
                        >{{ formatShowOptionsLen(showOptions) }}/{{
                            options.length && options.length - 1
                        }}</span
                    >
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'select-scene',
    props: {
        checkedData: {
            type: Array,
            default: () => ({}),
        },
        selectOps: {
            type: Array,
            default: () => [],
        },
        options: {
            type: Array,
            default: () => [],
        },
        choiceAreaType: {
            type: Number,
            default: 1,
        },
    },
    data() {
        return {
            searchVal: '',
            selectVal: 0,
            activeItem: '',
            areaTypeOps: [
                { label: '全部', value: 1 },
                { label: '我的区域', value: 2 },
                { label: '公共区域', value: 3 },
            ],
            selectAreaType: 1,
            isShowTip: false,
        };
    },
    computed: {
        showOptions() {
            return this.options
                .filter(
                    (item) => item.value === this.searchVal || item.label.includes(this.searchVal)
                )
                .slice(0, 101);
        },
        boxH() {
            return { height: '500px' };
        },
    },
    watch: {
        selectAreaType(newType) {
            this.$emit('update:choiceAreaType', newType);
        },
        showOptions() {
            this.$nextTick(() => {
                this.$refs.checkboxRef.$el.scrollTop = 0;
            });
        },
    },
    methods: {
        clickRectangle(item) {
            this.$emit('openGisDialog', item, this.options);
        },
        handleClkItem(tag) {
            this.$emit('deleteTag', tag);
        },
        handleChangeItem(item, status) {
            this.activeItem = item.value;
            this.$emit('sceneCheck', item, status);
        },
        handleSelect(value) {
            this.$emit('selectOps', value);
        },
        toggleAreaType(value) {
            this.$emit('toggleAreaType', value);
        },
        formatShowOptionsLen(showOptions) {
            const isExistAll = showOptions.some((item) => item.value === `${this.selectVal}-all`);
            return isExistAll ? showOptions.length - 1 : showOptions.length;
        },
        handleScroll(e) {
            const { scrollTop, clientHeight, scrollHeight } = e.target;
            this.isShowTip = scrollTop + clientHeight === scrollHeight;
        },
    },
};
</script>
<style lang="less" scoped>
.select-scene {
    display: inline-block;
    margin-right: 8px;
    height: 280px;
    background-color: #ffffff;
    border-radius: 2px;
    border: 1px solid #d9d9d9;

    &__head {
        display: flex;
        justify-content: space-between;
        height: 32px;
        background-color: #f6f7fa;
        padding-left: 14px;
        padding-right: 8px;
        & > .el-select {
            width: 100px;
            height: 26px;
            /deep/ .el-input {
                height: 28px;
                .el-input__inner {
                    height: 26px;
                    line-height: 26px;
                }
            }
        }
    }

    &__body {
        height: calc(100% - 32px);
    }

    &__search {
        height: 40px;
        padding: 8px 8px 0;
        .el-select {
            width: 100px;
        }
        /deep/ .el-input-group__prepend {
            background-color: #fff;
        }
    }

    &__list {
        position: relative;
        display: flex;
        height: calc(100% - 40px);
        padding: 8px 8px 0 8px;
        .checklist {
            display: flex;
            margin: 0;
            padding-left: 0;
            height: 100%;
            min-width: 120px;
            flex-direction: column;
            flex: 1;
            max-height: calc(100% - 30px);
            & > .el-checkbox {
                margin: 0 4px;
                height: 32px;
                padding-left: 8px;
                font-size: 13px;
                /deep/ .el-checkbox__label {
                    padding-left: 7px;
                    span {
                        display: inline-block;
                        vertical-align: middle;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        width: 210px;
                    }
                }
                &:hover {
                    cursor: pointer;
                    background-color: #f5f9ff;
                }
                &.isActive {
                    background-color: #edf3ff;
                }
            }
            &::-webkit-scrollbar {
                width: 10px;
                height: 1px;
            }
            &::-webkit-scrollbar-thumb {
                border-radius: 10px;
                box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                background: #f6f7fa;
            }
            &::-webkit-scrollbar-track {
                box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                border-radius: 10px;
                background: transparent;
            }
        }
        .footer {
            position: absolute;
            width: calc(100% - 30px);
            left: 20px;
            bottom: 4px;
            font-size: 13px;
            .tip {
                color: rgba(0, 0, 0, 0.45);
            }
            .operate {
                .toggle {
                    position: absolute;
                    left: 0;
                    bottom: -4px;
                    color: #adafb3;
                }
            }
            .account {
                position: absolute;
                right: 0;
                bottom: -4px;
                color: #adafb3;
            }
        }
    }
}

.overflow-auto {
    overflow: auto;
}

.overflow-hidden {
    overflow: hidden;
}

.divider {
    border-bottom: 1px solid #dedede;
}
</style>
