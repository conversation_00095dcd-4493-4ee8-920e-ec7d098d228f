import norMalConfig from '../normal-config';
const _url = '/realTimeToolSevice';
const realTimeToolSevice = {
    realTimeToolSeviceApi: {
        /* 投诉应用黑白名单 */
        // 新增白名单
        addBlackWhiteRoster(params) {
            return norMalConfig(`${_url}/complaintTool-service/blackWhiteRoster/add`, params);
        },
        // 修改白名单
        modifyBlackWhiteRoster(params) {
            return norMalConfig(`${_url}/complaintTool-service/blackWhiteRoster/modify`, params);
        },
        // 删除白名单
        removeBlackWhiteRoster(params) {
            return norMalConfig(`${_url}/complaintTool-service/blackWhiteRoster/remove`, params);
        },
        // 白名单列表
        getBlackWhite(params) {
            return norMalConfig(`${_url}/complaintTool-service/blackWhiteRoster/pageList`, params);
        },
        // 白名单号码
        getWhitePhone(params) {
            return norMalConfig(
                `${_url}/complaintTool-service/blackWhiteRoster/getWhitePhone`,
                params
            );
        },
        /* 投诉应用权限 */
        // 新增权限
        authAdd(params) {
            return norMalConfig(`${_url}/complaintTool-service/complaint/authAdd`, params);
        },
        // 修改权限信息
        authUpdate(params) {
            return norMalConfig(`${_url}/complaintTool-service/complaint/authUpdate`, params);
        },
        // 删除权限信息
        authDelete(params) {
            return norMalConfig(`${_url}/complaintTool-service/complaint/authDelete`, params);
        },
        // 权限列表
        getAuthList(params) {
            return norMalConfig(`${_url}/complaintTool-service/complaint/authList`, params);
        },
        /* 用户数据查询权限申请 */
        // 权限申请
        addPermission(params) {
            return norMalConfig(`${_url}/complaintTool-service/permission/add`, params);
        },
        // 权限查询
        getPermissionList(params) {
            return norMalConfig(`${_url}/complaintTool-service/permission/list`, params);
        },
        // 权限修改
        updatePermission(params) {
            return norMalConfig(`${_url}/complaintTool-service/permission/update`, params);
        },
        // 权限删除
        delPermission(params) {
            return norMalConfig(`${_url}/complaintTool-service/permission/del`, params);
        },
        // 审批
        approvePermission(params) {
            return norMalConfig(`${_url}/complaintTool-service/permission/approve`, params);
        },
        /* 投诉用户指定时间轨迹呈现 */
        // 投诉用户指定时间轨迹呈现页面-分页列表
        queryPageDetails(params) {
            return norMalConfig(
                `${_url}/complaintTool-service/timedTrajectory/queryPageDetails`,
                params
            );
        },
        // 投诉用户指定时间轨迹呈现页面-轨迹详情
        queryTrajectoryDetails(params) {
            return norMalConfig(
                `${_url}/complaintTool-service/timedTrajectory/queryTrajectoryDetails`,
                params
            );
        },
        /* 用户数据查询权限统计 */
        queryUserPermission(params) {
            return norMalConfig(
                `${_url}/complaintTool-service/timedTrajectory/queryUserPermission`,
                params
            );
        },
        /* 用户电子围栏轨迹稽核 */
        analyTrajectoryDetail(params) {
            return norMalConfig(
                `${_url}/complaintTool-service/timedTrajectory/queryTrajectory/analy`,
                params
            );
        }
    }
};
export default realTimeToolSevice;
