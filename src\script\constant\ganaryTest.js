const expandData = [
    {
        prop: 'updateTime',
        label: '更新时间:',
        labelWidth: '70px',
        element: 'el-date-picker',
        bind: {
            clearable: true,
            type: 'datetimerange',
            'range-separator': '-',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss',
        },
        span: 6,
    },
];

const fields = (isOpen, mapOpts = {}) => {
    let list = [
        {
            prop: 'grayscaleInterface',
            label: '灰度依凭接口:',
            labelWidth: '100px',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true,
            },
            span: 4,
        },
        {
            prop: 'grayscaleOperatorId',
            label: '灰度依凭租户:',
            labelWidth: '100px',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true,
            },
            span: 4,
        },
        {
            prop: 'grayscaleType',
            label: '灰度依凭类型:',
            labelWidth: '100px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '租户ID', value: 1 },
                    { label: '接口链接', value: 2 },
                    { label: '租户&接口共同作用', value: 3 },
                ],
            },
            span: 4,
        },
        {
            prop: 'isValid',
            label: '有效状态:',
            labelWidth: '70px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '有效', value: 1 },
                    { label: '无效', value: 0 },
                ],
            },
            span: 4,
        },
        {
            prop: 'createTime',
            label: '创建时间:',
            labelWidth: '80px',
            element: 'el-date-picker',
            bind: {
                clearable: true,
                type: 'datetimerange',
                'range-separator': '-',
                'start-placeholder': '开始日期',
                'end-placeholder': '结束日期',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss',
            },
            span: 5,
        },
        {
            span: 1
        },
        { span: 2, prop: 'showExpand', itemClassName: 'not-border' },
    ];
    if (isOpen) {
        list.splice(7, 0, ...expandData);
    }
    return list;
};

const tableColumns = [
    {
        prop: 'grayscaleInterface',
        label: '灰度依凭接口',
    },
    {
        prop: 'grayscaleOperatorId',
        label: '灰度依凭租户',
    },
    {
        prop: 'grayscaleType',
        label: '灰度依凭类型',
    },
    {
        prop: 'isValid',
        label: '有效状态',
    },
    {
        prop: 'comment',
        label: '备注',
    },
    {
        prop: 'createTime',
        label: '创建时间',
    },
    {
        prop: 'updateTime',
        label: '更新时间',
    },
    {
        prop: 'operation',
        label: '操作',
        width: '270',
    },
];

const regionTypeList = {
    1: '自定义区域',
    2: '省',
    3: '市',
    4: '区县'
};

const statusList = {
    0: '无效',
    1: '有效',
};

const grayscaleTypeList = {
    1: '租户ID',
    2: '接口链接',
    3: '租户&接口共同作用'
};

const startFields = [
    {
        prop: 'startTime',
        label: '开始时间：',
        labelWidth: '90px',
        span: 24,
        rules: [{ required: true, message: '请选择开始时间！', trigger: 'blur' }],
    },
    {
        prop: 'endTime',
        label: '预设结束时间：',
        labelWidth: '108px',
        element: 'el-date-picker',
        bind: {
            clearable: true,
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss',
        },
        span: 24,
    },
];

export {
    fields,
    tableColumns,
    regionTypeList,
    statusList,
    startFields,
    grayscaleTypeList
};