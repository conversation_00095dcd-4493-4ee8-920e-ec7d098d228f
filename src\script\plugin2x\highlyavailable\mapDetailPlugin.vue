<!--
 * @Date: 2025-02-14 11:35:48
 * @LastEditors: liurong <EMAIL>
 * @LastEditTime: 2025-02-27 14:00:23
 * @FilePath: \mtex-static-highlyavailable\src\script\plugin2x\highlyavailable\mapDetailPlugin.vue
-->
<template>
    <div class="detail-wrapper">
        <div class="content">
            <tab :tabActive.sync="tabActive" :tabList="tabList"></tab>
            <div class="detail-wrapper-content">
                <realTimeMonitor v-if="tabActive === '1'" />
                <historyData v-if="tabActive === '2'" />
            </div>
            <el-button icon="el-icon-back" class="btn-plain" size="small" @click="handleReturn"
                >返回</el-button
            >
        </div>
    </div>
</template>

<script>
import tab from '_com/tabs/index.vue';
import realTimeMonitor from './components/mapDetail/realTimeMonitor.vue';
import historyData from './components/mapDetail/historyData.vue';

export default {
    name: 'mapDetail',
    components: {
        tab,
        realTimeMonitor,
        historyData,
    },
    data() {
        return {
            tabActive: '1',
            tabList: [
                { name: '实时监控', key: '1' },
                { name: '历史数据', key: '2' },
            ],
        };
    },
    methods: {
        handleReturn() {
            this.$router.back();
        },
    },
};
</script>

<style lang="less" scoped>
.detail-wrapper {
    width: 100%;
    height: 100%;
    .content {
        position: relative;
        width: 100%;
        height: 100%;
        background: #f4f6f8;
        padding: 0 32px;
    }

    .btn-plain {
        position: absolute;
        top: 32px;
        right: 30px;
    }

    .text {
        font-size: 20px;
        color: #383838;
        line-height: 40px;
        font-weight: 600;
        padding-left: 10px;
    }

    &-content {
        width: 100%;
        height: calc(100% - 103px);
        background: #fff;
        box-shadow: 0px 0px 0.89rem 0px rgba(65, 67, 68, 0.15);
        border-radius: 0px 0.44rem 0.44rem 0.44rem;
    }
}
</style>
