const token = localStorage.getItem('token');
import { rel_operateUserId, rel_sourceSystemId, rel_sourceSystemName } from '../common/baseCompExt';
import GatewayAxios from '../common/gatewayAxios.js';
// import axios from 'axios';
const norMalConfig = function (url, params = {}, GateWay) {
    const API = new GatewayAxios(
        rel_operateUserId,
        rel_sourceSystemId,
        rel_sourceSystemName,
        GateWay
    );
    return new Promise((resolve, reject) => {
        let newParams = {
            requestData: params
        };
        API.post(url, newParams)
            .then((res) => {
                resolve(res);
            })
            .catch((err) => {
                console.log('err: ', err);
                reject(err);
            });
    });
};

export default norMalConfig;

// export const fileConfig = function (url, params, options) {
//     const API = new GatewayAxios(rel_operateUserId, rel_sourceSystemId, rel_sourceSystemName);
//     return new Promise((resolve, reject) => {
//         let newParams = {
//             'operateUserId': rel_operateUserId,
//             'webToken': token,
//             'sourceSystemName': rel_sourceSystemName,
//             requestData: params
//         };
//         API.getFile(url, newParams, options).then((res) => {
//             resolve(res);
//         }).catch((err) => {
//             console.log('err: ', err);
//             reject(err);
//         });
//     });
// };
export const fileConfig = function (url, params, options) {
    systemUtil.popupLoading(true, null);
    const API = new GatewayAxios(rel_operateUserId, rel_sourceSystemId, rel_sourceSystemName);
    return new Promise((resolve, reject) => {
        API.getFile(url, params, options)
            .then((res) => {
                systemUtil.popupLoading(false, null);
                resolve(res);
            })
            .catch((err) => {
                systemUtil.popupLoading(false, null);
                console.log('err: ', err);
                reject(err);
            });
    });
};
export const norMalConfigGet = function (url, params = {}, options = {}) {
    const API = new GatewayAxios(rel_operateUserId, rel_sourceSystemId, rel_sourceSystemName);
    return new Promise((resolve, reject) => {
        let newParams = {
            operateUserId: rel_operateUserId,
            webToken: token,
            sourceSystemName: rel_sourceSystemName,
            requestData: params
        };
        API.get(url, newParams, options)
            .then((res) => {
                resolve(res);
            })
            .catch((err) => {
                console.log('err: ', err);
                reject(err);
            });
    });
};
/* axios.interceptors.request.use((config) => {
    config.headers.token = token;
    return config;
}); */

