const fields = (Opts = { msisdn: [] }) => {
    return [
        {
            prop: 'listType',
            label: '名单类型：',
            labelWidth: '70',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '白名单', value: 1 },
                    { label: '黑名单', value: 2 }
                ]
            },
            span: 4
        },
        {
            prop: 'complaintType',
            label: '投诉类型：',
            labelWidth: '70',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '实时', value: 1 },
                    { label: '离线', value: 2 }
                ]
            },
            span: 4
        },
        {
            prop: 'msisdn',
            label: '用户号码：',
            labelWidth: '70',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true
            },
            span: 4
        },
        {
            prop: 'authStime',
            label: '授权开始日期：',
            labelWidth: '90',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'date',
                format: 'yyyy-MM-dd',
                'value-format': 'yyyy-MM-dd'
            },
            span: 5
        },
        {
            prop: 'authEtime',
            label: '授权结束日期：',
            labelWidth: '90',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'date',
                format: 'yyyy-MM-dd',
                'value-format': 'yyyy-MM-dd'
            },
            span: 5
        },
        {
            prop: 'startTime',
            label: '开始时间：',
            labelWidth: '70',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss'
            },
            span: 4
        },
        {
            prop: 'endTime',
            label: '结束时间：',
            labelWidth: '70',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss'
            },
            span: 4
        },
        {
            span: 2
        }
    ];
};
const tableColumns = [
    {
        prop: 'id',
        label: '字段ID'
    },
    {
        prop: 'listType',
        label: '名单类型'
    },
    {
        prop: 'complaintType',
        label: '投诉类型'
    },
    {
        prop: 'createUserId',
        label: '租户ID'
    },
    {
        prop: 'userName',
        label: '用户账号'
    },
    {
        prop: 'msisdn',
        label: '用户号码'
    },
    {
        prop: 'authStime',
        label: '授权开始日期'
    },
    {
        prop: 'authEtime',
        label: '授权结束日期'
    },
    {
        prop: 'isValid',
        label: '是否生效'
    },
    {
        prop: 'createTime',
        label: '创建时间'
    },
    {
        prop: 'lastUpdateTime',
        label: '最后修改时间'
    },
    {
        prop: 'operate',
        label: '操作'
    }
];

const dialogFields = (Opts = { msisdn: [] }) => {
    return [
        {
            prop: 'listType',
            label: '名单类型：',
            labelWidth: '108px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '白名单', value: 1 },
                    { label: '黑名单', value: 2 }
                ]
            },
            span: 24
        },
        {
            prop: 'complaintType',
            label: '投诉类型：',
            labelWidth: '108px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '实时', value: 1 },
                    { label: '离线', value: 2 }
                ]
            },
            span: 24
        },
        {
            prop: 'msisdn',
            label: '用户号码：',
            labelWidth: '108px',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true
            },
            span: 24
        },
        {
            prop: 'authStime',
            label: '授权开始日期：',
            labelWidth: '108px',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'date',
                format: 'yyyy-MM-dd',
                'value-format': 'yyyy-MM-dd'
            },
            span: 24
        },
        {
            prop: 'authEtime',
            label: '授权结束日期：',
            labelWidth: '108px',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'date',
                format: 'yyyy-MM-dd',
                'value-format': 'yyyy-MM-dd'
            },
            span: 24
        },
        {
            prop: 'isValid',
            label: '是否生效：',
            labelWidth: '108px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '是', value: 1 },
                    { label: '否', value: 0 }
                ]
            },
            span: 24
        }
    ];
};
export { fields, tableColumns, dialogFields };
