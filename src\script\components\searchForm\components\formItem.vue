<template functional>
    <el-form-item
        class="def-form-item"
        :class="{
            'w-full': props.isLayout,
            'mr-12': !props.isLayout,
            isTransparent: props.field.isTransparent || !props.field.prop,
            isDisabled: props.field.isDisabled,
            pl_10: !props.field.labelWidth,
            [props.field.className || '']: !!props.field.className,
        }"
        v-bind="{
            label: props.field.label,
            prop: props.field.prop,
            rules: props.field.rules,
            size: props.field.size,
            labelWidth: props.field.labelWidth,
            style: props.field.style,
        }"
    >
        <template #error="{ error }">
            <span v-if="props.isBubbleTip" class="custom-bubble-tip">
                {{ error }}
            </span>
        </template>
        <slot></slot>
    </el-form-item>
</template>

<script>
export default {
    name: 'formItem',
    props: {
        field: {
            type: Object,
        },
        isBubbleTip: {
            type: <PERSON><PERSON><PERSON>,
        },
        isLayout: {
            type: Boolean,
        },
    },
};
</script>

<style lang="less" scoped>
.def-form-item {
    display: inline-flex;
    margin: 0 0 12px 0;
    background-color: #fff;
    .custom-bubble-tip {
        position: absolute;
        right: 0;
        top: -26px;
        padding: 0 6px;
        height: 20px;
        line-height: 20px;
        border-radius: 2px;
        font-size: 12px;
        color: #fff;
        background-color: #f56c6c;
        z-index: 999;
        &::before {
            content: '';
            position: absolute;
            margin-top: -5px;
            width: 10px;
            height: 8px;
            right: 50%;
            bottom: -3px;
            transform: translateX(50%) rotate(45deg);
            background-color: #f56c6c;
        }
    }
    &.isTransparent {
        outline: none;
        background-color: transparent;
    }
    &.isDisabled {
        /deep/ .el-form-item__label {
            background-color: #f5f7fa;
        }
    }
    &.pl_10 {
        /deep/ .el-form-item__label {
            padding-left: 10px;
        }
    }
    /deep/ .el-form-item__label {
        position: relative;
        margin-bottom: 0;
        padding-right: 10px;
        &::before {
            margin-right: 2px !important;
        }
    }
    /deep/ .el-form-item__content {
        position: relative;
        display: flex;
        // margin-left: 0 !important;
        align-items: center;
        flex: 1;
        .el-input__inner {
            // padding: 0 10px;
            border-radius: 0;
        }
    }
    &:last-of-type {
        margin-right: 0;
    }
}
.mr-12 {
    margin-right: 12px;
}
.w-full {
    widows: 100%;
}
</style>
