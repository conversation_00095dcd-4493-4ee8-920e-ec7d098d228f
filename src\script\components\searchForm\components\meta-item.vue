<template>
    <component
        v-if="field.element"
        :is="field.element"
        :class="{ 'w-full': !('isFull' in field) || field.isFull }"
        v-model="form[field.prop]"
        v-bind="field.bind"
        v-on="field.on"
    >
        <!-- 处理函数式slot -->
        <render-slot v-if="!isValidReferenceType(field.slot)" :slot="field.slot" />
        <!-- 引用类型 -->
        <template v-else>
            <component
                v-for="(it, inx) in processSlot(field.slot)"
                :is="field.slot.element || it.element"
                :key="inx"
                v-bind="it"
                v-on="it.on"
            >
                <render-slot v-if="it.slot" :slot="it.slot" />
            </component>
        </template>
    </component>
</template>

<script>
// 在组件内部定义render-slot
const RenderSlot = {
    functional: true,
    props: ['slot'],
    render(h, ctx) {
        const slotContent = ctx.props.slot;
        if (typeof slotContent !== 'function') {
            return h('span', ctx.data, [slotContent]);
        }
        return slotContent(h, ctx);
    },
};

export default {
    name: 'meta-item',
    components: {
        RenderSlot,
    },
    props: {
        field: {
            type: Object,
        },
        form: {
            type: Object,
        },
    },
    methods: {
        isObject(type) {
            return Object.prototype.toString.call(type) === '[object Object]';
        },
        isFunction(type) {
            return Object.prototype.toString.call(type) === '[object Function]';
        },
        // 是否为有效基础数据类型
        isValidBaseType(value) {
            return ['string', 'number', 'boolean'].includes(typeof value);
        },
        isValidReferenceType(type) {
            return type !== null && typeof type === 'object' && !this.isFunction(type);
        },
        processSlot(slot) {
            switch (true) {
                case Array.isArray(slot):
                    return slot;
                case this.isObject(slot) && slot.element && Array.isArray(slot.enums):
                    return slot.enums;
                case this.isObject(slot):
                    return [slot];
                default:
                    return [];
            }
        },
    },
};
</script>

<style lang="less" scoped>
.w-full {
    width: 100%;
}
</style>
