import Api from '@/script/api/index';
export default {
    methods: {
        /**
         * 接口请求封装
         * @param apiItem 请求方式
         * @param postName 接口名
         * @param _param 入参
         * @param errContent 错误提示内容
         * @param callBack 成功逻辑业务代码callBack
         * @param notMainApi 是否是不关键接口，是不关键接口报错弹窗换成element
         * @param catchCallback catch时回调
         */
        highGetPost(apiItem, postName, _param, errContent, isPopupLoading = true) {
            if (isPopupLoading) systemUtil.popupLoading(true, null);
            return Api[apiItem][postName](_param)
                .then((rcvData) => {
                    systemUtil.popupLoading(false, null);
                    return rcvData;
                })
                .catch((err) => {
                    systemUtil.popupLoading(false, null);
                    this.$message.error(errContent + '失败，请联系管理员！' || err);
                    return Promise.reject(err);
                });
        },
        openDialog(msg, confirmFn, showCancelButton = true, cancelFn) {
            this.$confirm(
                `<div class="dialog-title">提醒</div>
            <div>${msg}</div>`,
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    dangerouslyUseHTMLString: true,
                    showClose: false,
                    customClass: 'my-task-dialog',
                    type: 'warning',
                    showCancelButton: showCancelButton,
                }
            )
                .then(confirmFn)
                .catch(cancelFn);
        },
    },
};
