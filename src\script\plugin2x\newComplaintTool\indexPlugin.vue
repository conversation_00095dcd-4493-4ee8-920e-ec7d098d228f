<template>
    <div id="complain-tool" class="complain-tool">
        <complaint-menu @jumpRouter="jumpRouter" />
        <div class="main-content">
            <router-view class="page-route"></router-view>
        </div>
    </div>
</template>
<script>
import complaintMenu from './menu/index.vue';
export default {
    name: 'newComplaintTool',
    components: {
        complaintMenu
    },
    data() {
        return {};
    },
    mounted() {
        this.onload();
    },
    methods: {
        jumpRouter(value) {
            this.openSubPath(value, '', {});
        },
        onload() {
            const origin = window.location.href;
            const lastItem = origin.substring(origin.lastIndexOf('/') + 1);
            const isSub = lastItem.split('-');
            if (isSub.length !== 1) {
                this.jumpRouter('complaintBlacklist');
            }
        }
    }
};
</script>
<style lang="less" scoped>
.complain-tool {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    .main-content {
        width: 100%;
        flex: 1;
        height: 0;
        background: linear-gradient(to right, #e6f7ff 0%, #ededfc 40%, #eceefc 100%);
    }
}
</style>
