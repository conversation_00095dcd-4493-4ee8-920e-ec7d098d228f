<template>
    <div class="mainData-wrapper">
        <searchBar :fields="fields" :form="form" :isLayout="false">
            <el-button
                class="highlyavailable-primary-btn"
                type="primary"
                size="small"
                @click="search()"
                >查询</el-button
            >
        </searchBar>
        <dataTable
            class="dataTable"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :total="total"
            stripe
            border
            :updateTable="getTableData"
        >
        </dataTable>
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import { fields, tableColumns } from '@/script/constant/mainData.js';
import { detailMixin } from './mixin';

export default {
    name: 'mainData',
    components: {
        searchBar,
        dataTable,
    },
    mixins: [detailMixin],
    data() {
        return {
            form: {
                time: [],
                displayIndicators: '',
            },
            fields,
            columns: tableColumns,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15,
            },
            total: 0,
        };
    },
    created() {
        this.initTime();
        this.search();
    },
    methods: {
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            const { time } = this.form;
            const [startTime, endTime] = time || [];
            const { regionType, regionId, timeSize } = this.queryRoute;
            this.highGetPost(
                'monitorApi',
                'getCrowdHot',
                {
                    regionType,
                    timeSize,
                    regionId,
                    startTime,
                    endTime,
                    pageNum: curPage,
                    pageSize,
                    modelList: {
                        modelId: 7,
                        modelParam: {
                            level: 2,
                            residentType: 1,
                            top: 100,
                        },
                    },
                    paramData: {
                        minVisitNum: 1,
                        maxVisitNum: 5,
                    },
                },
                '高可用监控查询'
            ).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    const data = res.data;
                    const { primaryResult, standbyResult, totalNum } = data;
                    const { arriveCrowd, flowCrowd, residentCrowd } = primaryResult;
                    const {
                        arriveCrowd: standbyArriveCrowd,
                        flowCrowd: standbyFlowCrowd,
                        residentCrowd: standbyResidentCrowd,
                    } = standbyResult;
                    this.tableData = arriveCrowd.map((item) => {
                        const { arriveCnt, timeSpan } = item;
                        const standArriveItem = standbyArriveCrowd.find(
                            (item) => item.timeSpan === timeSpan
                        );
                        const flowCrowdItem = flowCrowd.find((item) => item.timeSpan === timeSpan);
                        const residentCrowdItem = residentCrowd.find(
                            (item) => item.timeSpan === timeSpan
                        );
                        const standbyFlowCrowdItem = standbyFlowCrowd.find(
                            (item) => item.timeSpan === timeSpan
                        );
                        const standbyResidentItem = standbyResidentCrowd.find(
                            (item) => item.timeSpan === timeSpan
                        );
                        return {
                            time: timeSpan,
                            totalPeopleMain: flowCrowdItem.flowTotalCnt,
                            totalPeopleBackup: standbyFlowCrowdItem.flowTotalCnt,
                            flowInMain: flowCrowdItem.flowInCnt,
                            flowInBackup: standbyFlowCrowdItem.flowInCnt,
                            flowOutMain: flowCrowdItem.flowOutCnt,
                            flowOutBackup: standbyFlowCrowdItem.flowOutCnt,
                            visitsMain: arriveCnt,
                            visitsBackup: standArriveItem.arriveCnt,
                            residentMain: residentCrowdItem.residentCnt,
                            residentBackup: standbyResidentItem.residentCnt,
                        };
                    });
                    this.total = totalNum;
                } else {
                    this.$message.warning(res.returnMsg);
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
.mainData-wrapper {
    width: 100%;
    height: 100%;
    padding: 1.33rem;
    display: flex;
    flex-direction: column;
}
.dataTable {
    width: 100%;
    flex: 1;
    height: 0;
}
</style>
