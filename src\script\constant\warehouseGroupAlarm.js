const warnIndexOpts = [
    {
        label: '网络波动导致CK查询失败',
        value: [1]
    },
    {
        label: '缺数',
        value: [2]
    },
    {
        label: '主备库缺数',
        value: [3]
    },
    {
        label: '骤增骤降',
        value: [4]
    }
];
const expandData = [
    {
        prop: 'warnTypeList',
        label: '告警类型:',
        labelWidth: '70',
        bind: {
            clearable: true,
            placeholder: '请选择'
        },
        element: 'el-select',
        slot: {
            element: 'el-option',
            enums: warnIndexOpts
        },
        span: 3
    },
    {
        prop: 'fillStatusList',
        label: '补数状态:',
        labelWidth: '70',
        bind: {
            clearable: true,
            placeholder: '请选择'
        },
        element: 'el-select',
        slot: {
            element: 'el-option',
            enums: [
                {
                    label: '无需处理',
                    value: 0
                },
                {
                    label: '未处理',
                    value: 1
                },
                {
                    label: '已处理',
                    value: 2
                },
                {
                    label: '补数失败',
                    value: 3
                }
            ]
        },
        span: 3
    },
    {
        prop: 'warnTime',
        label: '告警时间:',
        labelWidth: '70',
        element: 'el-date-picker',
        bind: {
            clearable: true,
            type: 'datetimerange',
            'range-separator': '-',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss'
        },
        span: 7
    },
    {
        prop: 'fillTime',
        label: '补数时间:',
        labelWidth: '70',
        element: 'el-date-picker',
        bind: {
            clearable: true,
            type: 'datetimerange',
            'range-separator': '-',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss'
        },
        span: 7
    }
];
const fields = (isOpen, mapOpts = {}) => {
    const list = [
        {
            prop: 'userId',
            label: '租户ID:',
            labelWidth: '70',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true
            },
            span: 3
        },
        {
            prop: 'regionName',
            label: '区域名称:',
            labelWidth: '70',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true
            },
            span: 3
        },
        {
            prop: 'regionIdList',
            label: '区域ID:',
            labelWidth: '70',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true
            },
            span: 3
        },
        {
            prop: 'cityCodeList',
            label: '地市:',
            labelWidth: '40',
            element: () => import('@/script/components/selectDistrict.vue'),
            bind: {
                options: mapOpts.cityCodeList,
                props: {
                    multiple: true,
                    label: 'label',
                    value: 'value',
                    children: 'children',
                    emitPath: false
                }
            },
            span: 3
        },
        {
            prop: 'layerTypeList',
            label: '图层类型:',
            labelWidth: '70',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
                multiple: true,
                'collapse-tags': true
            },
            slot: {
                element: 'el-option',
                enums: mapOpts.layerTypeList
            },
            span: 3
        },
        {
            prop: 'warnClusterList',
            label: '告警主/备集群:',
            labelWidth: '100',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
                multiple: true,
                'collapse-tags': true
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: 'ck1', value: 1 },
                    { label: 'ck2', value: 2 },
                    { label: 'ck3', value: 3 }
                ]
            },
            span: 3
        },
        {
            prop: 'warnIndexList',
            label: '告警指标:',
            labelWidth: '70',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
                multiple: true,
                'collapse-tags': true
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '人流', value: 1 },
                    { label: '区域热力', value: 2 },
                    { label: '画像', value: 3 },
                    { label: '驻留时长', value: 4 },
                    { label: '区域人数', value: 5 },
                    { label: '区域驻留查询模型[常驻模型]', value: 6 },
                    { label: '累计到访模型', value: 7 },
                    { label: '定时调度', value: 8 }
                ]
            },
            span: 3
        },
        {
            span: 1
        },
        { span: 2, prop: 'showExpand', itemClassName: 'not-border' }
    ];
    if (isOpen) {
        list.splice(7, 0, ...expandData);
    }
    return list;
};
const tableColumns = [
    {
        prop: 'operateUserId',
        label: '租户ID'
    },
    {
        prop: 'sourceSystemName',
        label: '租户名称'
    },
    {
        prop: 'alarmStime',
        label: '告警时间'
    },
    {
        prop: 'regionName',
        label: '告警区域'
    },
    {
        prop: 'regionId',
        label: '区域ID'
    },
    {
        prop: 'cityName',
        label: '地市',
        width: 80
    },
    {
        prop: 'classifyName',
        label: '图层类型',
        width: 80
    },
    {
        prop: 'datasourceTo',
        label: '告警主/备集群'
    },
    {
        prop: 'alarmMetrics',
        label: '告警指标'
    },
    {
        prop: 'fillTableName',
        label: '告警表名'
    },
    {
        prop: 'alarmType',
        label: '告警类型'
    },
    {
        prop: 'fillStatus',
        label: '补数状态'
    },
    {
        prop: 'complementsEtime',
        label: '补数完成时间'
    },
    {
        prop: 'createTime',
        label: '告警记录生成时间'
    }
];

const alarmTypeOpts = [
    { label: '人流', value: 1 },
    { label: '区域热力', value: 2 },
    { label: '画像', value: 3 },
    { label: '驻留时长', value: 4 },
    { label: '区域人数', value: 5 },
    { label: '区域驻留查询模型[常驻模型]', value: 6 },
    { label: '累计到访模型', value: 7 },
    { label: '定时调度', value: 8 }
];
export { fields, tableColumns, warnIndexOpts, alarmTypeOpts };
