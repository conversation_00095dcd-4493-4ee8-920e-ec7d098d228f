import { setFitView } from '@/script/utils/method';

export default (g, option = {}) => {
    let graphs = [];
    let { img } = option;
    // 创建图层
    let layer = new g.layer();
    layer.visible = true;
    g.gis.scene.add(layer);

    class Point {
        constructor(latLng, data) {
            this.latLng = latLng;
            this.data = data;
            this.mesh = null;
        }
        static getGraphs() {
            return graphs;
        }
        static remove() {
            layer.removeAll();
        }
        static draw(points, isRemoveAll = true, isMove = true) {
            const isEmpty = !points || !points.length;
            if (isEmpty || isRemoveAll) {
                Point.remove();
                graphs = [];
                if (isEmpty) return;
            }

            for (const point of points) {
                const { latLng, data } = point;
                const curPoint = new Point(latLng, data);
                curPoint.draw();
                graphs.push(curPoint);
            }
            if (isMove) {
                const pointList = points.map((item) => item.latLng);
                Point.toMove(pointList);
            }
        }
        static toMove(points) {
            if (points.length < 3) {
                g.cameraControl.move(points[0]);
            } else {
                setFitView(points, g, 1);
            }
        }
        static toMovePoint(point, cb) {
            g.cameraControl.move(point);
            let timer = setTimeout(() => {
                g.gis.needUpdate = true;
                cb && cb();
                clearTimeout(timer);
                timer = null;
            }, 200);
        }
        static setHighlight(mesh, index, color) {
            g.meshList.img.changeColor(mesh, index, color);
        }
        static setPointSize(mesh, index, size) {
            g.meshList.img.changeWidth(mesh, index, size);
        }
        static destroy() {
            g = null;
            graphs = null;
            option = null;
            layer = null;
        }
        draw(latLng = this.latLng, image, size = 50) {
            const points = [{ ...latLng, ht: size === 50 ? 20 : 22, size, dir: 0 }];
            const material = g.meshList.img.getMaterial({ url: image || img, opacity: 1 });
            points.autoScale = true;
            const mesh = g.meshList.img.create(points, material);
            mesh.curPoint = this;
            layer.add(mesh);
            this.mesh = mesh;
        }
        remove() {
            layer.remove(this.mesh);
        }
    }

    return Point;
};
