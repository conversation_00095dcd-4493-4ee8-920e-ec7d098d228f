// 测试数据
const compData = {
  '区域-省份': {
    name: 'subAreaSelect',
    type: '省份',
    panelLevels: 1,
    regionList: [],
    conclusion: '',
  },
  '区域-地市': {
    name: 'subAreaSelect',
    type: '地市',
    panelLevels: 2,
    regionList: [],
    conclusion: '',
  },
  '区域-区县': {
    name: 'subAreaSelect',
    type: '区县',
    panelLevels: 3,
    regionList: [],
    conclusion: '',
  },
  '区域-区域': {
    name: 'multiAreaSelect',
    type: '区域',
    regionList: [],
    conclusion: '',
  },
};
const regionSetOps = [
  { label: '省份', value: 1 },
  { label: '地市', value: 2 },
  { label: '区县', value: 3 },
  { label: '区域', value: 4 },
];
const mapWidth = {
  区域: '622px',
  区域对: '402px',
};
export { compData, regionSetOps, mapWidth };
