<template>
	<div class="mysqlConfigManager">
		<dataTable
			class="dataTable"
			:columns="columns"
			:data="tableData"
			:pagination="pagination"
			:total="total"
			stripe
			:updateTable="getTableData"
			:isHidePagination="true"
		>
			<template #operation="{ row }">
				<div class="table-btn">
					<el-button
						class="table-btn-item"
						type="text"
						size="mini"
						@click="handleEdit(row)"
					>
						编辑
					</el-button>
					<el-button
						class="table-btn-item"
						type="text"
						size="mini"
						@click="handleDelete(row)"
					>
						删除
					</el-button>
				</div>
			</template>
		</dataTable>
		<!-- 编辑 -->
		<myDialog
			:title="dialogTypeName"
			:visible.sync="dialogVisible"
			:btnList="dialogConfigurationBtn()"
		>
			<searchBar
				:fields="dialogFields"
				:form="dialogForm"
			>
			</searchBar>
		</myDialog>
	</div>
</template>

<script>
import {
	tableColumns,
	dialogFields
} from '@/script/constant/mysqlConfigManager.js';
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import myDialog from '_com/dialog/index.vue';

export default {
	name: 'mysqlConfigManager',
	components: {
		searchBar,
		dataTable,
		myDialog,
	},
	data() {
		return {
			columns: tableColumns,
			tableData: [],
			pagination: {
				curPage: 1,
				pageSize: 15,
			},
			total: 0,
			curRow: {},
			dialogFields,
			dialogForm: {
				libName: '',
				libFlag: '',
			},
			dialogType: 'add', // add、edit
			dialogVisible: false,
		};
	},
	computed: {
		dialogTypeName() {
			switch (this.dialogType) {
				case 'add':
					return '新增';
				case 'edit':
					return '编辑';
				default:
					return '新增';
			}
		},
	},
	mounted() {
		this.search();
	},
	methods: {
		dialogConfigurationBtn() {
			return [
				{
					name: this.dialogTypeName,
					class: 'blue-btn',
					listeners: {
						click: () => {
							this.addOrEdit(this.dialogForm).then(() => {
								this.dialogVisible = false;
							});
						},
					},
				},
			];
		},
		handleAdd() {
			this.dialogType = 'add';
			this.dialogForm = this.$options.data().dialogForm;
			this.dialogVisible = true;
		},
		handleDelete(row) {
			this.$confirm('确定要删除吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.highGetPost(
					'positionServiceApi',
					'deleteMYSQLService',
					[row.libId],
					'删除'
				).then((res) => {
					if (res.serviceFlag === 'TRUE') {
						this.$message.success(res.returnMsg);
						this.search();
					} else {
						this.$message.error(res.returnMsg);
					}
				});
			});
		},
		handleEdit(row) {
			this.dialogType = 'edit';
			this.curRow = row;
			this.dialogForm = {
				libName: row.libName,
				libFlag: row.libFlag,
			};
			this.dialogVisible = true;
		},
		addOrEdit(form) {
			const { libFlag, libName } = form;
			const params = {
				createUserName: this.dialogType == 'edit' ? this.curRow.creator : undefined,
				id: this.dialogType === 'edit' ? this.curRow.libId : undefined,
				sourceId: libFlag,
				sourceName: libName
			};
			return this.highGetPost(
				'positionServiceApi',
				this.dialogType === 'add' ? 'insertMYSQLService' : 'updateMYSQLService',
				[params],
				this.dialogTypeName
			).then((res) => {
				if (res.serviceFlag === 'TRUE') {
					this.$message.success(res.returnMsg);
					this.search();
				} else {
					this.$message.error(res.returnMsg);
				}
			});
		},
		search() {
			this.pagination.curPage = 1;
			this.getTableData();
		},
		getTableData(pagination = {}) {
			// const { curPage = 1, pageSize = 15 } = pagination;
			this.highGetPost(
				'positionServiceApi',
				'getAllMYSQLServicePage',
				{},
				'MYSQL的高可用管理服务全量查询'
			).then(({ data }) => {
				this.tableData = data.map((item) => {
					return {
						libId: item.id,
						libName: item.sourceName,
						libFlag: item.sourceId,
						creator: item.createUserName,
						createTime: item.createTime,
						lastUpdateTime: item.updateTime,
					};
				});
				// this.total = data.pageTotal;
			});
		},
	},
};
</script>

<style lang="less" scoped>
.mysqlConfigManager {
	width: 100%;
	height: 100%;
	padding: 1.33rem;
	display: flex;
	flex-direction: column;
}

.dataTable {
	width: 100%;
	flex: 1;
	height: 0;
}

a {
	color: #1664ff;
	text-decoration: underline;
	cursor: pointer;
}

.table-btn {
	display: flex;

	&-item {
		position: relative;
		font-size: 14px;

		&.redText {
			color: #ff4d4f;
		}

		&::after {
			position: absolute;
			right: -13px;
			top: 8px;
			width: 1px;
			background: #ebeef5;
			height: 11px;
			content: "";
		}

		&:last-child {
			&::after {
				position: absolute;
				right: -13px;
				top: 8px;
				width: 1px;
				background: #ebeef5;
				height: 0px;
				content: "";
			}
		}
	}
}
</style>
