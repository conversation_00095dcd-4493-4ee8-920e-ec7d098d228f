<template functional>
    <el-row class="def-el-row" :gutter="props.gutter">
        <el-col
            v-for="(item, index) in props.fields"
            v-show="item.isShow !== false"
            :key="item.prop || index"
            :span="item.span || 24"
            v-bind="item.layout"
        >
            <slot :field="item"></slot>
        </el-col>
    </el-row>
</template>

<script>
export default {
    name: 'custom-layout',
    props: {
        gutter: {
            type: Number,
            default: 12,
        },
        fields: {
            type: Array,
        },
    },
};
</script>

<style lang="less" scoped>
.def-el-row {
    display: flex;
    flex-flow: wrap;
    .el-col:last-of-type .el-form-item {
        margin-right: 0;
    }
}
</style>
