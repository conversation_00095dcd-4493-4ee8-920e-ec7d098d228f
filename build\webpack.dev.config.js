const merge = require('webpack-merge');
const { baseConfig } = require('./webpack.base.config');
const config = {
    devtool: "source-map",
    //source-map 把映射文件生成到单独的文件，最完整最慢
    //cheap-module-source-map 在一个单独的文件中产生一个不带列映射的Map
    //eval-source-map 使用eval打包源文件模块,在同一个文件中生成完整sourcemap
    //cheap-module-eval-source-map sourcemap和打包后的JS同行显示，没有映射列
    devServer: {
        contentBase: false, //用于加载前端的无需打包的文件，如 ./static
        port: 8888,
        compress: true, // 服务器压缩
        open: false, // 自动打开浏览器
        host: '0.0.0.0',
    },
};
module.exports = merge(baseConfig, config);
