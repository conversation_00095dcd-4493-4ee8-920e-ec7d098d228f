<template functional>
    <div class="head-title">
        <el-button size="small" icon="el-icon-arrow-left" @click="listeners.back">返回</el-button>
        <span class="title">{{ props.title }}</span>
    </div>
</template>

<script>
export default {
    name: 'headTitle',
    props: {
        title: {
            type: String,
            default: ''
        }
    }
};
</script>

<style lang="less" scoped>
.head-title {
    display: flex;
    align-items: center;
    width: 100%;
    height: 72px;
    background-color: #fff;
    padding: 0 16px;
    box-sizing: border-box;

    .title {
        margin-left: 12px;
        font-weight: bold;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 24px;
        text-align: left;
        font-style: normal;
    }
}
</style>
