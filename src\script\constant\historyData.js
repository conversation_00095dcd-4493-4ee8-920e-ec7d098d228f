const fields = [
    {
        prop: 'nowDatasource',
        label: '所用库：',
        labelWidth: '70',
        element: 'el-select',
        bind: {
            placeholder: '请选择',
            clearable: true,
        },
        slot: {
            element: 'el-option',
            enums: [
                { label: '主库群', value: 2 },
                { label: '备用库群', value: 3 }
            ]
        },
    },
    {
        prop: 'time',
        label: '时间：',
        labelWidth: '40',
        element: 'el-date-picker',
        bind: {
            clearable: true,
            type: 'datetimerange',
            'range-separator': '-',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss',
        },
    },
    {}
];
const tableColumns = [
    {
        prop: 'inputUserId',
        label: '租户ID',
    },
    {
        prop: 'inputUserName',
        label: '租户名称',
    },
    {
        prop: 'startTime',
        label: '开始时间',
    },
    {
        prop: 'endTime',
        label: '结束时间',
    },
    {
        prop: 'nowDatasource',
        label: '所用库',
    },
];


export {
    fields,
    tableColumns,
};