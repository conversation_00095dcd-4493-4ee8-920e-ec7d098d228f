/**
 * @description 地图详情页面的混入文件
 */
export const mapDetailMixin = {
    methods: {
        /**
         * 合并多个时间段的画像数据
         * @param {Array} results - 多个时间段的查询结果
         * @returns {Object} 合并后的数据
         */
        mergePortraitData (results) {
            const mergedData = {
                primaryResult: {
                    sex: [],
                    age: [],
                },
                standbyResult: {
                    sex: [],
                    age: [],
                },
            };

            // 初始化性别和年龄数据结构
            const sexCategories = ['男', '女'];
            const ageCategories = ['18-24', '25-34', '35-44', '45-54', '55以上'];

            // 初始化数据结构
            sexCategories.forEach((category) => {
                mergedData.primaryResult.sex.push({ name: category, nub: 0 });
                mergedData.standbyResult.sex.push({ name: category, nub: 0 });
            });

            ageCategories.forEach((category) => {
                mergedData.primaryResult.age.push({ name: category, nub: 0 });
                mergedData.standbyResult.age.push({ name: category, nub: 0 });
            });

            // 合并数据
            results.forEach((result) => {
                if (!result) return;
                const { primaryResult, standbyResult } = result;

                // 合并性别数据
                primaryResult.sex.forEach((item) => {
                    const index = sexCategories.indexOf(item.name);
                    if (index !== -1) {
                        mergedData.primaryResult.sex[index].nub += item.nub;
                    }
                });
                standbyResult.sex.forEach((item) => {
                    const index = sexCategories.indexOf(item.name);
                    if (index !== -1) {
                        mergedData.standbyResult.sex[index].nub += item.nub;
                    }
                });

                // 合并年龄数据
                primaryResult.age.forEach((item) => {
                    const index = ageCategories.indexOf(item.name);
                    if (index !== -1) {
                        mergedData.primaryResult.age[index].nub += item.nub;
                    }
                });
                standbyResult.age.forEach((item) => {
                    const index = ageCategories.indexOf(item.name);
                    if (index !== -1) {
                        mergedData.standbyResult.age[index].nub += item.nub;
                    }
                });
            });

            return mergedData;
        },

        /**
         * 合并多个时间段的常驻数据
         * @param {Array} results - 多个时间段的查询结果
         * @returns {Object} 合并后的数据
         */
        mergePermanentData (results) {
            const mergedData = {
                primaryResult: {
                    arriveCrowd: [],
                    flowCrowd: [],
                    residentCrowd: [],
                },
                standbyResult: {
                    arriveCrowd: [],
                    flowCrowd: [],
                    residentCrowd: [],
                },
            };

            // 创建时间点映射
            const timeMap = new Map();

            // 收集所有时间点并初始化数据结构
            results.forEach((result) => {
                if (!result) return;
                const { primaryResult } = result;

                ['arriveCrowd', 'flowCrowd', 'residentCrowd'].forEach((type) => {
                    primaryResult[type].forEach((item) => {
                        if (!timeMap.has(item.timeSpan)) {
                            timeMap.set(item.timeSpan, {
                                timeSpan: item.timeSpan,
                                flowTotalCnt: 0,
                                flowInCnt: 0,
                                flowOutCnt: 0,
                                arriveCnt: 0,
                                residentCnt: 0,
                            });
                        }
                    });
                });
            });

            // 按时间排序
            const sortedTimes = Array.from(timeMap.keys()).sort();

            // 初始化结果数组
            sortedTimes.forEach((time) => {
                ['arriveCrowd', 'flowCrowd', 'residentCrowd'].forEach((type) => {
                    mergedData.primaryResult[type].push({ ...timeMap.get(time) });
                    mergedData.standbyResult[type].push({ ...timeMap.get(time) });
                });
            });

            // 合并数据
            results.forEach((result) => {
                if (!result) return;
                const { primaryResult, standbyResult } = result;

                ['arriveCrowd', 'flowCrowd', 'residentCrowd'].forEach((type) => {
                    primaryResult[type].forEach((item) => {
                        const targetIndex = sortedTimes.indexOf(item.timeSpan);
                        if (targetIndex !== -1) {
                            const target = mergedData.primaryResult[type][targetIndex];
                            target.flowTotalCnt += item.flowTotalCnt || 0;
                            target.flowInCnt += item.flowInCnt || 0;
                            target.flowOutCnt += item.flowOutCnt || 0;
                            target.arriveCnt += item.arriveCnt || 0;
                            target.residentCnt += item.residentCnt || 0;
                        }
                    });

                    standbyResult[type].forEach((item) => {
                        const targetIndex = sortedTimes.indexOf(item.timeSpan);
                        if (targetIndex !== -1) {
                            const target = mergedData.standbyResult[type][targetIndex];
                            target.flowTotalCnt += item.flowTotalCnt || 0;
                            target.flowInCnt += item.flowInCnt || 0;
                            target.flowOutCnt += item.flowOutCnt || 0;
                            target.arriveCnt += item.arriveCnt || 0;
                            target.residentCnt += item.residentCnt || 0;
                        }
                    });
                });
            });

            return mergedData;
        },
        setText (grid) {
            const data = grid.data;
            const { primary, standby } = data;
            const isSame = primary === standby;
            const commonStyle = 'color: #1664FF;text-stroke: 1px #FFFFFF;font-weight: bold;';
            grid.addText(
                grid.centerPoint,
                `
                ${isSame ? '' : '<div style="color: red;width: 100px;white-space: normal;font-weight: bold;">该区域主/备库群数据不一致</div>'}
                <div style="${commonStyle}">
                    主库群：${primary}
                </div>
                <div style="${commonStyle}">
                    备用库群：${standby}
                </div>
                            
            `,
                `
                 transform: translate(-50%, -50%);
                 padding: 5px 8px;
                 font-size: 11px;
                 line-height: 18px;
                 white-space: nowrap;
            `
            );
        }
    },
}; 