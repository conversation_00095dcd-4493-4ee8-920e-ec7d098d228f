const fields = (Opts = { msisdn: [] }) => {
    return [
        {
            prop: 'complaintType',
            label: '投诉类型：',
            labelWidth: '70',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '实时', value: 0 },
                    { label: '离线', value: 1 }
                ]
            },
            span: 3,
            rules: [{ required: true, message: '请选择投诉类型', trigger: 'change' }]
        },
        {
            prop: 'phoneNumber',
            label: '号码：',
            labelWidth: '70',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true
            },
            slot: {
                element: 'el-option',
                enums: Opts.msisdn.map((i) => ({ label: i.msisdn, value: i.msisdn }))
            },
            span: 3,
            rules: [{ required: true, message: '请选择号码', trigger: 'change' }]
        },
        {
            prop: 'regionId',
            label: '区域ID：',
            labelWidth: '70',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true
            },
            span: 4,
            rules: [{ required: true, message: '请输入区域ID', trigger: 'blur' }]
        },
        {
            prop: 'startTime',
            label: '开始时间：',
            labelWidth: '70',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss',
                'default-time': "['00:00:00']"
            },
            span: 4,
            rules: [{ required: true, message: '请选择开始时间', trigger: 'blur' }]
        },
        {
            prop: 'endTime',
            label: '结束时间：',
            labelWidth: '70',
            element: 'el-date-picker',
            bind: {
                placeholder: '请选择',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss',
                'default-time': "['23:59:59']"
            },
            span: 4,
            rules: [{ required: true, message: '请选择结束时间', trigger: 'blur' }]
        },
        {
            prop: 'stayDuration',
            label: '驻留时长：',
            labelWidth: '70',
            element: 'el-input-number',
            bind: {
                placeholder: '请输入',
                clearable: true,
                'controls-position': 'right',
                min: 1
            },
            span: 3
        },
        {
            span: 2
        }
    ];
};
const tableColumns = {
    1: [
        {
            prop: 'time',
            label: '信令时间'
        },
        {
            prop: 'baseStationId',
            label: '基站标识'
        },
        {
            prop: 'lng',
            label: '基站经度'
        },
        {
            prop: 'lat',
            label: '基站纬度'
        },
        {
            prop: 'event',
            label: '动作类型'
        }
    ],
    2: [
        {
            prop: 'regionId',
            label: '驻留点ID'
        },
        {
            prop: 'baseStationId',
            label: '基站标识'
        },
        {
            prop: 'lng',
            label: '基站经度'
        },
        {
            prop: 'lat',
            label: '基站纬度'
        },
        {
            prop: 'stayTime',
            label: '驻留时长'
        }
    ],
    3: [
        {
            prop: 'time',
            label: '信令时间'
        },
        {
            prop: 'baseStationId',
            label: '基站标识'
        },
        {
            prop: 'lng',
            label: '基站经度'
        },
        {
            prop: 'lat',
            label: '基站纬度'
        },
        {
            prop: 'distance',
            label: '距离'
        }
    ]
};

export { fields, tableColumns };
