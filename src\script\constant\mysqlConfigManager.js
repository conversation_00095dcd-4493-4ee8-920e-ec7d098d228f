const tableColumns = [
    {
        prop: 'libId',
        label: '库ID',
    },
    {
        prop: 'libName',
        label: '库名称',
    },
    {
        prop: 'libFlag',
        label: '库标识（主或备）',
    },
    {
        prop: 'creator',
        label: '创建人',
    },
    {
        prop: 'createTime',
        label: '创建时间',
    },
    {
        prop: 'lastUpdateTime',
        label: '最后更新时间',
    },
    {
        prop: 'operation',
        label: '操作',
    },
];

const dialogFields = [
    {
        prop: 'libName',
        label: '库名称:',
        element: 'el-input',
        bind: {
            placeholder: '请输入',
            clearable: true,
        },
        span: 24,
    },
    {
        prop: 'libFlag',
        label: '库标识:',
        element: 'el-input',
        bind: {
            placeholder: '请输入',
            clearable: true,
        },
        span: 24,
    },
];

export {
    tableColumns,
    dialogFields,
};