<template>
    <div class="warehouseGroupAlarm">
        <searchBar :fields="formCols" :form="form">
            <template>
                <el-button
                    class="highlyavailable-primary-btn"
                    type="primary"
                    size="small"
                    @click="search()"
                    >查询</el-button
                >
                <div class="showExpand" slot="showExpand" @click="clickExpand">
                    {{ isExpand ? '收起' : '展开' }} &nbsp;<i
                        :class="[isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
                    ></i>
                </div>
            </template>
        </searchBar>
        <dataTable
            class="dataTable"
            :columns="columns"
            :data="tableData"
            :pagination="pagination"
            :total="total"
            stripe
            :updateTable="getTableData"
        >
            <template #fillStatus="{ row }">
                <span
                    class="status-text"
                    :class="`${['yellow', 'red', 'green', 'wine-red'][row.fillStatus]}`"
                >
                    {{ ['无需处理', '未处理', '已处理', '补数失败'][row.fillStatus] }}
                </span>
            </template>
            <template #alarmMetrics="{ row }">
                {{ getWarnIndexList(row.alarmMetrics) }}
            </template>
            <template #datasourceTo="{ row }">
                {{ mapDatasourceFrom[row.datasourceTo] }}
            </template>
            <template #alarmType="{ row }">
                {{ getAlarmType(row.alarmType) }}
            </template>
        </dataTable>
    </div>
</template>

<script>
import searchBar from '_com/searchForm/index.vue';
import dataTable from '_com/tables/dataTableLast.vue';
import { getAreasToCity } from '@/script/utils/method.js';
import {
    fields,
    tableColumns,
    warnIndexOpts,
    alarmTypeOpts
} from '@/script/constant/warehouseGroupAlarm.js';

export default {
    name: 'warehouseGroupAlarm',
    components: {
        searchBar,
        dataTable
    },
    data() {
        return {
            form: {
                userId: '',
                regionName: '',
                regionIdList: '',
                cityCodeList: [],
                layerTypeList: [],
                warnClusterList: [],
                warnIndexList: [],
                warnTypeList: [],
                fillStatusList: [],
                warnTime: [],
                fillTime: []
            },
            columns: tableColumns,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            total: 0,
            isExpand: false,
            mapDatasourceFrom: {
                1: 'ck1',
                2: 'ck2',
                3: 'ck3'
            }
        };
    },
    computed: {
        formCols() {
            const { highDistricts, highLayers } = this.$store.getters;
            return fields(this.isExpand, {
                cityCodeList: getAreasToCity(highDistricts, true),
                layerTypeList: highLayers
            });
        }
    },
    created() {
        this.search();
    },
    methods: {
        search() {
            this.pagination.curPage = 1;
            this.getTableData();
        },
        getTableData(pagination = {}) {
            const { curPage = 1, pageSize = 15 } = pagination;
            const {
                userId,
                regionName,
                regionIdList,
                cityCodeList,
                layerTypeList,
                warnClusterList,
                warnIndexList,
                warnTypeList,
                fillStatusList,
                warnTime,
                fillTime
            } = this.form;
            const [warnStartTime, warnEndTime] = warnTime || [];
            const [fillStartTime, fillEndTime] = fillTime || [];
            const params = {
                userId,
                regionName,
                regionIdList: regionIdList ? [regionIdList] : regionIdList,
                cityCodeList: (cityCodeList || []).filter((city) => !city.includes('all')),
                layerTypeList: layerTypeList || [],
                warnClusterList: warnClusterList || [],
                warnIndexList: warnIndexList || [],
                warnTypeList: warnTypeList || [],
                fillStatusList: fillStatusList || [],
                warnStartTime,
                warnEndTime,
                fillStartTime,
                fillEndTime,
                pageNum: curPage,
                pageSize
            };
            this.highGetPost(
                'monitorApi',
                'warehouseInfo', // 请确认实际接口方法名
                params,
                '仓库集群告警查询'
            ).then(({ data }) => {
                this.tableData = data.list;
                this.total = data.total;
            });
        },
        clickExpand() {
            this.isExpand = !this.isExpand;
        },
        getWarnIndexList(value) {
            const curItem = alarmTypeOpts.find((item) => item.value === value);
            if (curItem) {
                return curItem.label;
            }
        },
        getAlarmType(value) {
            const curItem = warnIndexOpts.find((item) => item.value === value);
            if (curItem) {
                return curItem.label;
            }
        }
    }
};
</script>

<style lang="less" scoped>
.warehouseGroupAlarm {
    width: 100%;
    height: 100%;
    padding: 1.33rem;
    display: flex;
    flex-direction: column;
}
.showExpand {
    font-size: 14px;
    color: #3871b3;
    cursor: pointer;
}
.dataTable {
    width: 100%;
    flex: 1;
    height: 0;

    .status-text {
        color: #00a870;
        background: #e8f8f2;
        border-radius: 3px;
        border: 1px solid #00a870;
        font-size: 12px;
        padding: 3px;
    }

    .red {
        color: #e34d59;
        background: #fdecee;
        border: 1px solid #e34d59;
    }
    .green {
        color: #00a870;
        background: #fdecee;
        border: 1px solid #00a870;
    }
    .yellow {
        color: #ff9e01;
        background: #ffffff;
        border: 1px solid #ff9e01;
    }
    .wine-red {
        color: #581f20;
        background: #fdecee;
        border: 1px solid #581f20;
    }
}
</style>
