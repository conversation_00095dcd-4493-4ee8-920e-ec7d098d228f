<template>
    <div class="highlyavailable">
        <div class="highlyavailable-title">
            <div class="text">{{ title }}</div>
            <el-button
                v-if="tabActive == '1'"
                class="highlyavailable-primary-btn"
                type="primary"
                icon="el-icon-plus"
                @click="addRegion"
                >区域新增</el-button
            >
            <el-button
                v-if="tabActive == '3'"
                class="highlyavailable-primary-btn"
                type="primary"
                icon="el-icon-plus"
                @click="addVersion"
                >版本新增</el-button
            >
             <el-button
                v-if="tabActive == '4'"
                class="highlyavailable-primary-btn"
                type="primary"
                icon="el-icon-plus"
                @click="addckChange"
                >省份新增</el-button
            >
        </div>
        <tab :tabActive.sync="tabActive" :tabList="tabList"></tab>
        <div class="highlyavailable-content">
            <monitor v-if="tabActive == '1'" ref="monitor" />
            <warehouseGroupAlarm v-if="tabActive == '2'" />
            <ganaryTest v-if="tabActive == '3'" ref="ganaryTest"/>
            <ckChange v-if="tabActive == '4'" ref="ckChange"/>
            <recovery v-if="tabActive == '5'" ref="recovery"/>
        </div>
    </div>
</template>
<script>
import tab from '_com/tabs/index.vue';
import monitor from './components/home/<USER>';
import warehouseGroupAlarm from './components/home/<USER>';
import ganaryTest from './components/home/<USER>';
import ckChange from './components/home/<USER>';
import recovery from './components/home/<USER>';
export default {
    components: {
        tab,
        monitor,
        warehouseGroupAlarm,
        ganaryTest,
        ckChange,
        recovery,
    },
    data() {
        return {
            tabActive: '1',
            tabList: [
                { name: '高可用监控', key: '1' },
                { name: '库群告警信息', key: '2' },
                { name: '灰度测试管理', key: '3' },
                { name: 'CK主备库切换管理', key: '4' },
                { name: '一键恢复统计详情', key: '5' },
            ],
        };
    },
    computed: {
        title() {
            return this.tabList.find((item) => item.key == this.tabActive).name;
        },
    },
    mounted() {},
    methods: {
        addRegion() {
            this.$refs.monitor.addRegion();
        },
        addVersion(){
            this.$refs.ganaryTest.addVersion();
        },
        addckChange(){
            this.$refs.ckChange.addCk();
        }
    },
};
</script>
<style lang="less" scoped>
.highlyavailable {
    width: 100%;
    height: 100%;
    background: url('../../../img/background.png') no-repeat center center / 100% 100%;
    padding: 1.94rem 1.78rem 1.33rem 1.78rem;
    display: flex;
    flex-direction: column;
    &-title {
        display: flex;
        justify-content: space-between;
        .text {
            font-size: @font-size-max;
            color: @black-title;
            font-weight: 500;
            line-height: 40px;
        }
    }
    &-content {
        flex: 1;
        width: 100%;
        height: 0;
        background: #ffffff;
        box-shadow: 0px 0px 0.89rem 0px rgba(65, 67, 68, 0.15);
        border-radius: 0px 0.44rem 0.44rem 0.44rem;
    }
}
</style>
<style lang="less">
html {
    font-size: calc(100vw * 18 / 1920);
}
</style>
