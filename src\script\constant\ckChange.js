const fields = (mapOpts = {}) => {
    return [
        {
            prop: 'chName',
            label: '省份:',
            labelWidth: '50px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
            },
            slot: {
                element: 'el-option',
                enums: mapOpts.chName.map(i => ({ label: i.label, value: i.label })),
            },

            span: 3,
        },
        {
            prop: 'provinceCode',
            label: '省编码:',
            labelWidth: '70px',
            element: 'el-input',
            bind: {
                placeholder: '请输入',
                clearable: true,
            },
            span: 3,
        },
        {
            prop: 'primaryDatasource',
            label: '主数据源:',
            labelWidth: '70px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: 'ck1', value: 1 },
                    { label: 'ck2', value: 2 },
                    { label: 'ck3', value: 3 },
                ],
            },
            span: 3,
        },
        {
            prop: 'standbyDatasource',
            label: '备用数据源:',
            labelWidth: '100px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: 'ck1', value: 1 },
                    { label: 'ck2', value: 2 },
                    { label: 'ck3', value: 3 },
                ],
            },
            span: 4,
        },
        {
            prop: 'isValid',
            label: '有效状态:',
            labelWidth: '70px',
            element: 'el-select',
            bind: {
                placeholder: '请选择',
                clearable: true,
            },
            slot: {
                element: 'el-option',
                enums: [
                    { label: '有效', value: 1 },
                    { label: '无效', value: 0 },
                ],
            },
            span: 4,
        },
        {
            prop: 'createTime',
            label: '创建时间:',
            labelWidth: '80px',
            element: 'el-date-picker',
            bind: {
                clearable: true,
                type: 'datetimerange',
                'range-separator': '-',
                'start-placeholder': '开始日期',
                'end-placeholder': '结束日期',
                format: 'yyyy-MM-dd HH:mm:ss',
                'value-format': 'yyyy-MM-dd HH:mm:ss',
            },
            span: 5,
        },
        {
            span: 2
        }
    ];
};

const tableColumns = [
    {
        prop: 'provinceCode',
        label: '省编码',
    },
    {
        prop: 'chName',
        label: '省份名称',
    },
    {
        prop: 'acronym',
        label: '区域首字母名称',
    },
    {
        prop: 'primaryDatasource',
        label: '主数据源',
    },
    {
        prop: 'isValid',
        label: '有效状态',
    },
    {
        prop: 'createTime',
        label: '创建时间',
    },
    {
        prop: 'updateTime',
        label: '修改时间',
    },
    {
        prop: 'operation',
        label: '操作',
        width: '270',
    },
];

const regionTypeList = {
    1: '自定义区域',
    2: '省',
    3: '市',
    4: '区县'
};

const statusList = {
    0: '无效',
    1: '有效',
};

const grayscaleTypeList = {
    1: '租户ID',
    2: '接口链接'
};

const ckDataList = {
    1: 'ck1',
    2: 'ck2',
    3: 'ck3',
};

const startFields = [
    {
        prop: 'startTime',
        label: '开始时间：',
        labelWidth: '90px',
        span: 24,
        rules: [{ required: true, message: '请选择开始时间！', trigger: 'blur' }],
    },
    {
        prop: 'endTime',
        label: '预设结束时间：',
        labelWidth: '108px',
        element: 'el-date-picker',
        bind: {
            clearable: true,
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            'value-format': 'yyyy-MM-dd HH:mm:ss',
        },
        span: 24,
    },
];

export {
    fields,
    tableColumns,
    regionTypeList,
    statusList,
    startFields,
    grayscaleTypeList,
    ckDataList
};