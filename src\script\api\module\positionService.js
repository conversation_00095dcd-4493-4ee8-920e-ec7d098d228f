import norMalConfig from '../normal-config';
const _url = '/default-server';
const positionService = {
    positionServiceApi: {
        // 栅格人流异常告警服务列表查询
        getAlarmList(params) {
            return norMalConfig(`${_url}/fivestage-service/gridFlowAlarm/getAlarmList`, params);
        },
        // 栅格人流异常告警服务详情查询
        getAlarmDetails(params) {
            return norMalConfig(`${_url}/fivestage-service/gridFlowAlarm/getAlarmDetails`, params);
        },
        // 用户面和控制面融合效果监控查询
        getFusionEffect(params) {
            return norMalConfig(`${_url}/fivestage-service/gridFlowAlarm/getFusionEffect`, params);
        },
        // MYSQL的高可用管理服务全量查询
        getAllMYSQLServicePage(params) {
            return norMalConfig(`${_url}/fivestage-service/mysqlSwitchOverImportant/page`, params);
        },
        // MYSQL的高可用管理服务新增
        insertMYSQLService(params) {
            return norMalConfig(
                `${_url}/fivestage-service/mysqlSwitchOverImportant/insert`,
                params
            );
        },
        // MYSQL的高可用管理服务更新
        updateMYSQLService(params) {
            return norMalConfig(
                `${_url}/fivestage-service/mysqlSwitchOverImportant/update`,
                params
            );
        },
        // MYSQL的高可用管理服务删除
        deleteMYSQLService(params) {
            return norMalConfig(
                `${_url}/fivestage-service/mysqlSwitchOverImportant/delete`,
                params
            );
        }
    }
};
export default positionService;
