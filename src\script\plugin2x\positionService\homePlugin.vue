<template>
	<div class="highlyavailable">
		<div class="highlyavailable-title">
			<div class="text">{{ title }}</div>
			<el-button
				v-if="tabActive == '3'"
				class="highlyavailable-primary-btn"
				type="primary"
				icon="el-icon-plus"
				@click="addMySQLConfig"
			>新增</el-button>
		</div>
		<tab
			:tabActive.sync="tabActive"
			:tabList="tabList"
			:tabFit="true"
		></tab>
		<div class="highlyavailable-content">
			<personFlowAbnormal
				v-if="tabActive == '1'"
				ref="personFlowAbnormal"
			/>
			<fusionEffectMonitor
				v-if="tabActive == '2'"
				ref="fusionEffectMonitor"
			/>
			<mysqlConfigManager
				v-if="tabActive == '3'"
				ref="mysqlConfigManager"
			/>
		</div>
	</div>
</template>
<script>
import tab from '_com/tabs/index.vue';
import personFlowAbnormal from './components/home/<USER>';
import fusionEffectMonitor from './components/home/<USER>';
import mysqlConfigManager from './components/home/<USER>';
export default {
	components: {
		tab,
		personFlowAbnormal,
		fusionEffectMonitor,
		mysqlConfigManager,
	},
	data() {
		return {
			tabActive: '1',
			tabList: [
				{ name: '离线基站全域栅格人流异常告警服务', key: '1' },
				{ name: '用户面和控制面融合效果监控', key: '2' },
				{ name: 'mysql高可用配置表管理', key: '3' },
			],
		};
	},
	computed: {
		title() {
			return this.tabList.find((item) => item.key == this.tabActive).name;
		},
	},
	mounted() { },
	methods: {
		addMySQLConfig() {
			this.$refs.mysqlConfigManager.handleAdd();
		},
	},
};
</script>
<style lang="less" scoped>
.highlyavailable {
	width: 100%;
	height: 100%;
	background: url("../../../img/background.png") no-repeat center center /
		100% 100%;
	padding: 1.94rem 1.78rem 1.33rem 1.78rem;
	display: flex;
	flex-direction: column;
	&-title {
		display: flex;
		justify-content: space-between;
		.text {
			font-size: @font-size-max;
			color: @black-title;
			font-weight: 500;
			line-height: 40px;
		}
	}
	&-content {
		flex: 1;
		width: 100%;
		height: 0;
		background: #ffffff;
		box-shadow: 0px 0px 0.89rem 0px rgba(65, 67, 68, 0.15);
		border-radius: 0px 0.44rem 0.44rem 0.44rem;
	}
}
</style>
<style lang="less">
html {
	font-size: calc(100vw * 18 / 1920);
}
</style>
