<template>
	<div class="chart-card">
		<div class="chart-title">{{ title }}</div>
		<div
			v-if="!isEmptyData"
			class="chart-container"
			ref="chartContainer"
		></div>
		<div
			v-else
			class="empty-data-container"
		>
			暂无数据
		</div>
	</div>
</template>

<script>
import * as echarts from 'echarts';

export default {
	name: 'ChartCard',
	props: {
		title: {
			type: String,
			required: true
		},
		chartData: {
			type: Object,
			default: () => ({
				xAxis: [],
				series: []
			})
		},
		chartType: {
			type: String,
			default: 'line'
		},
		chartColor: {
			type: String,
			default: '#5B8FF9'
		},
		yAxisType: {
			type: String,
			default: 'percent',
			validator: function (value) {
				return ['percent', 'value'].indexOf(value) !== -1;
			}
		}
	},
	data() {
		return {
			chart: null,
			emptyData: {
				xAxis: [],
				series: []
			}
		};
	},
	computed: {
		isEmptyData() {
			return !this.chartData ||
				!this.chartData.xAxis ||
				!this.chartData.xAxis.length ||
				!this.chartData.series ||
				!this.chartData.series.length;
		}
	},
	watch: {
		chartData: {
			handler() {
				// 数据变化时，根据数据状态决定初始化或渲染图表
				if (this.isEmptyData) {
					// 如果数据为空且图表已存在，销毁图表
					if (this.chart) {
						this.chart.dispose();
						this.chart = null;
					}
				} else {
					// 如果有数据但图表不存在，初始化图表
					this.$nextTick(() => {
						if (!this.chart && this.$refs.chartContainer) {
							this.chart = echarts.init(this.$refs.chartContainer);
							this.renderChart();
						} else if (this.chart) {
							// 如果图表已存在且有数据，更新图表
							this.renderChart();
						}
					});
				}
			},
			deep: true,
		}
	},
	mounted() {
		this.initChart();
		window.addEventListener('resize', this.resizeChart);
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.resizeChart);
		if (this.chart) {
			this.chart.dispose();
			this.chart = null;
		}
	},
	methods: {
		initChart() {
			// 只有在有数据时才初始化图表
			if (!this.isEmptyData) {
				this.chart = echarts.init(this.$refs.chartContainer);
				this.renderChart();
			}
		},
		renderChart() {
			// 如果数据为空或图表未初始化，不执行渲染
			if (this.isEmptyData || !this.chart) return;

			// 直接使用chartData，因为已经通过isEmptyData确认数据有效
			const chartData = this.chartData;

			const option = {
				tooltip: {
					trigger: 'axis',
					formatter: function (params) {
						// 检查参数是否有效
						if (!params || !params.length) {
							return '';
						}
						const isPercent = this.yAxisType === 'percent';
						const value = params[0].value !== undefined ? (isPercent ? `${params[0].value}%` : params[0].value) : '暂无数据';
						return `<div style="font-size:14px;font-weight:bold;padding:8px 0;color:#333;">${params[0].name}</div>
						<div style="background:rgba(255,255,255,0.9);box-shadow:6px 0px 20px 0px rgba(34,87,188,0.1);border-radius:4px;padding:6px 10px;margin-top:5px;">
							<div style="display:flex;align-items:center;">
								<span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${params[0].color};margin-right:5px;"></span>
								<span style="color:#333;">${params[0].seriesName} ${value}</span>
							</div>
						</div>`;
					}.bind(this),
					extraCssText: 'background:linear-gradient(322deg, #FDFEFF 0%, #F4F7FC 100%); border-radius:6px; border:1px solid; border-image:linear-gradient(337deg, rgba(255,255,255,1), rgba(255,255,255,0)) 1 1; backdrop-filter:blur(3.7px); padding:8px 12px; box-shadow:0 4px 12px rgba(0,0,0,0.05);',
					padding: 0,
					textStyle: {
						color: '#333'
					}
				},
				grid: {
					left: '5%',
					right: '3%',
					bottom: '0',
					top: '5%',
					containLabel: true
				},
				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: chartData.xAxis || [],
					axisLine: {
						lineStyle: {
							color: '#b9babb',
						}
					},
					axisLabel: {
						color: '#86909C',
						margin: 12,
					}
				},
				yAxis: {
					type: 'value',
					min: 0,
					max: this.yAxisType === 'percent' ? 100 : null,
					axisLabel: {
						formatter: function (value) {
							return this.yAxisType === 'percent' ? value + '%' : value;
						}.bind(this),
						color: '#86909C'
					},
					splitLine: {
						lineStyle: {
							color: '#E0E0E0',
							type: 'dashed'
						}
					}
				},
				series: [
					{
						name: this.title,
						type: this.chartType,
						data: chartData.series || [],
						smooth: false,
						showSymbol: false,
						itemStyle: {
							color: this.chartColor
						},
						lineStyle: {
							width: 2,
							color: this.chartColor
						},
						areaStyle: {
							color: {
								type: 'linear',
								x: 0,
								y: 0,
								x2: 0,
								y2: 1,
								colorStops: [
									{
										offset: 0,
										color: this.chartColor + '40' // 40% opacity
									},
									{
										offset: 1,
										color: this.chartColor + '00' // 0% opacity
									}
								]
							}
						},
					}
				]
			};

			this.chart.setOption(option);
		},
		resizeChart() {
			if (this.chart) {
				this.chart.resize();
			}
		}
	}
};
</script>

<style lang="less" scoped>
.chart-card {
	width: 100%;
	height: 100%;
	padding: 16px;
	display: flex;
	flex-direction: column;
	background: #ffffff;
	box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.2);
	border-radius: 2px;

	.chart-title {
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		font-size: 16px;
		color: #1d2129;
		line-height: 24px;
		text-align: left;
		font-style: normal;
		margin-bottom: 10px;
	}

	.chart-container {
		flex: 1;
		width: 100%;
	}

	.empty-data-container {
		flex: 1;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #86909c;
		font-size: 14px;
	}
}
</style>
