/*
 * @Date: 2025-02-11 11:34:01
 * @LastEditors: liurong <EMAIL>
 * @LastEditTime: 2025-07-08 10:31:36
 * @FilePath: \mtex-static-highlyavailable\src\script\api\mtex-service-config.js
 */
import axios from 'axios';
const token = localStorage.getItem('token');
axios.interceptors.request.use((config) => {
    config.headers.token = token;
    return config;
});
import { reqUtil } from 'mtex-rams-core';
const { decodeResult, tryEncodeParam } = reqUtil;
import { decodeResultUser } from '../utils/resUtils';

// post 请求
const mtexFun = function (url, params = {}) {
    let newParams = params;
    newParams = tryEncodeParam(newParams);
    // newParams.webEncodeParam =
    //     newParams.webEncodeParam.match(/(\S*)ENDCODE/)[1];
    return new Promise((resolve, reject) => {
        axios
            .post(url, newParams)
            .then((res) => {
                let newRes = res.data;
                if (res.data.encodeResp) {
                    if (res.config.url === '/mtex/auth/um/queryUserPermissions') {
                        newRes = decodeResultUser(res.data);
                    } else {
                        newRes = decodeResult(res.data);
                    }
                }
                let result = newRes;
                if (result.success == '0') {
                    resolve(result.result);
                    return;
                }
                if (res.headers && res.headers['content-type'] === 'application/force-download') {
                    resolve(result);
                    return;
                }
                reject({
                    success: result.success,
                    errorMessage: result.errorInfo.message,
                    result: result,
                });
                return;
            })
            .catch((err) => {
                reject({
                    success: 111,
                    errorMessage: err.message,
                });
            });
    });
};
export default mtexFun;
