const option = {
  default: ({ legendData = ['主库群', '备用库群'], xData, series, dataZoom, rotate, unit = '人', formatter }) => {
    const isOverFive = xData.length > 5;
    const rate = isOverFive ? ((5 / xData.length || 0) * 100) : 30;
    console.log('rate~5', rate);

    return {
      tooltip: {
        trigger: 'axis',
        textStyle: {
          color: '#000',
          fontSize: 12,
        },
        confine: true,
        backgroundColor: 'rgba(255, 255, 255)', // 设置为半透明黑色背景
        formatter
      },
      legend: {
        data: legendData,
        right: '3%',
        itemWidth: 6,
        itemHeight: 6,
        textStyle: {
          fontSize: 11,
          width: 150,
          overflow: 'break'
        },
      },
      grid: {
        left: 16,
        right: 12,
        top: 40,
        bottom: dataZoom ? 20 : 12,
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: true,
        axisPointer: {
          type: 'shadow',
        },
        axisLine: {
          show: true,
        },
        axisLabel: {
          rotate: rotate || (isOverFive ? 30 : null),
          // interval: 0, // 强制显示所有标签
        },
        data: xData,
      },
      yAxis: {
        type: 'value',
        name: unit,
        nameTextStyle: {
          align: 'right',
        },
      },
      series,
      dataZoom: dataZoom && [
        {
          type: 'slider',
          filterMode: 'none',  // 不影响其他轴
          start: 10,           // 初始显示范围起点
          end: rate,            // 初始显示范围终点（按百分比）
          bottom: 0,         // 滚动条位置下移
          height: 4,         // 更细的滚动条
          handleSize: 0,      // 调整滑块大小
          top: '92%',
          zoomLock: true, // 是否只平移不缩放
          showDetail: false,
          fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
          borderColor: 'rgba(17, 100, 210, 0.12)',
        },
      ],
    };
  },
  pie: ({ data }) => {
    const total = data.reduce((acc, curItem) => acc + curItem.value, 0);
    return {
      tooltip: {
        trigger: 'item',
        formatter: '{b} :{d}%',
      },
      legend: {
        top: '36%',
        right: '15%',
        orient: 'vertical',
        itemWidth: 14,
        itemGap: 30,
        textStyle: {
          color: '#333',
          fontSize: 14,
          rich: {
            a: {
              verticalAlign: 'bottom',
              color: 'rgba(0,0,0,0.65)',
              fontWeight: 400,
              padding: [0, 32, 0, 4]
            },
            count: {
              color: 'rgba(0,0,0,0.90)',
              verticalAlign: 'bottom',
            }
          },
        },
        formatter: (name) => {
          const curItem = data.find((item) => item.name === name);
          const percentage = total ? Number((curItem.value / total) * 100).toFixed(2) : 0;
          return `{a|${name}}{count|${percentage} %}`;
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['68%', '89%'],
          center: ['27%', '49%'], // 控制饼图的位置，使其靠左
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          labelLine: {
            show: false,
          },
          data,
        },
      ],
    };
  },
  xAxisBar: ({ yData, series = [], rotate }) => {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        padding: 10,
        textStyle: {
          color: '#000',
          fontSize: 12,
        },
        confine: true,
        backgroundColor: 'rgba(255,255,255)',
      },
      legend: {
        x: 'right',
        itemWidth: 6,
        itemHeight: 6,
        textStyle: {
          fontSize: 11,
          width: 150,
          overflow: 'break'
        },
      },
      grid: {
        left: 0,
        right: 30,
        top: series.length > 1 ? 25 : 5,
        bottom: 0,
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        axisLine: {
          show: true,
        },
        axisLabel: {
          rotate,
          interval: 0,
        },
      },
      yAxis: {
        type: 'category',
        axisLabel: {
          interval: 0,
        },
        data: yData,
      },
      series,
    };
  },
};

export { option };
